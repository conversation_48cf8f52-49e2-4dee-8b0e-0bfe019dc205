import { defineConfig } from 'vite'
import tailwindcss from '@tailwindcss/vite'
import vue from '@vitejs/plugin-vue'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { VantResolver } from '@vant/auto-import-resolver'
import { resolve } from 'path'
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'

import pkg from './package.json'

// https://vite.dev/config/
export default defineConfig((config) => {
    // console.log(config)
    // console.log(config, process.cwd())
    return {
        server: {
            open: true,
            host: true,
            port: 9999,
            proxy: {
                '/api': {
                    // target: 'https://************:61301/',
                    target: 'https://h5.gpnow.xyz/',
                    changeOrigin: true,
                },
                '/ws': {
                    target: 'wss://h5.gpnow.xyz',
                    changeOrigin: true,
                    ws: true,
                },
            },
        },
        define: {
            'import.meta.env.__APP_VERSION__': JSON.stringify(pkg.version),
        },
        plugins: [
            vue(),
            AutoImport({
                dts: true,
                imports: [
                    'vue',
                    'vue-router',
                    'vue-i18n',
                    'pinia',
                    '@vueuse/core',
                ],
                resolvers: [
                    VantResolver(),
                ],
                dirs: [
                    './src/apis',
                    './src/config',
                    './src/utils',
                    './src/store',
                    './src/hooks',
                    './src/i18n',
                    './src/components',
                ],
            }),
            Components({
                resolvers: [
                    VantResolver(),
                ],
            }),
            createSvgIconsPlugin({
                iconDirs: [
                    resolve(__dirname, 'src/components/Icon/assets'),
                ],
                symbolId: 'icon-[dir]-[name]',
            }),
            tailwindcss(),
        ],
        resolve: {
            alias: {
                '@': resolve(__dirname, 'src'),
            },
        },
    }
})

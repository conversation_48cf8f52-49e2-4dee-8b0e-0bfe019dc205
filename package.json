{"name": "h5", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode development", "build": "vite build", "build:test": "vite build --mode test", "preview": "vite preview"}, "dependencies": {"@tailwindcss/vite": "^4.1.4", "@vueuse/core": "^13.1.0", "@vueuse/integrations": "^13.1.0", "aos": "2.3.4", "axios": "^1.8.4", "countup.js": "^2.8.0", "crypto-js": "^4.2.0", "currency.js": "^2.0.4", "dayjs": "^1.11.13", "echarts": "^5.6.0", "lodash": "^4.17.21", "pinia": "^3.0.2", "pinia-plugin-persistedstate": "^4.2.0", "qrcode": "^1.5.4", "swiper": "^11.2.6", "typed.js": "^2.1.0", "vant": "^4.9.19", "vue": "^3.5.13", "vue-echarts": "^7.0.3", "vue-i18n": "^11.1.3", "vue-router": "^4.5.0", "wujie": "^1.0.28", "wujie-vue3": "^1.0.28"}, "devDependencies": {"@vant/auto-import-resolver": "^1.3.0", "@vitejs/plugin-vue": "^5.2.3", "fast-glob": "^3.3.3", "tailwindcss": "^4.1.4", "unplugin-auto-import": "^19.1.2", "unplugin-vue-components": "^28.5.0", "vite": "^6.3.2", "vite-plugin-svg-icons": "^2.0.1"}}
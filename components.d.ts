/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    Amount: typeof import('./src/components/Amount/index.vue')['default']
    AsyncImage: typeof import('./src/components/AsyncImage/index.vue')['default']
    Avatar: typeof import('./src/components/Avatar/index.vue')['default']
    Card: typeof import('./src/components/Card/index.vue')['default']
    Cell: typeof import('./src/components/Cell/index.vue')['default']
    CellGroup: typeof import('./src/components/Cell/CellGroup.vue')['default']
    Collapse: typeof import('./src/components/Collapse/index.vue')['default']
    Controller: typeof import('./src/components/Controller/Controller.vue')['default']
    CountTo: typeof import('./src/components/CountTo/index.vue')['default']
    DateTime: typeof import('./src/components/Controller/DateTime.vue')['default']
    Description: typeof import('./src/components/Description/index.vue')['default']
    DescriptionGroup: typeof import('./src/components/Description/DescriptionGroup.vue')['default']
    Divider: typeof import('./src/components/Divider/index.vue')['default']
    Echarts: typeof import('./src/components/Echarts/index.vue')['default']
    Filter: typeof import('./src/components/Filter/index.vue')['default']
    Header: typeof import('./src/components/Header/index.vue')['default']
    Icon: typeof import('./src/components/Icon/index.vue')['default']
    ImageBox: typeof import('./src/components/ImageBox/index.vue')['default']
    Input: typeof import('./src/components/Controller/Input.vue')['default']
    Loading: typeof import('./src/components/Loading/index.vue')['default']
    Picker: typeof import('./src/components/Picker/index.vue')['default']
    RateCurrency: typeof import('./src/components/RateCurrency/index.vue')['default']
    Record: typeof import('./src/components/Record/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    Search: typeof import('./src/components/Search/index.vue')['default']
    Select: typeof import('./src/components/Controller/Select.vue')['default']
    Service: typeof import('./src/components/Service/index.vue')['default']
    Submit: typeof import('./src/components/Controller/Submit.vue')['default']
    Table: typeof import('./src/components/Table/index.vue')['default']
    Tip: typeof import('./src/components/Tip/index.vue')['default']
    VanBadge: typeof import('vant/es')['Badge']
    VanButton: typeof import('vant/es')['Button']
    VanCheckbox: typeof import('vant/es')['Checkbox']
    VanConfigProvider: typeof import('vant/es')['ConfigProvider']
    VanEmpty: typeof import('vant/es')['Empty']
    VanFloatingBubble: typeof import('vant/es')['FloatingBubble']
    VanForm: typeof import('vant/es')['Form']
    VanGrid: typeof import('vant/es')['Grid']
    VanGridItem: typeof import('vant/es')['GridItem']
    VanIcon: typeof import('vant/es')['Icon']
    VanImage: typeof import('vant/es')['Image']
    VanList: typeof import('vant/es')['List']
    VanNoticeBar: typeof import('vant/es')['NoticeBar']
    VanPicker: typeof import('vant/es')['Picker']
    VanPopover: typeof import('vant/es')['Popover']
    VanPopup: typeof import('vant/es')['Popup']
    VanPullRefresh: typeof import('vant/es')['PullRefresh']
    VanRadio: typeof import('vant/es')['Radio']
    VanRadioGroup: typeof import('vant/es')['RadioGroup']
    VanSkeleton: typeof import('vant/es')['Skeleton']
    VanSkeletonImage: typeof import('vant/es')['SkeletonImage']
    VanSkeletonParagraph: typeof import('vant/es')['SkeletonParagraph']
    VanSkeletonTitle: typeof import('vant/es')['SkeletonTitle']
    VanStepper: typeof import('vant/es')['Stepper']
    VanSwipe: typeof import('vant/es')['Swipe']
    VanSwipeItem: typeof import('vant/es')['SwipeItem']
    VanTab: typeof import('vant/es')['Tab']
    VanTabs: typeof import('vant/es')['Tabs']
  }
}

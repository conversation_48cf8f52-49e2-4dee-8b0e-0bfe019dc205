import 'package:flutter/material.dart';
import 'package:gp_stock_app/features/account/screens/account_screen.dart';
import 'package:gp_stock_app/features/activity/screens/activity_screen.dart';
import 'package:gp_stock_app/features/home/<USER>/home_screen.dart';
import 'package:gp_stock_app/features/market/market_section_screen.dart';
import 'package:gp_stock_app/features/profile/screens/profile_screen.dart';
import 'package:gp_stock_app/shared/constants/assets.dart';

enum NavigationItem {
  home(
    page: HomeScreen(),
    icon: Assets.homeIcon,
    selectedIcon: Assets.homeIconFilled,
    label: 'home',
  ),
  account(
    page: AccountScreen(),
    icon: Assets.accountIcon,
    selectedIcon: Assets.accountIconFilled,
    label: 'account',
  ),
  trade(
    page: MarketSectionScreen(),
    icon: Assets.tradeIcon,
    selectedIcon: Assets.tradeIconFilled,
    label: 'trade',
  ),
  activity(
    page: ActivityScreen(),
    icon: Assets.activityIcon,
    selectedIcon: Assets.activityIconFilled,
    label: 'activity',
  ),
  profile(
    page: ProfileScreen(),
    icon: Assets.profileIcon,
    selectedIcon: Assets.profileIconFilled,
    label: 'profile',
    showAppBar: false,
  );

  const NavigationItem({
    required this.page,
    required this.icon,
    required this.selectedIcon,
    required this.label,
    this.showAppBar = true,
  });

  final Widget page;
  final String icon;
  final String selectedIcon;
  final String label;
  final bool showAppBar;
}

part of 'main_cubit.dart';

class MainState extends Equatable {
  final NavigationItem selectedNavigationItem;
  final bool showChatFloatWidget;
  const MainState({
    required this.selectedNavigationItem,
    this.showChatFloatWidget = true,
  });

  @override
  List<Object?> get props => [selectedNavigationItem, showChatFloatWidget];

  MainState copyWith({
    NavigationItem? selectedNavigationItem,
    bool? showChatFloatWidget,
  }) {
    return MainState(
      selectedNavigationItem: selectedNavigationItem ?? this.selectedNavigationItem,
      showChatFloatWidget: showChatFloatWidget ?? this.showChatFloatWidget,
    );
  }
}

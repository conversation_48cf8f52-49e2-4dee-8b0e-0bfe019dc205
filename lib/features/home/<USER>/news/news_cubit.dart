import 'package:bloc/bloc.dart';
import 'package:injectable/injectable.dart';

import '../../../../shared/constants/enums.dart';
import '../../domain/repository/home_repository.dart';
import 'news_state.dart';

@injectable
class NewsCubit extends Cubit<NewsState> {
  final HomeRepository _repository;

  NewsCubit(this._repository) : super(const NewsState());

  Future<void> getNews({int page = 1, int size = 20}) async {
    emit(state.copyWith(status: DataStatus.loading));

    final result = await _repository.getNews(page: page, size: size);

    if (result.data != null) {
      emit(state.copyWith(
        status: DataStatus.success,
        newsData: result.data?.data,
      ));
    } else {
      emit(state.copyWith(
        status: DataStatus.failed,
        error: result.error,
      ));
    }
  }
}

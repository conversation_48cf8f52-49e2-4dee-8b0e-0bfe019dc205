import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/features/home/<USER>/models/home_notification/home_notification_model.dart';
import 'package:injectable/injectable.dart';

import '../../domain/repository/home_repository.dart';

@injectable
class HomeNotificationCubit extends Cubit<List<HomeNotificationModel>> {
  final HomeRepository _homeRepository;
  bool _suppressPopup = false;
  bool get shouldSuppressPopup => _suppressPopup;

  HomeNotificationCubit(this._homeRepository) : super([]);

  Future<void> getNotifications({bool suppressPopup = false}) async {
    _suppressPopup = suppressPopup;
    final result = await _homeRepository.getNotificationList();
    if (result.data != null) {
      emit(result.data!);
    }
  }
}

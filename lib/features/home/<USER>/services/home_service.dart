import 'package:collection/collection.dart';
import 'package:dio/dio.dart';
import 'package:gp_stock_app/features/home/<USER>/models/aichatmodel/ai_chat_model.dart';
import 'package:gp_stock_app/features/home/<USER>/models/app_update/app_update.dart';
import 'package:injectable/injectable.dart';
import 'dart:io';

import '../../../../core/api/network/endpoint/api_endpoints.dart';
import '../../../../core/api/network/models/result.dart';
import '../../../../core/api/network/network.dart';
import '../models/banner/banner_response.dart';
import '../models/home_notification/home_notification_model.dart';
import '../models/news/news_model.dart';
import '../repository/home_repository.dart';

@Injectable(as: HomeRepository)
class HomeService implements HomeRepository {
  @override
  Future<ResponseResult<BannerResponse>> getBannerList() async {
    try {
      final Response response = await NetworkProvider().get(
        ApiEndpoints.bannerList,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(
            data: BannerResponse.fromJson(response.data),
          );
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get banner list');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  @override
  Future<ResponseResult<List<HomeNotificationModel>>> getNotificationList() async {
    try {
      final Response response = await NetworkProvider().get(ApiEndpoints.notificationList);
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          final List<dynamic> notificationData = response.data['data'];
          return ResponseResult(
            data: notificationData.map((json) => HomeNotificationModel.fromJson(json)).toList(),
          );
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get notification list');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  @override
  Future<ResponseResult<NewsResponse>> getNews({
    int page = 1,
    int size = 20,
  }) async {
    try {
      final Response response = await NetworkProvider().get(
        ApiEndpoints.news,
        queryParameters: {
          'pageNumber': page,
          'pageSize': size,
        },
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(
            data: NewsResponse.fromJson(response.data),
          );
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get news');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  @override
  Future<ResponseResult<AIChatModel>> sendAIMessage(String question) async {
    try {
      final Response response = await NetworkProvider().post(
        ApiEndpoints.aiChat,
        data: {
          'question': question,
        },
        isAuthRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(
            data: AIChatModel.fromJson(response.data['data']),
          );
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to send AI message');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }


  @override
  Future<ResponseResult<AppUpdate>> getAppUpdate() async {
    try {
      final response = await NetworkProvider().get(
        ApiEndpoints.appUpdate,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          final data = response.data['data'];
          final updates = List<AppUpdate>.from(data.map((e) => AppUpdate.fromJson(e)));

          // 优先级：2 (通用) > 平台特定 (iOS 或 Android)
          AppUpdate? selectedUpdate = updates.firstWhereOrNull((update) => update.type == 2);

          if (selectedUpdate == null) {
            if (Platform.isIOS) {
              selectedUpdate = updates.firstWhereOrNull((update) => update.type == 1);
            } else if (Platform.isAndroid) {
              selectedUpdate = updates.firstWhereOrNull((update) => update.type == 0);
            }
          }

          if (selectedUpdate != null) {
            return ResponseResult(data: selectedUpdate);
          }
          return ResponseResult(error: 'No suitable app update found');
        }
        return ResponseResult(error: response.data['msg']);
      }
      return ResponseResult(error: 'Failed to get app update');
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }
}

part of 'home_cubit.dart';

class HomeState extends Equatable {
  final int selectedTableTab;
  final int selectedNewsTab;
  final DataStatus tableFetchStatus;
  final DataStatus bannerFetchStatus;
  final List<BannerData>? bannerData;
  final String? error;
  final bool isPaginating;
  final int bannerIndex;

  const HomeState({
    this.selectedTableTab = 0,
    this.selectedNewsTab = 0,
    this.tableFetchStatus = DataStatus.idle,
    this.bannerFetchStatus = DataStatus.idle,
    this.bannerData,
    this.error,
    this.isPaginating = false,
    this.bannerIndex = 0,
  });

  @override
  List<Object?> get props => [
        selectedTableTab,
        selectedNewsTab,
        tableFetchStatus,
        isPaginating,
        bannerFetchStatus,
        bannerData,
        error,
        bannerIndex,
      ];

  HomeState copyWith({
    int? selectedTableTab,
    int? selectedNewsTab,
    DataStatus? tableFetchStatus,
    bool? isPaginating,
    DataStatus? bannerFetchStatus,
    List<BannerData>? bannerData,
    String? error,
    int? bannerIndex,
  }) {
    return HomeState(
      selectedTableTab: selectedTableTab ?? this.selectedTableTab,
      selectedNewsTab: selectedNewsTab ?? this.selectedNewsTab,
      tableFetchStatus: tableFetchStatus ?? this.tableFetchStatus,
      isPaginating: isPaginating ?? this.isPaginating,
      bannerFetchStatus: bannerFetchStatus ?? this.bannerFetchStatus,
      bannerData: bannerData ?? this.bannerData,
      error: error ?? this.error,
      bannerIndex: bannerIndex ?? this.bannerIndex,
    );
  }
}

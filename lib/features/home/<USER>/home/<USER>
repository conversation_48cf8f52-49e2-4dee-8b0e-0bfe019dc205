import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:gp_stock_app/core/utils/log.dart';
import 'package:gp_stock_app/features/account/widgets/app_update_dialog.dart';
import 'package:gp_stock_app/features/home/<USER>/models/banner/banner_response.dart';
import 'package:injectable/injectable.dart';
import 'package:package_info_plus/package_info_plus.dart';

import '../../../../shared/constants/enums.dart';
import '../../domain/repository/home_repository.dart';

part 'home_state.dart';

@injectable
class HomeCubit extends Cubit<HomeState> {
  final HomeRepository _homeService;

  HomeCubit(this._homeService) : super(const HomeState());

  void updateTableTab(int tab) {
    emit(state.copyWith(selectedTableTab: tab));
  }

  void updateNewsTab(int tab) {
    emit(state.copyWith(selectedNewsTab: tab));
  }

  void updateBannerIndex(int index) {
    emit(state.copyWith(bannerIndex: index));
  }

  Future<void> getBannerList() async {
    emit(
      state.copyWith(
        bannerFetchStatus: DataStatus.loading,
      ),
    );
    try {
      final result = await _homeService.getBannerList();
      if (result.isSuccess) {
        emit(
          state.copyWith(
            bannerFetchStatus: DataStatus.success,
            bannerData: result.data?.data,
          ),
        );
      } else {
        emit(
          state.copyWith(
            bannerFetchStatus: DataStatus.failed,
            error: result.error,
          ),
        );
      }
    } on Exception catch (e) {
      emit(
        state.copyWith(
          bannerFetchStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
    }
  }

  void getAppUpdate() async {
    final result = await _homeService.getAppUpdate();
    if (result.isSuccess) {
      final model = result.data!;
      logDev("model>>> ${model.toJson()}", "getAppUpdategetAppUpdate");
      bool shouldShowUpdateDialog = await _shouldUpdate(model.version);
      // bool shouldShowUpdateDialog = await _shouldUpdate("1.2.2");
      if (shouldShowUpdateDialog && model.status == 1 && !kDebugMode) {
        AppUpdateDialog(model: model).show();
      }
    }
  }

  static Future<bool> _shouldUpdate(String newVersion) async {
    // 检查 newVersion 格式是否符合 xx.xx.xx
    final versionRegex = RegExp(r'^\d+\.\d+\.\d+$');
    if (!versionRegex.hasMatch(newVersion)) {
      // 如果格式不对，返回 false
      return false;
    }

    // 获取本地应用信息
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    String currentVersion = packageInfo.version;
    print("当前版本号: $currentVersion, 最新版本号: $newVersion");

    // 将版本号按 "." 分割
    List<String> currentParts = currentVersion.split('.');
    List<String> newParts = newVersion.split('.');

    // 将每个部分转换成数字
    int currentMajor = int.parse(currentParts[0]);
    int currentMinor = int.parse(currentParts[1]);
    int currentPatch = int.parse(currentParts[2]);

    int newMajor = int.parse(newParts[0]);
    int newMinor = int.parse(newParts[1]);
    int newPatch = int.parse(newParts[2]);

    // 依次比较主版本号、次版本号和修订号
    if (newMajor > currentMajor) {
      return true;
    } else if (newMajor == currentMajor) {
      if (newMinor > currentMinor) {
        return true;
      } else if (newMinor == currentMinor) {
        if (newPatch > currentPatch) {
          return true;
        }
      }
    }
    // 如果新版本号不大于当前版本号，返回false
    return false;
  }
}

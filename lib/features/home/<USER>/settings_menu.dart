import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/main/logic/main/main_cubit.dart';
import 'package:gp_stock_app/shared/routes/routes.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:gp_stock_app/shared/theme/my_color_scheme.dart';

import '../../../shared/app/extension/helper.dart';
import '../../../shared/constants/assets.dart';
import '../../../shared/constants/enums.dart';
import '../../../shared/logic/sort_color/sort_color_cubit.dart';
import '../../../shared/logic/theme/theme_cubit.dart';
import '../../notifications/logic/notifications/notifications_cubit.dart';

class SettingsMenu extends StatelessWidget {
  const SettingsMenu({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 160.gw,
      decoration: BoxDecoration(
        color: myColorScheme(context).cardColor,
        borderRadius: BorderRadius.circular(12.gr),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withNewOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          BlocSelector<NotificationsCubit, NotificationsState, int?>(
            selector: (state) => state.notificationCount,
            builder: (context, notificationCount) {
              return _MenuItem(
                icon: Assets.notificationIcon,
                title: 'myNotifications'.tr(),
                badge: notificationCount?.toString(),
                onTap: () {
                  Navigator.pop(context);
                  Navigator.pushNamed(context, routeNotificationList).then((value) {
                    getIt<NotificationsCubit>().getNotificationCount();
                  });
                },
              );
            },
          ),
          _Divider(),
          _MenuItem(
            icon: Assets.languageIcon,
            title: 'language'.tr(),
            onTap: () => _showSubMenu(
              context: context,
              child: LanguageOptions(),
            ),
          ),
          _Divider(),
          _MenuItem(
            icon: Assets.themeIcon,
            title: 'theme'.tr(),
            onTap: () => _showSubMenu(
              context: context,
              child: _ThemeOptions(),
            ),
          ),
          _Divider(),
          _MenuItem(
            icon: Assets.stockIcon,
            title: 'priceColor'.tr(),
            onTap: () => _showSubMenu(
              context: context,
              child: _PriceColorOptions(),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _showSubMenu({
    required BuildContext context,
    required Widget child,
  }) async {
    final RenderBox button = context.findRenderObject() as RenderBox;
    final buttonPosition = button.localToGlobal(Offset.zero);

    await showMenu(
      context: context,
      position: RelativeRect.fromLTRB(
        buttonPosition.dx,
        buttonPosition.dy + 80.gh,
        buttonPosition.dx - 1.gh,
        0,
      ),
      elevation: 0,
      color: Colors.transparent,
      constraints: BoxConstraints(maxWidth: 120.gw),
      items: [
        PopupMenuItem(
          enabled: false,
          padding: EdgeInsets.zero,
          child: Container(
            width: 160.gw,
            decoration: BoxDecoration(
              color: myColorScheme(context).cardColor,
              borderRadius: BorderRadius.circular(12.gr),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withNewOpacity(0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: child,
          ),
        ),
      ],
    );
  }
}

class _ThemeOptions extends StatelessWidget {
  const _ThemeOptions();

  @override
  Widget build(BuildContext context) {
    return BlocSelector<ThemeCubit, ThemeState, ThemeMode>(
      selector: (state) => state.themeMode,
      builder: (context, themeMode) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _MenuItem(
              icon: Assets.settingsIcon,
              title: 'systemTheme'.tr(),
              onTap: () {
                context.read<ThemeCubit>().changeTheme(ThemeMode.system);
                Navigator.pop(context);
              },
              value: themeMode == ThemeMode.system ? '✓' : null,
            ),
            _Divider(),
            _MenuItem(
              icon: Assets.themeIcon,
              title: 'lightTheme'.tr(),
              onTap: () {
                context.read<ThemeCubit>().changeTheme(ThemeMode.light);
                Navigator.pop(context);
              },
              value: themeMode == ThemeMode.light ? '✓' : null,
            ),
            _Divider(),
            _MenuItem(
              icon: Assets.darkThemeIcon,
              title: 'darkTheme'.tr(),
              onTap: () {
                context.read<ThemeCubit>().changeTheme(ThemeMode.dark);
                Navigator.pop(context);
              },
              value: themeMode == ThemeMode.dark ? '✓' : null,
            ),
          ],
        );
      },
    );
  }
}

class LanguageOptions extends StatelessWidget {
  const LanguageOptions({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        for (final locale in context.supportedLocales) ...[
          _MenuItem(
            icon: Helper().getLanguageIcon(locale),
            title: Helper().getLanguageName(locale),
            onTap: () async {
              HapticFeedback.lightImpact();
              try {
                if (context.supportedLocales.contains(locale)) {
                  await context.setLocale(locale);
                  if (context.mounted) {
                    context.read<MainCubit>().sendLocale(locale);
                    Navigator.pop(context);
                  }
                }
              } catch (e) {
                debugPrint('Error setting locale: $e');
              }
            },
            value: context.locale == locale ? '✓' : null,
          ),
          if (locale != context.supportedLocales.last) _Divider(),
        ],
      ],
    );
  }
}

class _PriceColorOptions extends StatelessWidget {
  const _PriceColorOptions();

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<SortColorCubit, SortColorState>(
      builder: (context, state) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _MenuItem(
              icon: Assets.greenDownIcon,
              title: 'redUpGreenDown'.tr(),
              onTap: () {
                context.read<SortColorCubit>().toggleMarketColor(
                      MarketColor.redUpGreenDown,
                    );
                Navigator.pop(context);
              },
              value: state.marketColor == MarketColor.redUpGreenDown ? '✓' : null,
            ),
            _Divider(),
            _MenuItem(
              icon: Assets.redDownIcon,
              title: 'greenUpRedDown'.tr(),
              onTap: () {
                context.read<SortColorCubit>().toggleMarketColor(
                      MarketColor.greenUpRedDown,
                    );
                Navigator.pop(context);
              },
              value: state.marketColor == MarketColor.greenUpRedDown ? '✓' : null,
            ),
          ],
        );
      },
    );
  }
}

class _MenuItem extends StatelessWidget {
  final String title, icon;
  final String? badge, value;
  final VoidCallback onTap;

  const _MenuItem({
    required this.icon,
    required this.title,
    required this.onTap,
    this.badge,
    this.value,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 10.gw, vertical: 12.gh),
        child: Row(
          children: [
            SvgPicture.asset(
              icon,
              fit: BoxFit.scaleDown,
              width: 20.gw,
              height: 20.gh,
            ),
            12.horizontalSpace,
            Expanded(
              child: Text(
                title,
                style: FontPalette.medium14.copyWith(
                  color: myColorScheme(context).titleColor,
                ),
              ),
            ),
            if (value != null) ...[
              Text(
                value!,
                style: FontPalette.normal12.copyWith(
                  color: myColorScheme(context).subTitleColor,
                ),
              ),
            ],
            if (badge != null) ...[
              Container(
                padding: EdgeInsets.symmetric(horizontal: 6.gw, vertical: 2.gh),
                decoration: BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.circular(10.gr),
                ),
                child: Text(
                  badge!,
                  style: FontPalette.normal12.copyWith(
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

class _Divider extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Divider(
      height: 1,
      color: Colors.grey.withNewOpacity(0.1),
    );
  }
}

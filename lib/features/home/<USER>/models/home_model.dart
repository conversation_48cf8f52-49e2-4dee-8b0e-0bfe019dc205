//!sample model for news  -- delete this after api is ready
///TODO: delete this after api is ready
class NewsModel {
  final int id;
  final String title;
  final String coverUrl;
  final String createTime;
  final String? source;

  NewsModel({
    required this.id,
    required this.title,
    required this.coverUrl,
    required this.createTime,
    this.source,
  });

  factory NewsModel.fromJson(Map<String, dynamic> json) {
    return NewsModel(
      id: json['id'],
      title: json['title'],
      coverUrl: json['coverUrl'],
      createTime: json['createTime'],
      source: json['source'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'coverUrl': coverUrl,
      'createTime': createTime,
      'source': source,
    };
  }
}

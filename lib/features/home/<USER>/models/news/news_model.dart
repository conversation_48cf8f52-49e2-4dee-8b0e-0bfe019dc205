// news_model.dart
import 'package:freezed_annotation/freezed_annotation.dart';

part 'news_model.freezed.dart';
part 'news_model.g.dart';

@freezed
class NewsResponse with _$NewsResponse {
  const factory NewsResponse({
    @Default(0) int code,
    required NewsData data,
    @Default('') String msg,
  }) = _NewsResponse;

  factory NewsResponse.fromJson(Map<String, dynamic> json) => _$NewsResponseFromJson(json);
}

@freezed
class NewsData with _$NewsData {
  const factory NewsData({
    @Default([]) List<NewsItem> records,
    @Default(0) int total,
    @Default(0) int size,
    @Default(0) int current,
    @Default(true) bool searchCount,
    @Default(true) bool optimizeCountSql,
    @Default([]) List<dynamic> orders,
    @Default('') String countId,
    @Default(0) int maxLimit,
    @Default(0) int pages,
  }) = _NewsData;

  factory NewsData.fromJson(Map<String, dynamic> json) => _$NewsDataFromJson(json);
}

@freezed
class NewsItem with _$NewsItem {
  const factory NewsItem({
    required int id,
    @Default('') String title,
    @Default('') String content,
    @Default('') String digest,
    @Default('') String url,
    @Default('') String comefrom,
    @Default('') String publishTime,
  }) = _NewsItem;

  factory NewsItem.fromJson(Map<String, dynamic> json) => _$NewsItemFromJson(json);
}

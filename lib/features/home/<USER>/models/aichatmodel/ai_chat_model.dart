import 'package:freezed_annotation/freezed_annotation.dart';

part 'ai_chat_model.freezed.dart';
part 'ai_chat_model.g.dart';

@freezed
class AIChatModel with _$AIChatModel {
  const factory AIChatModel({
    @<PERSON><PERSON><PERSON><PERSON>(name: 'createTime') required String createTime,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'creator') required String creator,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'id') required int id,
    @<PERSON>son<PERSON>ey(name: 'question') required String question,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'reply') required String reply,
    @<PERSON>son<PERSON><PERSON>(name: 'siteId') required int siteId,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'updateTime') required String updateTime,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'updater') required String updater,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'userId') required int userId,
  }) = _AIChatModel;

  factory AIChatModel.fromJson(Map<String, dynamic> json) => _$AIChatModelFromJson(json);
}

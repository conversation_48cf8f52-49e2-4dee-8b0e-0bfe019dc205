import 'dart:math';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/main/domain/enums/navigation_item.dart';
import 'package:gp_stock_app/features/main/logic/main/main_cubit.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';

import '../../../shared/constants/enums.dart';
import '../../../shared/theme/font_pallette.dart';
import '../../../shared/theme/my_color_scheme.dart';
import '../../../shared/widgets/market_table/market_table_row.dart';
import '../../../shared/widgets/sort_header.dart';
import '../../market/domain/models/stock_table_response.dart';
import '../../market/logic/market/market_cubit.dart';
import '../../market/widgets/market_table_header.dart';

class MarketDataTable extends StatefulWidget {
  final int? limit;
  final bool isHome;
  const MarketDataTable({super.key, this.limit, this.isHome = false});

  @override
  State<MarketDataTable> createState() => _MarketDataTableState();
}

class _MarketDataTableState extends State<MarketDataTable> {
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildMarketTabs(),
        12.verticalSpace,
        _buildTableContainer(),
      ],
    );
  }

  Widget _buildMarketTabs() {
    return BlocSelector<MarketCubit, MarketState, (MarketType?, TodaysTab)>(
      selector: (state) => (state.selectedMarketTableType, state.selectedTodaysTab),
      builder: (context, state) {
        final List<Widget> rowChildren = [];

        // Add market tabs
        if (widget.isHome) {
          rowChildren.addAll(_buildTabsForMarketHome(state.$2));
        } else {
          rowChildren.addAll(_buildTabsForMarket(state.$1 ?? MarketType.shenzhen, state.$2));
        }

        // Create the animated tabs section
        final animatedTabs = AnimationLimiter(
          child: Row(
            children: AnimationConfiguration.toStaggeredList(
              duration: const Duration(milliseconds: 300),
              childAnimationBuilder: (widget) => SlideAnimation(
                horizontalOffset: 50.0,
                child: FadeInAnimation(
                  child: widget,
                ),
              ),
              children: rowChildren,
            ),
          ),
        );

        // If on home screen, return a Row with animated tabs and the more button
        if (widget.isHome) {
          return Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: animatedTabs,
              ),
              TextButton.icon(
                icon: const Icon(Icons.chevron_right_outlined),
                iconAlignment: IconAlignment.end,
                onPressed: () => context.read<MainCubit>().selectedNavigationItem(NavigationItem.trade),
                label: Text("more".tr()),
                style: TextButton.styleFrom(
                  foregroundColor: myColorScheme(context).textColor2,
                  iconColor: myColorScheme(context).textColor2,
                  padding: EdgeInsets.symmetric(horizontal: 4.gh, vertical: 4.gh),
                  minimumSize: const Size(0, 0),
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
              ),
            ],
          );
        }

        // If not on home screen, just return the animated tabs
        return animatedTabs;
      },
    );
  }

  List<Widget> _buildTabsForMarket(MarketType selectedType, TodaysTab selectedTab) {
    switch (selectedTab) {
      case TodaysTab.aShares:
        return _buildASharesTabs(selectedType);
      case TodaysTab.hkShares:
        return _buildHKSharesTabs(selectedType);
      case TodaysTab.usShares:
        return _buildUSSharesTabs(selectedType);
    }
  }

  List<Widget> _buildTabsForMarketHome(TodaysTab selectedTab) {
    final tabs = [
      (title: 'a_shares', tab: TodaysTab.aShares),
      (title: 'hk_shares', tab: TodaysTab.hkShares),
      (title: 'us_shares', tab: TodaysTab.usShares),
    ];

    return tabs
        .map((tabInfo) => Padding(
              padding: EdgeInsets.only(right: 15.gr),
              child: MarketTableHeader(
                title: tabInfo.title.tr(),
                isSelected: selectedTab == tabInfo.tab,
                onTap: () => context.read<MarketCubit>().updateTodaysTab(tabInfo.tab, isHome: true),
              ),
            ))
        .toList();
  }

  List<Widget> _buildASharesTabs(MarketType selectedType) => [
        _buildMarketTab('shenzhen_stock_exchange', MarketType.shenzhen, selectedType),
        15.horizontalSpace,
        _buildMarketTab('shanghai_stock_exchange', MarketType.shanghai, selectedType),
        15.horizontalSpace,
        _buildMarketTab('star_market', MarketType.starMarket, selectedType),
        15.horizontalSpace,
        _buildMarketTab('gem_board', MarketType.gem, selectedType),
      ];

  List<Widget> _buildHKSharesTabs(MarketType selectedType) => [
        _buildMarketTab('main_board', MarketType.main, selectedType),
        15.horizontalSpace,
        _buildMarketTab('gen_board', MarketType.gen, selectedType),
      ];

  List<Widget> _buildUSSharesTabs(MarketType selectedType) => [
        _buildMarketTab('china_market', MarketType.china, selectedType),
        15.horizontalSpace,
        _buildMarketTab('star_market', MarketType.star, selectedType),
      ];

  Widget _buildMarketTab(String label, MarketType type, MarketType selectedType) {
    return MarketTableHeader(
      title: label.tr(),
      isSelected: selectedType == type,
      onTap: () => context.read<MarketCubit>().updateMarketTableType(type),
    );
  }

  Widget _buildTableContainer() {
    return Container(
      decoration: BoxDecoration(
        color: myColorScheme(context).cardColor,
        borderRadius: BorderRadius.circular(8.gr),
        boxShadow: [
          BoxShadow(
            color: myColorScheme(context).shadowColor,
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(18.gr),
        child: Column(
          children: [
            _buildTableHeader(),
            10.verticalSpace,
            Divider(color: myColorScheme(context).dividerColor),
            _buildTableContent(),
          ],
        ),
      ),
    );
  }

  Widget _buildTableHeader() {
    return BlocBuilder<MarketCubit, MarketState>(
      builder: (context, state) {
        return Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              flex: 3,
              child: Text(
                'name'.tr(),
                style: _headerTextStyle,
              ),
            ),
            Expanded(
              flex: 3,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  SortHeader(
                    title: 'currentPrice'.tr(),
                    sortType: state.sortByPriceAsc,
                    onTap: context.read<MarketCubit>().handleSortByPrice,
                  ),
                  SortHeader(
                    title: 'rise_fall'.tr(),
                    sortType: state.sortByChangeAsc,
                    onTap: context.read<MarketCubit>().handleSortByChange,
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildTableContent() {
    return BlocSelector<MarketCubit, MarketState, (DataStatus, StockTableData?, bool)>(
      selector: (state) => (
        state.tableFetchStatus,
        state.tableData?.data,
        state.isPaginating,
      ),
      builder: (context, data) {
        final (status, items, isPaginating) = data;

        if (status == DataStatus.loading && !isPaginating) {
          return _buildLoadingList();
        }

        if (items?.list?.isEmpty ?? true) {
          return _buildEmptyState();
        }

        return ListView.builder(
          physics: const NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          itemCount: _calculateItemCount(items!),
          itemBuilder: (context, index) => MarketTableRow(data: items.list![index], tabType: TradeTabType.Quotes),
        );
      },
    );
  }

  int _calculateItemCount(StockTableData items) =>
      widget.limit != null ? min(items.list!.length, widget.limit!) : items.list!.length;

  Widget _buildLoadingList() {
    return Column(
      children: List.generate(
        6,
        (_) => Padding(
          padding: EdgeInsets.only(bottom: 8.gh),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8.gr),
            child: ShimmerWidget(
              height: 45.gh,
              width: double.infinity,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Text(
        'no_data_available'.tr(),
        style: FontPalette.medium14.copyWith(
          color: myColorScheme(context).subTitleColor,
        ),
      ),
    );
  }

  TextStyle get _headerTextStyle => FontPalette.medium14.copyWith(
        color: myColorScheme(context).subTitleColor,
      );
}

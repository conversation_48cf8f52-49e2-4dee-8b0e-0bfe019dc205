import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';

import '../../../../shared/theme/my_color_scheme.dart';
import '../../domain/models/home_notification/home_notification_model.dart';

class EventDetailsScreen extends StatelessWidget {
  final HomeNotificationModel event;

  const EventDetailsScreen({
    super.key,
    required this.event,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(event.title),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 12.gw),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Image
              if (event.imageUrl.isNotEmpty)
                Hero(
                  tag: event.imageUrl,
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8.gr),
                    child: CachedNetworkImage(
                      imageUrl: event.imageUrl,
                      width: double.infinity,
                      height: 126.gh,
                      fit: BoxFit.cover,
                      errorWidget: (context, url, error) => Container(
                        width: double.infinity,
                        height: 200.gh,
                        decoration: BoxDecoration(
                          color: Colors.grey[200],
                          borderRadius: BorderRadius.circular(8.gr),
                        ),
                        child: Icon(
                          Icons.image_not_supported,
                          size: 40.gw,
                          color: Colors.grey[400],
                        ),
                      ),
                      placeholder: (context, url) => Center(
                        child: CircularProgressIndicator.adaptive(),
                      ),
                    ),
                  ),
                ),
              8.verticalSpace,
              // Description (HTML Content)
              Html(
                data: event.content,
                style: {
                  "body": Style(
                    fontSize: FontSize(14),
                    color: myColorScheme(context).textLabelColor,
                    lineHeight: LineHeight(1.5),
                  ),
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}

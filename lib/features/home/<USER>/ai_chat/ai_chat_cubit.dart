import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/features/home/<USER>/repository/home_repository.dart';
import 'package:gp_stock_app/features/home/<USER>/ai_chat/ai_chat_state.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

class AIChatCubit extends Cubit<AIChatState> {
  final homeService = getIt<HomeRepository>();
  AIChatCubit() : super(const AIChatState());

  Future<void> sendMessage(String message) async {
    emit(state.copyWith(aiChatStatus: DataStatus.loading));
    try {
      final response = await homeService.sendAIMessage(message);
      if (response.isSuccess && response.data != null) {
        emit(state.copyWith(
          aiChatStatus: DataStatus.success,
          aiChatData: [
            ...state.aiChatData,
            response.data!,
          ],
        ));
      } else {
        emit(state.copyWith(aiChatStatus: DataStatus.failed, aiChatError: response.error));
      }
    } catch (e) {
      emit(state.copyWith(aiChatStatus: DataStatus.failed, aiChatError: e.toString()));
    }
  }
}

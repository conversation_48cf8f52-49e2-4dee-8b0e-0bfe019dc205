import 'package:gp_stock_app/core/api/network/models/result.dart';
import 'package:gp_stock_app/features/home/<USER>/models/app_update/app_update.dart';

import '../models/aichatmodel/ai_chat_model.dart';
import '../models/banner/banner_response.dart';
import '../models/home_notification/home_notification_model.dart';
import '../models/news/news_model.dart';

abstract class HomeRepository {
  const HomeRepository();

  Future<ResponseResult<BannerResponse>> getBannerList();
  Future<ResponseResult<List<HomeNotificationModel>>> getNotificationList();
  Future<ResponseResult<NewsResponse>> getNews({int page, int size});
  Future<ResponseResult<AIChatModel>> sendAIMessage(String question);

  /// 获取app更新信息
  Future<ResponseResult<AppUpdate>> getAppUpdate();
}

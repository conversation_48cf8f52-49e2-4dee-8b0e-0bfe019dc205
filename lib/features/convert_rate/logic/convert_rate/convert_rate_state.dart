import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../../shared/constants/enums.dart';
import '../../domain/models/convert_rate_model.dart';

part 'convert_rate_state.freezed.dart';

@freezed
class ConvertRateState with _$ConvertRateState {
  const factory ConvertRateState({
    @Default(DataStatus.idle) DataStatus status,
    List<ConvertRate>? rates,
    String? error,
    @Default("1") String customAmount,
    @Default("CNY") String selectedCurrency,
    @Default(false) bool isEditingAmount,
    @Default(false) bool isCalculating,
    double? convertedUSD,
    double? convertedCNY,
    double? convertedHKD,
  }) = _ConvertRateState;
}
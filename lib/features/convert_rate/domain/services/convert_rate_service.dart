import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';

import '../../../../core/api/network/endpoint/api_endpoints.dart';
import '../../../../core/api/network/models/result.dart';
import '../../../../core/api/network/network.dart';
import '../models/convert_rate_model.dart';
import '../repository/convert_rate_repository.dart';

@Injectable(as: ConvertRateRepository)
class ConvertRateService implements ConvertRateRepository {
  @override
  Future<ResponseResult<List<ConvertRate>>> getConvertRates() async {
    try {
      final response = await NetworkProvider().get(
        ApiEndpoints.exchangeRate,
        isAuthRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          final List<dynamic> data = response.data['data'] ?? [];
          return ResponseResult(
            data: data.map((e) => ConvertRate.fromJson(e)).toList(),
          );
        }
        return ResponseResult(error: response.data['msg']);
      }
      return ResponseResult(error: 'Failed to get exchange rates');
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }
}

import 'package:bloc/bloc.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/features/forgot/domain/repository/forgot_repository.dart';
import 'package:injectable/injectable.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'forgot_state.dart';

@injectable
class ForgotCubit extends Cubit<ForgotState> {
  final ForgotRepository _repository;

  ForgotCubit(this._repository) : super(const ForgotState());

  Future<bool> resetPassword({
    required String mobile,
    required String password,
    required String smsCode,
  }) async {
    emit(state.copyWith(status: DataStatus.loading));

    final base64Password = password.toBase64();

    final result = await _repository.resetPassword(
      mobile: mobile,
      newPassword: base64Password,
      smsCode: smsCode,
      verifyType: "mobile",
      passwordType: PasswordType.account,
    );

    if (result.data != null) {
      emit(state.copyWith(status: DataStatus.success, error: null));
      return true;
    } else {
      emit(state.copyWith(status: DataStatus.failed, error: result.error));
      return false;
    }
  }
}

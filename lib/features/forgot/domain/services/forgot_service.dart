import 'package:dio/dio.dart';
import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:injectable/injectable.dart';

import '../../../../core/api/network/models/result.dart';
import '../../../../core/api/network/network.dart';
import '../../../../shared/constants/enums.dart';
import '../repository/forgot_repository.dart';

@Injectable(as: ForgotRepository)
class ForgotService implements ForgotRepository {
  @override
  Future<ResponseResult<bool>> resetPassword({
    required String mobile,
    required String newPassword,
    required String smsCode,
    required String verifyType,
    required PasswordType passwordType,
  }) async {
    try {
      final Map<String, dynamic> requestData = {
        'mobile': mobile,
        'newPassword': newPassword,
        'smsCode': smsCode,
        'verifyType': verifyType,
        'type': passwordType.value,
      };

      final Response response = await NetworkProvider().post(
        ApiEndpoints.changePassword,
        data: requestData,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(data: true);
        }
        return ResponseResult(error: response.data['msg']);
      }
      return ResponseResult(error: 'Failed to reset password');
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }
}

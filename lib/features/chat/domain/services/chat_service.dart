import 'package:dio/dio.dart';
import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/api/network/models/result.dart';
import 'package:gp_stock_app/core/api/network/network.dart';
import 'package:gp_stock_app/features/chat/domain/models/chat_config.dart';
import 'package:gp_stock_app/features/chat/domain/models/services_conversation/chat_services_conversation.dart';
import 'package:injectable/injectable.dart';

import '../repository/chat_repository.dart';

@singleton
class ChatService implements ChatRepository {
  @override
  Future<ResponseResult<ChatConfig>> getUserSig() async {
    try {
      final Response response = await NetworkProvider().post(
        ApiEndpoints.userSig,
        isAuthRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          final ChatConfig chatConfig = ChatConfig.fromJson(response.data['data'] ?? {});
          return ResponseResult(data: chatConfig);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get user signature');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.message ?? 'Unknown error occurred');
    }
  }

  @override
  Future<ResponseResult<ChatServicesConversation>> getChatServiceAccount() async {
    try {
      final Response response = await NetworkProvider().get(
        ApiEndpoints.getChatServiceAccount,
        isAuthRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(data: ChatServicesConversation.fromJson(response.data["data"]));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get chat service account');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.message ?? 'Unknown error occurred');
    }
  }
}

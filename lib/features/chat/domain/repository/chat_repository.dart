import 'package:gp_stock_app/core/api/network/models/result.dart';
import 'package:gp_stock_app/features/chat/domain/models/chat_config.dart';
import 'package:gp_stock_app/features/chat/domain/models/services_conversation/chat_services_conversation.dart';

abstract class ChatRepository {
  const ChatRepository();

  /// 获取腾讯sdk 配置
  Future<ResponseResult<ChatConfig>> getUserSig();

  /// 获取聊天客服账号
  Future<ResponseResult<ChatServicesConversation>> getChatServiceAccount();
}

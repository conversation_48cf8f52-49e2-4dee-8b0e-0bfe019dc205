import 'package:freezed_annotation/freezed_annotation.dart';

part 'chat_config.freezed.dart';
part 'chat_config.g.dart';

@freezed
class ChatConfig with _$ChatConfig {
  const factory ChatConfig({
    @Default('') String imImage,
    @Default('') String nickName,
    @Default('') String sdkAppId,
    @Default('') String userId,
    @Default('') String userSig,
  }) = _ChatConfig;

  factory ChatConfig.fromJson(Map<String, dynamic> json) => _$ChatConfigFromJson(json);
}

import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';

V2TimConversation? getConversation({
  String? userID,
  String? groupID,
  String? name,
  String? faceUrl,
}) {
  if (groupID != null) {
    return V2TimConversation(
      conversationID: 'group_$groupID',
      type: 2,
      groupID: groupID,
      showName: name,
      faceUrl: faceUrl,
    );
  }
  if (userID != null) {
    return V2TimConversation(
      conversationID: 'c2c_$userID',
      type: 1,
      userID: userID,
      showName: name,
      faceUrl: faceUrl,
    );
  }
  return null;
}

Iterable<CustomStickerPackage> getStickers(List<CustomEmojiFaceData> defaultCustomEmojiStickerList) {
  final defaultEmojiList = defaultCustomEmojiStickerList
      .map((customEmojiPackage) {
        return CustomStickerPackage(
          name: customEmojiPackage.name,
          baseUrl: "assets/custom_face_resource/${customEmojiPackage.name}",
          isEmoji: customEmojiPackage.isEmoji,
          isDefaultEmoji: true,
          stickerList: customEmojiPackage.list
              .asMap()
              .keys
              .map(
                (idx) => CustomSticker(
                  index: idx,
                  name: customEmojiPackage.list[idx],
                ),
              )
              .toList(),
          menuItem: CustomSticker(
            index: 0,
            name: customEmojiPackage.icon,
          ),
        );
      })
      .toList()
      .reversed;

  return defaultEmojiList;
}

Widget renderCustomStickerPanel({
  sendTextMessage,
  sendFaceMessage,
  deleteText,
  addCustomEmojiText,
  addText,
  List<CustomEmojiFaceData> defaultCustomEmojiStickerList = const [],
  double? height,
  double? width,
}) {
  return StickerPanel(
    sendTextMsg: sendTextMessage,
    sendFaceMsg: (index, data) => sendFaceMessage(index + 1, (data.split("/")[3]).split("@")[0]),
    deleteText: deleteText,
    addText: addText,
    addCustomEmojiText: addCustomEmojiText,
    customStickerPackageList: [
      ...getStickers(defaultCustomEmojiStickerList),
    ],
  );
}

extension ConversationExtension on String {
  String get maskString => (length <= 2) ? '*' * length : this[0] + '*' * (length - 2) + this[length - 1];
  bool get hasSquareBrackets => startsWith('[') && endsWith(']');
  String get removeSquareBrackets => (hasSquareBrackets ? substring(1, length - 1) : this);
}

extension StringExtension on String? {
  bool get notNullNorEmpty => this != null && this!.isNotEmpty;
  Color get colorFromUserId {
    if (this == null || this!.isEmpty) {
      return Colors.grey[200]!;
    }

    // Hash the userId into an integer value using XOR for better distribution
    int hash = this!.codeUnits.fold(0, (prev, element) => (prev * 31) ^ element);

    // Generate RGB values based on the hash
    int red = (hash & 0xFF0000) >> 16; // Extract the red component
    int green = (hash & 0x00FF00) >> 8; // Extract the green component
    int blue = (hash & 0x0000FF); // Extract the blue component

    return Color.fromARGB(255, red, green, blue);
  }

  LinearGradient? get gradientFromUserId {
    if (this == null || this!.isEmpty) {
      return LinearGradient(
        colors: [Colors.grey[200]!, Colors.grey[400]!],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      );
    }

    // Hash the userId into an integer value
    int hash = this!.codeUnits.fold(0, (prev, element) => (prev * 31) ^ element);

    // Generate RGB values for the start color
    int red1 = (hash & 0xFF0000) >> 16;
    int green1 = (hash & 0x00FF00) >> 8;
    int blue1 = (hash & 0x0000FF);

    // Create a secondary color as a shade of the first color
    // Darken by reducing RGB values by a percentage (e.g., 20%)
    double shadeFactor = 0.8; // Use 0.8 for darker shade or >1.0 for lighter
    int red2 = (red1 * shadeFactor).clamp(0, 255).toInt();
    int green2 = (green1 * shadeFactor).clamp(0, 255).toInt();
    int blue2 = (blue1 * shadeFactor).clamp(0, 255).toInt();

    return LinearGradient(
      colors: [
        Color.fromARGB(255, red1, green1, blue1),
        Color.fromARGB(255, red2, green2, blue2),
      ],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    );
  }
}

extension ConversationChatExtension on V2TimConversation {
  V2TimFriendInfo? get friendInfo {
    final decodedCustomData = customData.notNullNorEmpty ? jsonDecode(customData!) : null;
    if (decodedCustomData != null && (decodedCustomData as Map).containsKey('userID')) {
      return V2TimFriendInfo.fromJson(decodedCustomData);
    }
    return null;
  }

  int? get messagingFrequency {
    final decodedCustomData = customData.notNullNorEmpty ? jsonDecode(customData!) : null;
    if (decodedCustomData != null && (decodedCustomData as Map).containsKey('frequency')) {
      return int.tryParse(decodedCustomData['frequency'] as String);
    }
    return null;
  }
}

extension GroupChatExtension on V2TimGroupInfo {
  int? get messagingFrequency {
    if (customInfo != null && customInfo!.isNotEmpty && customInfo!.containsKey('frequency')) {
      return int.tryParse(customInfo!['frequency'] as String);
    }
    return null;
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_lucide/flutter_lucide.dart';
import 'package:gp_stock_app/shared/theme/my_color_scheme.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';

class UserInfoCard extends StatelessWidget {
  const UserInfoCard({
    super.key,
    this.userInfo,
  });

  final V2TimUserFullInfo? userInfo;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: userInfo?.faceUrl == null
                  ? LinearGradient(
                      colors: [
                        myColorScheme(context).primaryColor.withValues(alpha: 0.6),
                        myColorScheme(context).primaryColor,
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    )
                  : null,
              image: userInfo?.faceUrl != null
                  ? DecorationImage(
                      image: NetworkImage(userInfo!.faceUrl!),
                      fit: BoxFit.cover,
                    )
                  : null,
            ),
            child: userInfo?.faceUrl == null ? const Icon(LucideIcons.user, size: 30, color: Colors.white) : null,
          ),
          const SizedBox(width: 16),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                userInfo?.userID ?? "",
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              if (userInfo?.nickName != null)
                Text(
                  userInfo!.nickName!,
                ),
            ],
          ),
        ],
      ),
    );
  }
}

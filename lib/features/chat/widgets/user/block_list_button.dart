import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_lucide/flutter_lucide.dart';
import 'package:gp_stock_app/features/chat/screens/block_list_screen.dart';

class BlockListButton extends StatelessWidget {
  const BlockListButton({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const BlockListScreen(),
          ),
        );
      },
      child: Container(
        padding: const EdgeInsets.fromLTRB(20, 12, 20, 12),
        child: Row(
          children: [
            Icon(
              LucideIcons.users_round,
              size: 16,
              color: Colors.black87,
            ),
            SizedBox(width: 6),
            Text(
              "chat_blocked_contacts".tr(),
              style: TextStyle(
                color: Colors.black87,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

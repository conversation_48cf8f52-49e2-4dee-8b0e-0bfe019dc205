import 'package:flutter/material.dart';

class CMMentor extends StatelessWidget {
  const CMMentor({
    super.key,
    required this.id,
  });

  final int? id;

  @override
  Widget build(BuildContext context) {
    // TODO
    return Container();

    //   final mentorListFetchStatus = context.watch<SmartInvestmentCubit>().state.mentorListFetchStatus;

    //   if (mentorListFetchStatus == DataStatus.idle) {
    //     context.read<SmartInvestmentCubit>().getMentorList();
    //   }

    //   if (mentorListFetchStatus == DataStatus.loading) {
    //     return const CircularProgressIndicator();
    //   }

    //   if (mentorListFetchStatus == DataStatus.failed) {
    //     return Container(
    //       child: const Text(
    //         'Failed to load mentors',
    //         style: TextStyle(
    //           color: Colors.grey,
    //           fontStyle: FontStyle.italic,
    //           fontWeight: FontWeight.w300,
    //           fontSize: 10,
    //         ),
    //       ),
    //     );
    //   }

    //   final mentorList = context.read<SmartInvestmentCubit>().state.mentorListData?.data?.list;
    //   final mentor = mentorList?.firstWhere((mentor) => mentor.id == id);

    //   if (mentor == null) {
    //     return Container(
    //       child: const Text(
    //         'You don\'t have access to this mentor',
    //         style: TextStyle(
    //           color: Colors.grey,
    //           fontStyle: FontStyle.italic,
    //           fontWeight: FontWeight.w300,
    //           fontSize: 10,
    //         ),
    //       ),
    //     );
    //   }

    //   return MentorCard(
    //       mentor: mentor,
    //       onFollowTap: () {
    //         Navigator.pushNamed(
    //           context,
    //           routeSmartInvestmentScreen,
    //           arguments: {'id': id},
    //         );
    //       });
  }
}

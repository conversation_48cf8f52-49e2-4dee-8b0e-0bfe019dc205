import 'package:flutter/material.dart';
import 'package:gp_stock_app/features/chat/widgets/chat/last_message.dart';
import 'package:gp_stock_app/features/chat/widgets/chat/unread_count.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';

class ChatTileSub extends StatelessWidget {
  const ChatTileSub({super.key, this.data});

  final V2TimConversation? data;

  @override
  Widget build(BuildContext context) {
    final unreadCount = data?.unreadCount?.toString();

    return Padding(
      padding: const EdgeInsets.only(top: 2),
      child: Row(
        children: [
          Expanded(
            flex: 5,
            child: LastMessage(lastMessage: data?.lastMessage),
          ),
          Expanded(
            flex: 1,
            child: UnreadCount(unreadCount: unreadCount),
          ),
        ],
      ),
    );
  }
}

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/features/chat/utils/utils.dart';
import 'package:gp_stock_app/features/chat/widgets/chat/dp.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:gp_stock_app/shared/theme/my_color_scheme.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';

class GroupMembers extends StatelessWidget {
  const GroupMembers({
    super.key,
    required this.members,
  });
  final List<V2TimGroupMemberFullInfo?> members;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
      color: myColorScheme(context).backgroundColor,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                'members'.tr(),
                style: FontPalette.semiBold16,
              ),
              const Spacer(),
              Text(
                '${members.length}',
                style: FontPalette.medium16.copyWith(color: Colors.black.withValues(alpha: 0.3)),
              ),
            ],
          ),
          ListView.separated(
            itemCount: members.length,
            physics: const NeverScrollableScrollPhysics(),
            shrinkWrap: true,
            padding: const EdgeInsets.only(top: 10, left: 0),
            itemBuilder: (context, index) {
              return InkWell(
                onTap: () {},
                child: Row(
                  children: [
                    Dp(
                      faceUrl: members[index]?.faceUrl,
                      type: 1,
                      userId: members[index]?.userID,
                      name: members[index]?.nickName,
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Text(
                        (members[index]?.nickName ?? '').maskString,
                        style: FontPalette.normal15,
                      ),
                    ),
                  ],
                ),
              );
            },
            separatorBuilder: (context, index) => const SizedBox(height: 10),
          ),
        ],
      ),
    );
  }
}

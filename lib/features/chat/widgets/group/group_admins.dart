import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/features/chat/utils/utils.dart';
import 'package:gp_stock_app/features/chat/widgets/chat/dp.dart';
import 'package:gp_stock_app/shared/theme/color_pallette.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:gp_stock_app/shared/theme/my_color_scheme.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';

class GroupAdmins extends StatelessWidget {
  const GroupAdmins({
    super.key,
    required this.admins,
  });
  final List<V2TimGroupMemberFullInfo?> admins;
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
      color: myColorScheme(context).backgroundColor,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: 10,
        children: [
          Text(
            'admins'.tr(),
            style: FontPalette.semiBold16,
          ),
          SizedBox(
            height: 110,
            child: ListView.separated(
              scrollDirection: Axis.horizontal,
              itemBuilder: (context, index) => Column(
                spacing: 4,
                children: [
                  Stack(
                    alignment: Alignment.center,
                    children: [
                      CircleAvatar(
                        radius: 32,
                        backgroundColor: myColorScheme(context).primaryColor,
                      ),
                      CircleAvatar(
                        radius: 30,
                        backgroundColor: ColorPalette.whiteColor,
                      ),
                      Dp(
                        type: 1,
                        faceUrl: admins[index]?.faceUrl,
                        userId: admins[index]?.userID,
                        name: admins[index]?.nickName,
                      ),
                      // admins[index]?.faceUrl == null
                      //     ? SvgPicture.asset(
                      //         Assets.userEmptyAvatar,
                      //         alignment: Alignment.center,
                      //         fit: BoxFit.fill,
                      //       )
                      //     : ClipOval(
                      //         child: CachedNetworkImage(
                      //           imageUrl: admins[index]?.faceUrl ?? '',
                      //           fit: BoxFit.cover,
                      //           height: 54,
                      //           width: 54,
                      //           errorListener: (value) => logDev(value, 'dp', error: true),
                      //           errorWidget: (context, url, error) => const Icon(
                      //             LucideIcons.users,
                      //             color: Colors.white,
                      //             size: 24,
                      //           ),
                      //         ),
                      //       ),
                    ],
                  ),
                  Text((admins[index]?.userID ?? '').maskString),
                  if (admins[index]?.role == GroupMemberRoleType.V2TIM_GROUP_MEMBER_ROLE_OWNER)
                    Text(
                      'owner'.tr(),
                      style: TextStyle(
                        color: Colors.black.withValues(alpha: 0.5),
                        fontSize: 10,
                      ),
                    ),
                ],
              ),
              itemCount: admins.length,
              separatorBuilder: (context, index) => const SizedBox(width: 10),
            ),
          ),
        ],
      ),
    );
  }
}

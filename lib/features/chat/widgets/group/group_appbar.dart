import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/features/chat/widgets/chat/dp.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';
import 'package:gp_stock_app/shared/theme/color_pallette.dart';
import 'package:gp_stock_app/shared/widgets/buttons/custom_material_button.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';

class GroupAppbar extends StatelessWidget implements PreferredSizeWidget {
  const GroupAppbar({
    super.key,
    required this.data,
  });
  final V2TimGroupInfo data;
  @override
  Widget build(BuildContext context) {
    Future<void> exitGroup() async {
      final response = await TencentImSDKPlugin.v2TIMManager.quitGroup(groupID: data.groupID);
      if (response.code == 0 && context.mounted) {
        GPEasyLoading.showToast('leftGroupSuccess'.tr());
        Navigator.pop(context);
        Navigator.pop(context);
        Navigator.pop(context);
      }
    }

    return AppBar(
      backgroundColor: Colors.white,
      surfaceTintColor: Colors.white,
      title: Row(
        spacing: 10,
        children: [
          SizedBox(
              width: 54,
              height: 54,
              child: Dp(
                type: 2,
                faceUrl: data.faceUrl,
                userId: data.groupID,
                name: data.groupName,
              )),
          Text(data.groupName ?? ''),
        ],
      ),
      actions: [
        IconButton(
          onPressed: () {
            showDialog(
              context: context,
              builder: (context2) {
                return AlertDialog(
                  title: Text('confirmLeaveGroup'.tr()),
                  actions: [
                    TextButton(
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      child: Text('cancel'.tr(), style: TextStyle(fontSize: 16)),
                    ),
                    CustomMaterialButton(
                      width: 108,
                      height: 40,
                      buttonText: 'leave'.tr(),
                      onPressed: () {
                        exitGroup();
                      },
                    ),
                  ],
                );
              },
            );
          },
          icon: Icon(
            Icons.exit_to_app_outlined,
            color: ColorPalette.redColor,
          ),
        ),
        const SizedBox(width: 16),
      ],
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(70);
}

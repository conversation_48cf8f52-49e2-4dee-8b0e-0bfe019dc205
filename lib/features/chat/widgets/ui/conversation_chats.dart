import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/api/network/network_helper.dart';
import 'package:gp_stock_app/features/chat/utils/enums.dart';
import 'package:gp_stock_app/features/chat/widgets/chat/chat_tile.dart';
import 'package:gp_stock_app/features/chat/widgets/ui/empty_chat.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';

class ConversationChats extends StatelessWidget {
  const ConversationChats({super.key, this.type, this.onMembersCountChange, this.onGroupsCountChange});

  final ChatScreenType? type;
  final Function(int count)? onMembersCountChange;
  final Function(int count)? onGroupsCountChange;

  @override
  Widget build(BuildContext context) {
    return TIMUIKitConversation(
      isShowOnlineStatus: false,
      locale: NetworkHelper.currentLocale,
      emptyBuilder: () => const EmptyChat(),
      onMembersCountChange: onMembersCountChange,
      onGroupsCountChange: onGroupsCountChange,
      itemBuilder: (conversationItem, [onlineStatus]) {
        if (type == ChatScreenType.channels && conversationItem.type == 1) {
          return Container();
        }
        if (type == ChatScreenType.users && conversationItem.type == 2) {
          return Container();
        }
        return ChatTile(data: conversationItem);
      },
    );
  }
}

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bounceable/flutter_bounceable.dart';

class EmptyChat extends StatelessWidget {
  const EmptyChat({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      child: Padding(
        padding: const EdgeInsets.only(top: 80),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Bounceable(
              onTap: () {},
              child: Container(
                padding: const EdgeInsets.all(0),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.01),
                  borderRadius: BorderRadius.circular(16),
                ),
                // TODO
                child: const Icon(Icons.chat_bubble_outline, size: 90, color: Colors.black),
                // child: Image.asset(
                //   Assets.gradChat,
                //   width: 90,
                //   height: 90,
                // ),
              ),
            ),
            Padding(
              padding: EdgeInsets.only(top: 8),
              child: SizedBox(
                width: 160,
                child: Text(
                  "startConnecting".tr(),
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

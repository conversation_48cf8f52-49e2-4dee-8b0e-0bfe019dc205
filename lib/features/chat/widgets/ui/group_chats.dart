import 'package:flutter/material.dart';
import 'package:gp_stock_app/features/chat/utils/utils.dart';
import 'package:gp_stock_app/features/chat/widgets/chat/special_tile.dart';
import 'package:gp_stock_app/features/chat/widgets/ui/empty_chat.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';

class GroupChats extends StatelessWidget {
  const GroupChats({super.key, this.onGroupCountChanged});
  final Function(int)? onGroupCountChanged;
  @override
  Widget build(BuildContext context) {
    return TIMUIKitGroup(
      itemBuilder: (context, V2TimGroupInfo groupInfo) {
        final messagingFrequency = groupInfo.messagingFrequency;
        return SpecialTile(
          groupId: groupInfo.groupID,
          name: groupInfo.groupName,
          url: groupInfo.faceUrl,
          messagingFrequency: messagingFrequency,
        );
      },
      emptyBuilder: (v) => const EmptyChat(),
      onGroupCountChanged: onGroupCountChanged,
    );
  }
}

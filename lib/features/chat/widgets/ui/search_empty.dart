import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bounceable/flutter_bounceable.dart';

class SearchEmpty extends StatelessWidget {
  const SearchEmpty({super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      child: Padding(
        padding: const EdgeInsets.only(top: 80),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Bounceable(
              onTap: () {},
              child: Container(
                padding: const EdgeInsets.all(0),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.01),
                  borderRadius: BorderRadius.circular(16),
                ),
                // TODO
                child: Icon(Icons.search, size: 169, color: Colors.black),
                // child: SvgPicture.asset(
                //   Assets.emptySearch,
                //   width: 169,
                //   height: 169,
                // ),
              ),
            ),
            Padding(
              padding: EdgeInsets.only(top: 8),
              child: SizedBox(
                child: Text(
                  "noResults".tr(),
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

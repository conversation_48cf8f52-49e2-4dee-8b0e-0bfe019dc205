// import 'package:flutter/material.dart';
// import 'package:flutter_bounceable/flutter_bounceable.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:gp_stock_app/core/constants/assets.dart';
// import 'package:gp_stock_app/v2/features/chat/screens/add_contact_screen.dart';

// class EmptyContacts extends StatelessWidget {
//   const EmptyContacts({
//     super.key,
//   });

//   @override
//   Widget build(BuildContext context) {
//     return SizedBox(
//       width: 1.sw,
//       child: Padding(
//         padding: const EdgeInsets.only(top: 80),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.center,
//           mainAxisAlignment: MainAxisAlignment.start,
//           children: [
//             Bounceable(
//               onTap: () {
//                 showModalBottomSheet(
//                   context: context,
//                   isScrollControlled: true,
//                   builder: (ctx) => const FractionallySizedBox(
//                     heightFactor: 0.8,
//                     child: AddContactScreen(),
//                   ),
//                 );
//               },
//               child: Container(
//                 padding: const EdgeInsets.all(0),
//                 decoration: BoxDecoration(
//                   color: Colors.black.withValues(alpha:0.01),
//                   borderRadius: BorderRadius.circular(16),
//                 ),
//                 child: Image.asset(
//                   Assets.gradContacts,
//                   width: 90,
//                   height: 90,
//                 ),
//               ),
//             ),
//             const Padding(
//               padding: EdgeInsets.only(top: 8),
//               child: SizedBox(
//                 width: 140,
//                 child: Text(
//                   "Add your contacts and start connecting!",
//                   textAlign: TextAlign.center,
//                   style: TextStyle(fontSize: 12),
//                 ),
//               ),
//             )
//           ],
//         ),
//       ),
//     );
//   }
// }

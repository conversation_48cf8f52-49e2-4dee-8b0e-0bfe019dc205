import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/core/utils/log.dart';
import 'package:gp_stock_app/core/utils/tencent_utils.dart';
import 'package:gp_stock_app/features/chat/domain/models/chat_config.dart';
import 'package:gp_stock_app/features/chat/domain/models/services_conversation/chat_services_conversation.dart';
import 'package:gp_stock_app/features/chat/domain/services/chat_service.dart';
import 'package:gp_stock_app/features/profile/logic/auth_aware_cubit.dart';
import 'package:gp_stock_app/features/sign_in/domain/models/login/login_response.dart';
import 'package:gp_stock_app/features/sign_in/domain/repository/sign_in_repository.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:injectable/injectable.dart';

part 'chat_state.dart';

@singleton
class ChatCubit extends AuthAwareCubit<ChatState> {
  final ChatService _chatService;

  ChatCubit(SignInRepository signInRepo, this._chatService) : super(signInRepo, const ChatState());

  @override
  void onLoggedIn(LoginResponse loginResponse) => initChats();

  @override
  void onLoggedOut() => logout();

  void initChats() async {
    emit(state.copyWith(initialLoading: DataStatus.loading, tencentConnectStatus: DataStatus.loading));
    try {
      final chatConfig = await _chatService.getUserSig();
      final sdkappid = chatConfig.data?.sdkAppId;
      final userid = chatConfig.data?.userId;
      final usersig = chatConfig.data?.userSig;
      logDev(chatConfig.toString(), 'chatConfig', special: true);

      if (sdkappid == null || userid == null || usersig == null) {
        throw Exception("INCORRECT_VALUES");
      }
      void onLoginSuccess() {
        emit(state.copyWith(isTencentInitialized: true, tencentConnectStatus: DataStatus.success));
      }

      void onConnectFailed(code, error) {
        logDev(error, 'onConnectFailed', error: true);
        emit(state.copyWith(tencentConnectStatus: DataStatus.failed));
      }

      TencentIMUtils.initTencent(
        sdkappid: int.parse(sdkappid),
        userid: userid,
        usersig: usersig,
        onLoginSuccess: () {
          emit(state.copyWith(initialLoading: DataStatus.success, tencentConnectStatus: DataStatus.success));
        },
        onConnectFailed: (code, error) {
          logDev(error, 'onConnectFailed', error: true);
          TencentIMUtils.initTencent(
            sdkappid: int.parse(sdkappid),
            userid: userid,
            usersig: usersig,
            onLoginSuccess: onLoginSuccess,
            onConnectFailed: onConnectFailed,
          );
        },
      );
    } catch (e) {
      emit(state.copyWith(initialLoading: DataStatus.failed));
      logDev(e, 'initChats', error: true);
    }
  }

  Future<ChatServicesConversation?> getChatServicesAccount() async {
    GPEasyLoading.showLoading();
    try {
      final res = await _chatService.getChatServiceAccount();
      return res.data;
    } finally {
      GPEasyLoading.dismiss();
    }
  }

  void logout() async {
    emit(state.copyWith(initialLoading: DataStatus.loading));
    try {
      TencentIMUtils.logoutTencent(
        onLogoutSuccess: () {
          emit(
            state.copyWith(
              initialLoading: DataStatus.success,
              isTencentInitialized: false,
              tencentConnectStatus: DataStatus.idle,
            ),
          );
        },
      );
    } catch (e) {
      emit(state.copyWith(initialLoading: DataStatus.failed));
      logDev(e, 'logout', error: true);
    }
  }
}

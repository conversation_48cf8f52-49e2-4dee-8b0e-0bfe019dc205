part of 'chat_cubit.dart';

class ChatState extends Equatable {
  const ChatState({
    this.initialLoading = DataStatus.idle,
    this.isTencentInitialized = false,
    this.tencentConnectStatus = DataStatus.idle,
    this.chatConfig = const ChatConfig(),
  });

  final DataStatus initialLoading;
  final bool isTencentInitialized;
  final DataStatus tencentConnectStatus;
  final ChatConfig chatConfig;
  @override
  List<Object?> get props => [
        initialLoading,
        isTencentInitialized,
        tencentConnectStatus,
        chatConfig,
      ];

  ChatState copyWith({
    DataStatus? initialLoading,
    bool? isTencentInitialized,
    DataStatus? tencentConnectStatus,
    ChatConfig? chatConfig,
  }) {
    return ChatState(
      initialLoading: initialLoading ?? this.initialLoading,
      isTencentInitialized: isTencentInitialized ?? this.isTencentInitialized,
      tencentConnectStatus: tencentConnectStatus ?? this.tencentConnectStatus,
      chatConfig: chatConfig ?? this.chatConfig,
    );
  }
}

import 'dart:developer';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/features/chat/screens/chat_screen.dart';
import 'package:gp_stock_app/features/chat/widgets/ui/empty_chat.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';

import '../utils/utils.dart';
import '../widgets/ui/search_empty.dart';

class SearchScreen extends StatelessWidget {
  const SearchScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('chat_search'.tr()),
      ),
      body: TIMUIKitSearch(
        tIMUIKitSearchIndicatorChild: const EmptyChat(),
        noResultWidget: const SearchEmpty(),
        onTapConversation: (V2TimConversation v2TimConversation, V2TimMessage? v2TimMessage) {
          log(v2TimConversation.userID.toString());
          log(v2TimConversation.groupID.toString());
          log(v2TimConversation.type.toString());

          if (v2TimConversation.type == 1) {
            Navigator.push<dynamic>(
              context,
              MaterialPageRoute(
                builder: (context) => ChatScreen(
                  selectedConversation: getConversation(
                    userID: v2TimConversation.userID,
                    name: v2TimConversation.showName,
                  ),
                ),
              ),
            );
          }
          if (v2TimConversation.type == 2) {
            Navigator.push<dynamic>(
              context,
              MaterialPageRoute(
                builder: (context) => ChatScreen(
                  selectedConversation: getConversation(
                    groupID: v2TimConversation.groupID,
                    name: v2TimConversation.showName,
                  ),
                ),
              ),
            );
          }
        },
      ),
    );
  }
}

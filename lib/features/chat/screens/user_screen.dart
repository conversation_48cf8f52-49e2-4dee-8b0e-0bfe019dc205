import 'package:flutter/material.dart';
import 'package:gp_stock_app/shared/widgets/error/shared_error.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';
import 'package:tencent_cloud_chat_uikit/ui/views/TIMUIKitProfile/profile_widget.dart';
import 'package:tencent_cloud_chat_uikit/ui/views/TIMUIKitProfile/tim_uikit_profile.dart';

import '../widgets/user/block_list_button.dart';
import '../widgets/user/user_info_card.dart' show UserInfoCard;

class UserScreen extends StatefulWidget {
  const UserScreen({super.key, this.userID, this.isSelf});
  final String? userID;
  final bool? isSelf;

  @override
  UserScreenState createState() => UserScreenState();
}

class UserScreenState extends State<UserScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(),
      body: SafeArea(
        child: Builder(
          builder: (context) {
            final isSelf = widget.isSelf ?? false;
            final userID = widget.userID;
            if (userID == null) return const SharedError();

            return TIMUIKitProfile(
              userID: userID,
              profileWidgetsOrder: isSelf
                  ? [
                      ProfileWidgetEnum.userInfoCard,
                      ProfileWidgetEnum.operationDivider,
                      ProfileWidgetEnum.customBuilderOne,
                      ProfileWidgetEnum.operationDivider,
                    ]
                  : [
                      ProfileWidgetEnum.userInfoCard,
                      ProfileWidgetEnum.operationDivider,
                      ProfileWidgetEnum.addToBlockListBar,
                      ProfileWidgetEnum.operationDivider,
                      ProfileWidgetEnum.addAndDeleteArea,
                    ],
              profileWidgetBuilder: ProfileWidgetBuilder(
                userInfoCard: (userInfo) => UserInfoCard(
                  userInfo: userInfo,
                ),
                customBuilderOne: (isFriend, friendInfo, conversation) => const BlockListButton(),
              ),
            );
          },
        ),
      ),
    );
  }
}

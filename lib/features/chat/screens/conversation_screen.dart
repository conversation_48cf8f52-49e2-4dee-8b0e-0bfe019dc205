import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_lucide/flutter_lucide.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/chat/domain/models/services_conversation/chat_services_conversation.dart';
import 'package:gp_stock_app/features/chat/logic/chat/chat_cubit.dart';
import 'package:gp_stock_app/features/chat/screens/search_screen.dart';
import 'package:gp_stock_app/features/chat/utils/enums.dart';
import 'package:gp_stock_app/features/chat/utils/theme.dart';
import 'package:gp_stock_app/features/chat/utils/utils.dart';
import 'package:gp_stock_app/features/chat/widgets/ui/chat_type_tabs.dart';
import 'package:gp_stock_app/features/chat/widgets/ui/conversation_chats.dart';
import 'package:gp_stock_app/features/chat/widgets/ui/group_chats.dart';
import 'package:gp_stock_app/features/chat/widgets/ui/users_chats.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/loading/shared_loading.dart';
import 'package:gp_stock_app/shared/mixins/chat_button_mixin.dart';
import 'package:gp_stock_app/shared/widgets/error/shared_error.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';

import 'chat_screen.dart';

class ConversationScreen extends StatefulWidget {
  final ChatServicesConversation? conversation;

  const ConversationScreen({super.key, this.conversation});

  @override
  State<ConversationScreen> createState() => _ConversationScreenState();
}

class _ConversationScreenState extends State<ConversationScreen> with HideFloatButtonRouteAwareMixin {
  CoreServicesImpl timCoreInstance = TIMUIKitCore.getInstance();
  int groupCount = 0;
  int userCount = 0;

  int selectedIndex = 0;
  final tabs = [
    ChatScreenType.all,
    ChatScreenType.channels,
    ChatScreenType.users,
  ];

  bool _showConnected = false;
  Timer? _timer;

  @override
  void initState() {
    super.initState();

    _navigateToConversation();
  }

  void _navigateToConversation() async {
    final model = widget.conversation;
    if (model != null) {
      final conversation = getConversation(userID: model.pImAccount, name: model.pNickname);
      if (conversation == null) return;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Navigator.push<dynamic>(
          context,
          MaterialPageRoute(
            builder: (context) => ChatScreen(selectedConversation: conversation),
          ),
        );
      });
    }
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return TUIChatTheme(
      customTheme: Theme.of(context).brightness == Brightness.dark ? ChatTheme.darkChatTheme : ChatTheme.lightChatTheme,
      child: DefaultTabController(
        length: tabs.length,
        child: Scaffold(
          appBar: AppBar(
            surfaceTintColor: Colors.transparent,
            backgroundColor: Theme.of(context).brightness == Brightness.dark ? Colors.transparent : Colors.white,
            automaticallyImplyLeading: false,
            leading: const BackButton(),
            title: Row(
              spacing: 10.gw,
              children: [
                Text(
                  'chat_chats'.tr(),
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 16.gsp,
                    // color: const TUITheme().myChatColor,
                  ),
                ),
                BlocConsumer<ChatCubit, ChatState>(listener: (context, state) {
                  if (state.tencentConnectStatus == DataStatus.success) {
                    setState(() => _showConnected = true);
                    _timer?.cancel();
                    _timer = Timer(const Duration(seconds: 2), () {
                      if (mounted) {
                        setState(() => _showConnected = false);
                      }
                    });
                  }
                }, builder: (context, state) {
                  final value = state.tencentConnectStatus;
                  return AnimatedSlide(
                    duration: const Duration(milliseconds: 300),
                    offset: value == DataStatus.loading || (value == DataStatus.success && _showConnected)
                        ? Offset.zero
                        : const Offset(0, -1),
                    child: AnimatedOpacity(
                      duration: const Duration(milliseconds: 300),
                      opacity:
                          value == DataStatus.loading || (value == DataStatus.success && _showConnected) ? 1.0 : 0.0,
                      child: Row(
                        children: [
                          if (value == DataStatus.loading)
                            const SizedBox(
                              width: 10,
                              height: 20,
                              child: CupertinoActivityIndicator(),
                            ),
                          if (value == DataStatus.loading) const SizedBox(width: 10),
                          Text(
                            value == DataStatus.loading ? 'chat_loading'.tr() : 'chat_connected'.tr(),
                            style: const TextStyle(fontSize: 14, color: Colors.grey),
                          ),
                        ],
                      ),
                    ),
                  );
                }),
              ],
            ),
            bottom: ChatTypeTabs(
              types: tabs,
              index: selectedIndex,
              groupCount: groupCount,
              userCount: userCount,
              onTap: (value) {
                setState(() {
                  selectedIndex = value;
                });
              },
            ),
            actions: [
              IconButton(
                onPressed: () => Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const SearchScreen(),
                  ),
                ),
                icon: const Icon(
                  size: 20,
                  LucideIcons.search,
                ),
              ),
            ],
          ),
          body: BlocSelector<ChatCubit, ChatState, DataStatus>(
            selector: (state) => state.initialLoading,
            builder: (context, initialLoading) {
              if (initialLoading == DataStatus.loading) {
                return const SharedLoading();
              }
              if (initialLoading == DataStatus.failed) {
                return const SharedError();
              }

              return TabBarView(
                children: [
                  ConversationChats(
                    onMembersCountChange: (count) => WidgetsBinding.instance.addPostFrameCallback(
                      (timeStamp) => userCount == 0 ? setState(() => userCount = count) : null,
                    ),
                    onGroupsCountChange: (count) {
                      WidgetsBinding.instance.addPostFrameCallback(
                        (timeStamp) => groupCount == 0 ? setState(() => groupCount = count) : null,
                      );
                    },
                  ),
                  GroupChats(
                    onGroupCountChanged: (count) {
                      WidgetsBinding.instance.addPostFrameCallback(
                        (timeStamp) => setState(() => groupCount = count),
                      );
                    },
                  ),
                  UsersChats(
                    onMembersCountChange: (count) => WidgetsBinding.instance.addPostFrameCallback(
                      (timeStamp) => setState(() => userCount = count),
                    ),
                  ),
                ],
              );
            },
          ),
        ),
      ),
    );
  }
}

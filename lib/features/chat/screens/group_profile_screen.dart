import 'package:flutter/material.dart';
import 'package:gp_stock_app/features/chat/widgets/group/group_admins.dart';
import 'package:gp_stock_app/features/chat/widgets/group/group_appbar.dart';
import 'package:gp_stock_app/features/chat/widgets/group/group_members.dart';

import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';

class GroupProfileScreen extends StatefulWidget {
  const GroupProfileScreen({super.key, required this.groupID});

  final String groupID;

  @override
  State<GroupProfileScreen> createState() => _GroupProfileScreenState();
}

class _GroupProfileScreenState extends State<GroupProfileScreen> {
  String? groupName;
  @override
  Widget build(BuildContext context) {
    return TIMUIKitGroupProfile(
      groupID: widget.groupID,
      builder: (context, groupInfo, groupMemberList) {
        final admins = groupMemberList
            .where(
              (element) =>
                  element?.role == GroupMemberRoleType.V2TIM_GROUP_MEMBER_ROLE_ADMIN ||
                  element?.role == GroupMemberRoleType.V2TIM_GROUP_MEMBER_ROLE_OWNER,
            )
            .toList();
        final members = groupMemberList
            .where(
              (element) => element?.role == GroupMemberRoleType.V2TIM_GROUP_MEMBER_ROLE_MEMBER,
            )
            .toList();

        return SizedBox(
          height: MediaQuery.sizeOf(context).height,
          child: Scaffold(
            appBar: GroupAppbar(
              data: groupInfo,
            ),
            body: SingleChildScrollView(
              child: Column(
                children: [
                  GroupAdmins(admins: admins),
                  GroupMembers(members: members),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

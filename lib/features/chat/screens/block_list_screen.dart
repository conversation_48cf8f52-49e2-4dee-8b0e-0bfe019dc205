import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_lucide/flutter_lucide.dart';
import 'package:gp_stock_app/shared/theme/my_color_scheme.dart';
import 'package:tencent_cloud_chat_uikit/ui/views/TIMUIKitBlackList/tim_uikit_black_list.dart';

import 'user_screen.dart';

class BlockListScreen extends StatefulWidget {
  const BlockListScreen({super.key});

  @override
  BlockListScreenState createState() => BlockListScreenState();
}

class BlockListScreenState extends State<BlockListScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        surfaceTintColor: myColorScheme(context).backgroundColor,
        backgroundColor: Colors.white,
        title: Row(
          children: [
            Padding(
              padding: const EdgeInsets.only(right: 6),
              child: Icon(
                LucideIcons.users_round,
                color: myColorScheme(context).primaryColor,
                size: 16,
              ),
            ),
            Text('chat_blocked_contacts'.tr()),
          ],
        ),
      ),
      body: TIMUIKitBlackList(
        onTapItem: (value) {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => UserScreen(userID: value.userID),
            ),
          );
        },
      ),
    );
  }
}

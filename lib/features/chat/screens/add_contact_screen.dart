// import 'package:flutter/material.dart';
// import 'package:gp_stock_app/v2/features/chat/screens/chat_screen.dart';
// import 'package:gp_stock_app/v2/features/chat/utils/scaffold.dart';
// import 'package:gp_stock_app/v2/features/chat/utils/utils.dart';
// import 'package:tencent_cloud_chat_uikit/ui/views/TIMUIKitAddFriend/tim_uikit_add_friend.dart';

// class AddContactScreen extends StatefulWidget {
//   const AddContactScreen({super.key});

//   @override
//   AddContactScreenState createState() => AddContactScreenState();
// }

// class AddContactScreenState extends State<AddContactScreen> {
//   @override
//   Widget build(BuildContext context) {
//     return CustomScaffold(
//       body: SafeArea(
//         child: TIMUIKitAddFriend(
//           onTapAlreadyFriendsItem: (String userID) {
//             Navigator.push(
//               context,
//               MaterialPageRoute(
//                 builder: (context) => ChatScreen(
//                   selectedConversation: getConversation(userID),
//                 ),
//               ),
//             );
//           },
//         ),
//       ),
//     );
//   }
// }

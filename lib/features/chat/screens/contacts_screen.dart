// import 'package:flutter/material.dart';
// import 'package:flutter_lucide/flutter_lucide.dart';
// import '../../../core/theme/my_color_scheme.dart'// import 'package:gp_stock_app/core/widgets/new/button.dart';
// import 'package:gp_stock_app/v2/features/chat/screens/add_contact_screen.dart';
// import 'package:gp_stock_app/v2/features/chat/screens/chat_screen.dart';
// import 'package:gp_stock_app/v2/features/chat/utils/scaffold.dart';
// import 'package:gp_stock_app/v2/features/chat/utils/utils.dart';
// import 'package:gp_stock_app/v2/features/chat/widgets/ui/empty_contacts.dart';
// import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';
// import 'package:tencent_cloud_chat_uikit/ui/views/TIMUIKitContact/tim_uikit_contact.dart';

// class ContactsScreen extends StatefulWidget {
//   const ContactsScreen({super.key});

//   @override
//   ContactsScreenState createState() => ContactsScreenState();
// }

// class ContactsScreenState extends State<ContactsScreen> {
//   @override
//   Widget build(BuildContext context) {
//     return CustomScaffold(
//       appBar: AppBar(
//         surfaceTintColor: myColorScheme(context).white,
//         backgroundColor: Colors.white,
//         title: Row(
//           children: [
//             Padding(
//               padding: const EdgeInsets.only(right: 6),
//               child: Icon(
//                 LucideIcons.users_round,
//                 color: myColorScheme(context).primaryColor,
//                 size: 16,
//               ),
//             ),
//             const Text('Contacts'),
//           ],
//         ),
//         actions: [
//           Padding(
//             padding: const EdgeInsets.only(right: 12),
//             child: Column(
//               crossAxisAlignment: CrossAxisAlignment.center,
//               mainAxisAlignment: MainAxisAlignment.center,
//               children: [
//                 GrButton(
//                   text: 'Add Friend',
//                   icon: LucideIcons.user_round_plus,
//                   onTap: () {
//                     showModalBottomSheet(
//                       context: context,
//                       isScrollControlled: true,
//                       builder: (ctx) => const FractionallySizedBox(
//                         heightFactor: 0.8,
//                         child: AddContactScreen(),
//                       ),
//                     );
//                   },
//                 ),
//               ],
//             ),
//           ),
//         ],
//       ),
//       body: SafeArea(
//         child: TIMUIKitContact(
//           onTapItem: (value) {
//             Navigator.push(
//               context,
//               MaterialPageRoute(
//                 builder: (context) => ChatScreen(
//                   selectedConversation: getConversation(value.userID),
//                 ),
//               ),
//             );
//           },
//           emptyBuilder: (v) => const EmptyContacts(),
//         ),
//       ),
//     );
//   }
// }

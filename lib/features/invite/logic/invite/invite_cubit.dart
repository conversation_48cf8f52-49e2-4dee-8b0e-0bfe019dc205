import 'package:bloc/bloc.dart';
import 'package:injectable/injectable.dart';

import '../../../../shared/constants/enums.dart';
import '../../domain/repository/invite_repository.dart';
import 'invite_state.dart';

@injectable
class InviteCubit extends Cubit<InviteState> {
  final InviteRepository _inviteRepository;

  InviteCubit(this._inviteRepository) : super(const InviteState());

  Future<void> getInviteDetails() async {
    emit(state.copyWith(status: DataStatus.loading));

    try {
      final result = await _inviteRepository.getInviteDetails();

      if (result.isSuccess) {
        emit(state.copyWith(
          status: DataStatus.success,
          inviteDetail: result.data,
        ));
      } else {
        emit(state.copyWith(
          status: DataStatus.failed,
          error: result.error,
        ));
      }
    } catch (e) {
      emit(state.copyWith(
        status: DataStatus.failed,
        error: e.toString(),
      ));
    }
  }
}

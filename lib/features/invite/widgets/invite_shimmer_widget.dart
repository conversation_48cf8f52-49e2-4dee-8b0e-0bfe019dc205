import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';

import '../../../shared/widgets/shimmer/shimmer_widget.dart';

class InviteScreenShimmer extends StatelessWidget {
  const InviteScreenShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.redAccent,
            Color(0xFFFFC6C6),
          ],
        ),
      ),
      child: Column(
        children: [
          _buildHeaderShimmer(),
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.vertical(top: Radius.circular(20.gr)),
              ),
              margin: EdgeInsets.only(top: 20.gh),
              padding: EdgeInsets.all(16.gr),
              child: ShimmerWidget(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      _buildReferralOptionsShimmer(),
                      Sized<PERSON>ox(height: 20.gh),
                      _buildButtonShimmer(),
                      <PERSON><PERSON><PERSON>ox(height: 20.gh),
                      _buildCommissionInfoShimmer(),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeaderShimmer() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 20.gw, vertical: 16.gh),
      child: ShimmerWidget(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              width: 180.gw,
              height: 28.gh,
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.5),
                borderRadius: BorderRadius.circular(4.gr),
              ),
            ),
            SizedBox(height: 8.gh),
            Container(
              width: 280.gw,
              height: 18.gh,
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.5),
                borderRadius: BorderRadius.circular(4.gr),
              ),
            ),
            SizedBox(height: 16.gh),
            Container(
              height: 120.gh,
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.5),
                borderRadius: BorderRadius.circular(8.gr),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReferralOptionsShimmer() {
    return Row(
      children: [
        Expanded(
          child: Container(
            padding: EdgeInsets.symmetric(vertical: 24.gh, horizontal: 16.gw),
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(10.gr),
            ),
            height: 100.gh,
          ),
        ),
        SizedBox(width: 16.gw),
        Expanded(
          child: Container(
            padding: EdgeInsets.symmetric(vertical: 24.gh, horizontal: 16.gw),
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(10.gr),
            ),
            height: 100.gh,
          ),
        ),
      ],
    );
  }

  Widget _buildButtonShimmer() {
    return Container(
      height: 48.gh,
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(10.gr),
      ),
    );
  }

  Widget _buildCommissionInfoShimmer() {
    return Container(
      padding: EdgeInsets.all(16.gr),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(10.gr),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Container(
                      width: 100.gw,
                      height: 16.gh,
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        borderRadius: BorderRadius.circular(4.gr),
                      ),
                    ),
                    SizedBox(height: 10.gh),
                    Container(
                      width: 80.gw,
                      height: 28.gh,
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        borderRadius: BorderRadius.circular(4.gr),
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                height: 50.gh,
                width: 1,
                color: Colors.grey[300],
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Container(
                      width: 100.gw,
                      height: 16.gh,
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        borderRadius: BorderRadius.circular(4.gr),
                      ),
                    ),
                    SizedBox(height: 10.gh),
                    Container(
                      width: 80.gw,
                      height: 28.gh,
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        borderRadius: BorderRadius.circular(4.gr),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: 20.gh),
          Divider(height: 1, color: Colors.grey[300]),
          SizedBox(height: 20.gh),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                width: 80.gw,
                height: 16.gh,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(4.gr),
                ),
              ),
              Container(
                width: 120.gw,
                height: 16.gh,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(4.gr),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

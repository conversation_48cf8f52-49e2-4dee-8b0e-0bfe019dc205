import 'package:bloc/bloc.dart';
import 'package:injectable/injectable.dart';

import '../../../../shared/constants/enums.dart';
import '../../domain/repository/company_news_repository.dart';
import 'company_news_state.dart';

@injectable
class CompanyNewsCubit extends Cubit<CompanyNewsDetailsState> {
  final CompanyNewsRepository _repository;

  CompanyNewsCubit(this._repository) : super(const CompanyNewsDetailsState());

  Future<void> getCompanyNewsDetails(String id) async {
    emit(state.copyWith(status: DataStatus.loading));

    final result = await _repository.getCompanyNewsDetails(id);

    if (result.data != null) {
      emit(state.copyWith(
        status: DataStatus.success,
        newsDetails: result.data?.data,
        error: null,
      ));
    } else {
      emit(state.copyWith(
        status: DataStatus.failed,
        error: result.error,
      ));
    }
  }
}

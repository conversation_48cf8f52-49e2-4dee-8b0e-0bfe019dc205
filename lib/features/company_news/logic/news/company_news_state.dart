import 'package:equatable/equatable.dart';

import '../../../../shared/constants/enums.dart';
import '../../domain/models/company_news_details.dart';

class CompanyNewsDetailsState extends Equatable {
  final DataStatus status;
  final CompanyNewsDetailsData? newsDetails;
  final String? error;

  const CompanyNewsDetailsState({
    this.status = DataStatus.idle,
    this.newsDetails,
    this.error,
  });

  @override
  List<Object?> get props => [status, newsDetails, error];

  CompanyNewsDetailsState copyWith({
    DataStatus? status,
    CompanyNewsDetailsData? newsDetails,
    String? error,
  }) {
    return CompanyNewsDetailsState(
      status: status ?? this.status,
      newsDetails: newsDetails ?? this.newsDetails,
      error: error ?? this.error,
    );
  }
}

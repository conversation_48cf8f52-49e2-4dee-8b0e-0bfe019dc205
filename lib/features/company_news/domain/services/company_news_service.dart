import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';

import '../../../../core/api/network/endpoint/api_endpoints.dart';
import '../../../../core/api/network/models/result.dart';
import '../../../../core/api/network/network.dart';
import '../models/company_news_details.dart';
import '../repository/company_news_repository.dart';

@Injectable(as: CompanyNewsRepository)
class CompanyNewsService implements CompanyNewsRepository {
  @override
  Future<ResponseResult<CompanyNewsDetailsResponse>> getCompanyNewsDetails(String id) async {
    try {
      final response = await NetworkProvider().get(
        ApiEndpoints.companyNewsInfo,
        queryParameters: {'id': id},
        isAuthRequired: false,
      );

      if (response.statusCode == 200 && response.data['code'] == 0) {
        return ResponseResult(data: CompanyNewsDetailsResponse.fromJson(response.data));
      }
      return ResponseResult(error: response.data['msg'] ?? 'Failed to get news details');
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }
}

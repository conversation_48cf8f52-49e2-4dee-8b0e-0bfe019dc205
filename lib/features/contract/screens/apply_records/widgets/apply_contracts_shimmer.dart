import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';

class ApplyContractsShimmer extends StatelessWidget {
  const ApplyContractsShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      padding: EdgeInsets.symmetric(horizontal: 10.gw),
      itemBuilder: (context, index) => ShimmerWidget(
        radius: 12.gr,
        height: 90.gh,
        width: double.infinity,
      ),
      separatorBuilder: (context, index) => 10.verticalSpace,
      itemCount: 6,
    );
  }
}

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/widgets/table_empty.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../../../core/utils/utils.dart';
import '../../../../shared/app/extension/helper.dart';
import '../../../../shared/constants/enums.dart';
import '../../../../shared/routes/routes.dart';
import '../../../../shared/theme/font_pallette.dart';
import '../../../../shared/theme/my_color_scheme.dart';
import '../../../../shared/widgets/pagination/common_refresher.dart';
import '../../../account/domain/models/account_summary/contract_summary_response.dart';
import '../../../account/logic/account/account_cubit.dart';
import 'widgets/apply_contracts_shimmer.dart';

class ContractSettleHistoryScreen extends StatefulWidget {
  const ContractSettleHistoryScreen({super.key});

  @override
  State<ContractSettleHistoryScreen> createState() => _ContractSettleHistoryScreenState();
}

class _ContractSettleHistoryScreenState extends State<ContractSettleHistoryScreen> {
  final RefreshController _refreshController = RefreshController(initialRefresh: false);
  final int _settlementStatus = 2;

  @override
  void initState() {
    super.initState();
    Helper.afterInit(_fetchData);
  }

  Future<void> _fetchData() async {
    context.read<AccountCubit>().getContractSummaryWithSettlementStatus(settlementStatus: _settlementStatus);
  }

  Future<void> _onRefresh() async {
    await context.read<AccountCubit>().getContractSummaryWithSettlementStatus(settlementStatus: _settlementStatus);
    _refreshController.refreshCompleted();
  }

  Future<void> _onLoading() async {
    await context
        .read<AccountCubit>()
        .getContractSummaryWithSettlementStatus(settlementStatus: _settlementStatus, isLoadMore: true);

    if (!mounted) return;

    final state = context.read<AccountCubit>().state;
    final recordCount = state.contractSummary?.records?.length ?? 0;
    final totalCount = state.contractSummary?.total ?? 0;

    if (recordCount >= totalCount) {
      _refreshController.loadNoData();
    } else {
      _refreshController.loadComplete();
    }
  }

  @override
  void dispose() {
    _refreshController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('historicalMessages'.tr()),
        surfaceTintColor: Colors.transparent,
      ),
      body: BlocBuilder<AccountCubit, AccountState>(
        builder: (context, state) {
          if (state.contractSettlementFetchStatus == DataStatus.loading) {
            return const ApplyContractsShimmer();
          }
          final records = state.settledContractSummary?.records ?? [];
          if (records.isEmpty || state.contractSettlementFetchStatus == DataStatus.failed) {
            return CommonRefresher(
              controller: _refreshController,
              enablePullDown: true,
              onRefresh: _onRefresh,
              child: _buildEmptyState(),
            );
          }

          return CommonRefresher(
            controller: _refreshController,
            enablePullDown: true,
            enablePullUp: state.settledContractSummary?.hasNext ?? false,
            onRefresh: _onRefresh,
            onLoading: _onLoading,
            child: AnimationLimiter(
              child: ListView.builder(
                padding: EdgeInsets.symmetric(horizontal: 10.gr, vertical: 10.gr),
                itemCount: records.length,
                itemBuilder: (context, index) {
                  return AnimationConfiguration.staggeredList(
                    position: index,
                    duration: const Duration(milliseconds: 600),
                    child: SlideAnimation(
                      verticalOffset: 50.0,
                      child: FadeInAnimation(
                        child: Padding(
                          padding: EdgeInsets.only(bottom: 10.gr),
                          child: ContractApplyRecordItem(record: records[index]),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return ListView(
      physics: const AlwaysScrollableScrollPhysics(),
      children: [
        SizedBox(
          height: 0.7.gsh,
          child: Center(
            child: TableEmptyWidget(
              height: 60.gh,
              width: 60.gw,
            ),
          ),
        ),
      ],
    );
  }
}

class ContractApplyRecordItem extends StatelessWidget {
  final ContractSummaryData record;

  const ContractApplyRecordItem({super.key, required this.record});

  @override
  Widget build(BuildContext context) {
    final colorScheme = myColorScheme(context);

    return AnimationConfiguration.synchronized(
      duration: const Duration(milliseconds: 300),
      child: ScaleAnimation(
        scale: 0.95,
        child: Container(
          width: double.infinity,
          padding: EdgeInsets.all(12.gr),
          decoration: BoxDecoration(
            color: colorScheme.cardColor,
            borderRadius: BorderRadius.circular(12.gr),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withNewOpacity(0.05),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(getContractLabel(record), style: FontPalette.semiBold12),
                  GestureDetector(
                    onTap: () =>
                        Navigator.pushNamed(context, routeSpotAndContractHistory, arguments: {'contract': record}),
                    child: Container(
                      padding: EdgeInsets.symmetric(horizontal: 6.gw, vertical: 4.gh),
                      decoration: BoxDecoration(
                        color: colorScheme.primaryColor,
                        borderRadius: BorderRadius.circular(6.gr),
                      ),
                      child: Text(
                        'transactionDetails'.tr(),
                        style: FontPalette.semiBold12.copyWith(
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              8.verticalSpace,
              _buildDataFields(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDataFields(BuildContext context) {
    return Column(
      children: [
        _DataField(
          label: 'date'.tr(),
          value: _formatDateRange(record.openTime, record.expireTime),
        ),
        _DataField(
          label: 'initialMargin'.tr(),
          value: record.initCash.toString(),
        ),
        _DataField(
          label: 'totalOperatingFunds'.tr(),
          value: record.totalPower.toString(),
        ),
        _DataField(
          label: 'expandedMargin'.tr(),
          value: record.expendAmount.toString(),
        ),
        _DataField(
          label: 'supplementLoss'.tr(),
          value: record.coverLossAmount.toString(),
        ),
        _DataField(
          label: 'totalProfitAndLoss'.tr(),
          value: record.winAmount.toString(),
        ),
      ],
    );
  }

  String _formatDateRange(String? startDate, String? endDate) {
    final start = (startDate ?? '0').split(' ')[0];
    final end = (endDate ?? '0').split(' ')[0];
    return '$start - $end';
  }
}

class _DataField extends StatelessWidget {
  final String label;
  final String value;

  const _DataField({
    required this.label,
    required this.value,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = myColorScheme(context);

    return Padding(
      padding: EdgeInsets.symmetric(vertical: 3.gr),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: FontPalette.normal13.copyWith(
              color: colorScheme.titleColor,
            ),
          ),
          Text(
            value,
            style: FontPalette.semiBold14.copyWith(
              fontFamily: 'Akzidenz-Grotesk',
              letterSpacing: -0.5,
              color: colorScheme.primaryColor.withValues(alpha: 5),
            ),
          ),
        ],
      ),
    );
  }
}

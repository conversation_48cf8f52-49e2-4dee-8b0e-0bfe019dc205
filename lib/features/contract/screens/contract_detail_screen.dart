import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/core/utils/utils.dart';
import 'package:gp_stock_app/features/account/widgets/account_market_table.dart';
import 'package:gp_stock_app/features/account/widgets/build_action_buttons.dart';
import 'package:gp_stock_app/features/contract/screens/contract_info_screen.dart';
import 'package:gp_stock_app/shared/constants/assets.dart';
import 'package:gp_stock_app/shared/models/route_arguments/trading_arguments.dart';

import '../../../shared/app/extension/helper.dart';
import '../../../shared/constants/enums.dart';
import '../../../shared/routes/routes.dart';
import '../../../shared/theme/my_color_scheme.dart';
import '../../account/domain/models/account_summary/contract_summary_response.dart';
import '../../account/logic/account/account_cubit.dart';
import '../../account/widgets/assets_card.dart';

/// 合约总详情
class ContractDetailScreen extends StatefulWidget {
  final ContractSummaryData contractSummary;
  const ContractDetailScreen({super.key, required this.contractSummary});

  @override
  State<ContractDetailScreen> createState() => _ContractDetailScreenState();
}

class _ContractDetailScreenState extends State<ContractDetailScreen> {
  @override
  void initState() {
    super.initState();
    Helper.afterInit(_init);
  }

  Future<void> _init() async {
    final accountCubit = context.read<AccountCubit>();
    accountCubit.startContractDataPolling(widget.contractSummary.id ?? 0);
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      onPopInvokedWithResult: (didPop, result) => context.read<AccountCubit>().stopContractDataPolling(),
      child: Scaffold(
        appBar: AppBar(
          surfaceTintColor: Colors.transparent,
          backgroundColor: myColorScheme(context).cardColor,
          title: Text(getContractLabel(widget.contractSummary)),
          // actions: [
          //   IconButton(
          //     onPressed: () {
          //       context.verifyAuth(
          //         () => Navigator.push(
          //             context,
          //             MaterialPageRoute(
          //                 builder: (context) => ContractInfoScreen(contractSummary: widget.contractSummary))),
          //       );
          //     },
          //     icon: const Icon(Icons.more_horiz_outlined, size: 16),
          //   ),
          // ],
        ),
        body: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: Column(
            children: [
              Container(
                decoration: BoxDecoration(
                  color: myColorScheme(context).cardColor,
                  borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(20.gr),
                    bottomRight: Radius.circular(20.gr),
                  ),
                ),
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16.gw),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      // Assets Card
                      BlocBuilder<AccountCubit, AccountState>(
                        builder: (context, state) {
                          return AssetsCard(
                            totalAssets: state.currentContractSummary?.allAsset ?? widget.contractSummary.allAsset ?? 0,
                            todaysEarnings: state.currentContractSummary?.todayWinAmount ??
                                widget.contractSummary.todayWinAmount ??
                                0,
                            availableBalance:
                                state.currentContractSummary?.useAmount ?? widget.contractSummary.useAmount ?? 0,
                            myInterest: state.currentContractSummary?.interestAmount ??
                                widget.contractSummary.interestAmount ??
                                0,
                            frozenAmount:
                                state.currentContractSummary?.freezePower ?? widget.contractSummary.freezePower ?? 0,
                            isContract: true,
                            currency: widget.contractSummary.currency ?? 'CNY',
                          );
                        },
                      ),
                      14.verticalSpace,
                      // Action Buttons
                      if (widget.contractSummary.type == 1) ...[
                        _ActionButtons1(contractSummary: widget.contractSummary),
                      ],
                      if (widget.contractSummary.type == 2) ...[
                        _ActionButtons2(contractSummary: widget.contractSummary),
                      ],
                      if (widget.contractSummary.type == 3) ...[
                        _ActionButtons3(contractSummary: widget.contractSummary),
                      ],
                      18.verticalSpace,
                    ],
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.gw),
                child: Column(
                  children: [
                    14.verticalSpace,
                    AccountMarketTable(contract: widget.contractSummary),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

//standard
//experience
class _ActionButtons1 extends StatelessWidget {
  final ContractSummaryData contractSummary;
  const _ActionButtons1({required this.contractSummary});

  @override
  Widget build(BuildContext context) {
    return Column(
      spacing: 15.gh,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            Expanded(
              child: BuildActionButton(
                label: 'expandMargin'.tr(),
                icon: Assets.percentageProgressIcon,
                onTap: () => Navigator.pushNamed(context, routeMarginCall, arguments: {
                  'contractId': contractSummary.id,
                  'contractType': ContractAction.marginExpand,
                  'type': contractSummary.contractType
                }).then(
                  (value) {
                    if (!context.mounted) return;
                    context.read<AccountCubit>().getCurrentContractSummary(contractSummary.id!);
                  },
                ),
              ),
            ),
            Expanded(
              child: BuildActionButton(
                label: 'supplementLoss'.tr(),
                icon: Assets.downFallIcon,
                onTap: () => Navigator.pushNamed(context, routeMarginCall, arguments: {
                  'contractId': contractSummary.id,
                  'contractType': ContractAction.replenish,
                  'type': contractSummary.contractType
                }),
              ),
            ),
            Expanded(
              child: BuildActionButton(
                label: 'cashOut1'.tr(),
                icon: Assets.withdrawIcon,
                onTap: () {
                  Navigator.pushNamed(context, routeContractWithdraw, arguments: {
                    'contractId': contractSummary.id.toString(),
                  });
                },
              ),
            ),
            Expanded(
              child: BuildActionButton(
                label: 'tradingCenter'.tr(),
                icon: Assets.tradingIcon,
                onTap: () {
                  final instrument = getTradingArguments(contractSummary.marketType!);
                  Navigator.pushNamed(
                    context,
                    routeTradingCenter,
                    arguments: TradingArguments(
                      instrumentInfo: instrument,
                      selectedIndex: TradeTabType.Trading.index,
                      // shouldNavigateToIndex: true,
                      contract: context.read<AccountCubit>().state.currentContractSummary ?? contractSummary,
                    ),
                  );
                },
              ),
            ),
          ],
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            Expanded(
              child: BuildActionButton(
                label: 'fundRecords'.tr(),
                icon: Assets.recordsIcon,
                onTap: () => Navigator.pushNamed(context, routeFundRecords,
                    arguments: {'contractId': contractSummary.id, 'isContractAccount': true}),
              ),
            ),
            // Expanded(
            //   child: BuildActionButton(
            //     label: 'contractRenewal'.tr(),
            //     icon: Assets.listAcceptIcon,
            //     // onTap: () => Navigator.pushNamed(context, routeMarginCall,
            //     //     arguments: {'contractId': contractSummary.id, 'contractType': Contract.renew}),
            //   ),
            // ),
            Expanded(
              child: BuildActionButton(
                label: 'terminateContract'.tr(),
                icon: Assets.listDeniedIcon,
                onTap: () => Navigator.pushNamed(context, routeTerminateContract, arguments: contractSummary),
              ),
            ),
            Expanded(
              child: BuildActionButton(
                label: 'transactionHistory'.tr(),
                icon: Assets.listHistoryIcon,
                onTap: () =>
                    Navigator.pushNamed(context, routeSpotAndContractHistory, arguments: {'contract': contractSummary}),
              ),
            ),
            Expanded(
              child: BuildActionButton(
                label: 'contractDetails'.tr(),
                icon: Assets.listAcceptIcon,
                onTap: () => context.verifyAuth(
                  () => Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => ContractInfoScreen(
                        contractSummary: contractSummary,
                      ),
                    ),
                  ),
                ),
              ),
            ),
            // Expanded(
            //   child: SizedBox(),
            // ),
          ],
        ),
      ],
    );
  }
}

//bonus
class _ActionButtons3 extends StatelessWidget {
  final ContractSummaryData contractSummary;
  const _ActionButtons3({required this.contractSummary});

  @override
  Widget build(BuildContext context) {
    return Column(
      spacing: 15.gh,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            Expanded(
              child: BuildActionButton(
                label: 'expandMargin'.tr(),
                icon: Assets.percentageProgressIcon,
                onTap: () {
                  Navigator.pushNamed(context, routeMarginCall, arguments: {
                    'contractId': contractSummary.id,
                    'contractType': ContractAction.marginExpand,
                    'type': contractSummary.contractType
                  });
                },
              ),
            ),
            Expanded(
              child: BuildActionButton(
                label: 'supplementLoss'.tr(),
                icon: Assets.downFallIcon,
                onTap: () => Navigator.pushNamed(context, routeMarginCall, arguments: {
                  'contractId': contractSummary.id,
                  'contractType': ContractAction.replenish,
                  'type': contractSummary.contractType
                }),
              ),
            ),
            Expanded(
              child: BuildActionButton(
                label: 'cashOut1'.tr(),
                icon: Assets.withdrawIcon,
                onTap: () => Navigator.pushNamed(context, routeContractWithdraw, arguments: {
                  'contractId': contractSummary.id.toString(),
                }),
              ),
            ),
            Expanded(
              child: BuildActionButton(
                label: 'tradingCenter'.tr(),
                icon: Assets.tradingIcon,
                onTap: () {
                  final instrument = getTradingArguments(contractSummary.marketType!);
                  Navigator.pushNamed(
                    context,
                    routeTradingCenter,
                    arguments: TradingArguments(
                      instrumentInfo: instrument,
                      selectedIndex: TradeTabType.Trading.index,
                      contract: context.read<AccountCubit>().state.currentContractSummary ?? contractSummary,
                    ),
                  );
                },
              ),
            ),
          ],
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            Expanded(
              child: BuildActionButton(
                label: 'funding_records'.tr(),
                icon: Assets.recordsIcon,
                onTap: () => Navigator.pushNamed(context, routeFundRecords,
                    arguments: {'contractId': contractSummary.id, 'isContractAccount': true}),
              ),
            ),
            Expanded(
              child: BuildActionButton(
                label: 'terminateContract'.tr(),
                icon: Assets.listDeniedIcon,
                onTap: () => Navigator.pushNamed(context, routeTerminateContract, arguments: contractSummary),
              ),
            ),

            Expanded(
              child: BuildActionButton(
                label: 'historicalMessages'.tr(),
                icon: Assets.listHistoryIcon,
                onTap: () =>
                    Navigator.pushNamed(context, routeSpotAndContractHistory, arguments: {'contract': contractSummary}),
              ),
            ),
            Expanded(
              child: BuildActionButton(
                label: 'contractDetails'.tr(),
                icon: Assets.listAcceptIcon,
                onTap: () => context.verifyAuth(
                  () => Navigator.push(context,
                      MaterialPageRoute(builder: (context) => ContractInfoScreen(contractSummary: contractSummary))),
                ),
              ),
            ),
            // Expanded(
            //   child: SizedBox(),
            // ),
          ],
        ),
      ],
    );
  }
}

// ignore: unused_element
class _ActionButtons2 extends StatelessWidget {
  final ContractSummaryData contractSummary;
  const _ActionButtons2({required this.contractSummary});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        Expanded(
          child: BuildActionButton(
            label: 'tradingCenter'.tr(),
            icon: Assets.tradingIcon,
            onTap: () {
              final instrument = getTradingArguments(contractSummary.marketType!);
              Navigator.pushNamed(
                context,
                routeTradingCenter,
                arguments: TradingArguments(
                  instrumentInfo: instrument,
                  selectedIndex: TradeTabType.Trading.index,
                  contract: context.read<AccountCubit>().state.currentContractSummary ?? contractSummary,
                ),
              );
            },
          ),
        ),
        Expanded(
          child: BuildActionButton(
            label: 'apply_records'.tr(),
            icon: Assets.recordsIcon,
            onTap: () => Navigator.pushNamed(context, routeFundRecords,
                arguments: {'contractId': contractSummary.id, 'isContractAccount': true}),
          ),
        ),
        Expanded(
          child: BuildActionButton(
            label: 'terminateContract'.tr(),
            icon: Assets.listDeniedIcon,
            onTap: () => Navigator.pushNamed(context, routeTerminateContract, arguments: contractSummary),
          ),
        ),
        Expanded(
          child: BuildActionButton(
            label: 'historicalMessages'.tr(),
            icon: Assets.listHistoryIcon,
            onTap: () =>
                Navigator.pushNamed(context, routeSpotAndContractHistory, arguments: {'contract': contractSummary}),
          ),
        ),
        Expanded(
          child: BuildActionButton(
            label: 'contractDetails'.tr(),
            icon: Assets.listAcceptIcon,
            onTap: () => context.verifyAuth(
              () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => ContractInfoScreen(
                    contractSummary: contractSummary,
                  ),
                ),
              ),
            ),
          ),
        ),
        // Expanded(
        //   child: SizedBox(),
        // ),
      ],
    );
  }
}

import 'package:gp_stock_app/features/contract/domain/models/contract_calculation/contract_calculation_model.dart';
import 'package:gp_stock_app/features/contract/domain/models/contract_withdrawal_config/contract_withdrawal_config.dart';

import '../../../../core/api/network/models/result.dart';
import '../models/assets_records/assets_records.dart';
import '../models/contract_activity/contract_activity_response.dart';
import '../models/contract_application/contract_application.dart';
import '../models/contract_apply_records/contract_apply_records.dart';
import '../models/expand_margin/margin_call_response.dart';

abstract class ContractRepository {
  const ContractRepository();

  Future<ResponseResult<ContractApplyRecords>> getContractApplyRecords({
    required int pageNum,
  });

  Future<ResponseResult<ContractApplicationData>> getOrdinaryConfigInfo({required int type});

  Future<ResponseResult<ContractActivityData>> getContractActivity({
    required int type,
    required int parentType,
  });

  Future<ResponseResult<bool>> applyOrdinaryContract({
    required int applyAmount,
    required int contractConfigId,
    required int riskId,
    required int type,
  });

  Future<ResponseResult<bool>> terminateContract({required String contractId});

  Future<ResponseResult<AssetsRecords>> getAssetRecord({
    required int? contractId,
    required int pageNum,
    required String? commentAssetId,
    String? fromType,
    String? startTime,
    String? endTime,
  });

  Future<ResponseResult<AssetsRecords>> getContractAccountRecord({
    required int? contractId,
    required int pageNum,
  });

  Future<ResponseResult<MarginCallResponse>> getMarginCall({required int contractId});

  Future<ResponseResult<bool>> addExpandMargin({
    required String contractId,
    required double applyAmount,
    required int type,
  });

  Future<ResponseResult<bool>> renewalContract({required String contractId});
  Future<ResponseResult<bool>> applyExperienceContract(
      {required int activityId, required int activityRiskId, required double applyAmount, required int type});
  Future<ResponseResult<bool>> applyBonusContract(
      {required int activityId, required int activityRiskId, required double applyAmount, required int type});
  Future<ResponseResult<bool>> withdraw({required String applyAmount, required String id});
  Future<ResponseResult<ContractWithdrawalConfig>> getWithdrawConfig({required String contractId});

  Future<ResponseResult<ContractCalculationModel>> getOrdinaryApplyAmount({
    required int contractConfigId,
    required int riskId,
    required int applyAmount,
    required int type,
  });

  Future<ResponseResult<ContractCalculationModel>> getBonusContractAmount({
    required int activityId,
    required int activityRiskId,
    required int applyAmount,
    required int type,
  });

  Future<ResponseResult<List<int>>> getOpenContractType();
}

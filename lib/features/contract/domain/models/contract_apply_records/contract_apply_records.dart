// To parse this JSON data, do
//
//     final contractApplyRecordsResponse = contractApplyRecordsResponseFromJson(jsonString);

import 'package:freezed_annotation/freezed_annotation.dart';
import 'dart:convert';

part 'contract_apply_records.freezed.dart';
part 'contract_apply_records.g.dart';

ContractApplyRecordsResponse contractApplyRecordsResponseFromJson(str) => ContractApplyRecordsResponse.fromJson((str));

String contractApplyRecordsResponseToJson(ContractApplyRecordsResponse data) => json.encode(data.toJson());

@freezed
class ContractApplyRecordsResponse with _$ContractApplyRecordsResponse {
  const factory ContractApplyRecordsResponse({
    int? code,
    ContractApplyRecords? data,
    String? msg,
  }) = _ContractApplyRecordsResponse;

  factory ContractApplyRecordsResponse.fromJson(Map<String, dynamic> json) =>
      _$ContractApplyRecordsResponseFromJson(json);
}

@freezed
class ContractApplyRecords with _$ContractApplyRecords {
  const factory ContractApplyRecords({
    int? current,
    bool? hasNext,
    List<ContractApplyRecord>? records,
    int? total,
  }) = _ContractApplyRecords;

  factory ContractApplyRecords.fromJson(Map<String, dynamic> json) => _$ContractApplyRecordsFromJson(json);
}

@freezed
class ContractApplyRecord with _$ContractApplyRecord {
  const factory ContractApplyRecord({
    int? auditStatus,
    DateTime? createTime,
    int? id,
    String? marketType,
    int? multiple,
    int? periodType,
    int? totalCash,
    int? totalPower,
    int? type,
  }) = _ContractApplyRecord;

  factory ContractApplyRecord.fromJson(Map<String, dynamic> json) => _$ContractApplyRecordFromJson(json);
}

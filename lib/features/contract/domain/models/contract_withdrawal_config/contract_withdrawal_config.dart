import 'package:freezed_annotation/freezed_annotation.dart';

part 'contract_withdrawal_config.freezed.dart';
part 'contract_withdrawal_config.g.dart';

@freezed
class ContractWithdrawalConfig with _$ContractWithdrawalConfig {
  const factory ContractWithdrawalConfig({
    String? endTime,
    @Default(0) int id,
    @Default(0) double maxProfitWithdrawalAmount,
    @Default(0) double minProfitWithdrawalAmount,
    String? startTime,
    @Default(true) bool testUsersCanWithdraw,
    @Default(0) double amount,
    @Default(true) bool canUse,
    @Default(0) int dailyProfitWithdrawalCount,
  }) = _ContractWithdrawalConfig;

  factory ContractWithdrawalConfig.fromJson(Map<String, dynamic> json) => _$ContractWithdrawalConfigFromJson(json);
}

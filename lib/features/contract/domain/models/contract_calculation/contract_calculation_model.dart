import 'package:freezed_annotation/freezed_annotation.dart';

part 'contract_calculation_model.freezed.dart';
part 'contract_calculation_model.g.dart';

@freezed
class ContractCalculationModel with _$ContractCalculationModel {
  const factory ContractCalculationModel({
    @Default(0) double canUserCashCNY,
    @Default(0) double deductCanUseCashCNY,
    @Default(0) double deductInterestCashCNY,
    @Default(0) double discountInterestCNY,
    @Default(0) double exchangeRate,
    @Default(0) double interestCashCNY,
    @Default(0) double rate,
    @Default(0) double rateAmount,
  }) = _ContractCalculationModel;

  factory ContractCalculationModel.fromJson(Map<String, dynamic> json) => _$ContractCalculationModelFromJson(json);
}

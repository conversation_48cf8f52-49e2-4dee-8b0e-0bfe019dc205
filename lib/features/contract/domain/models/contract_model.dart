class ContractModelAmount {
  final double? totalTadingFunds;
  final double? lossWarningLine;
  final double? lossFlatLine;
  final double? interestRate;
  final double? deductionAmount;
  final double? actualAmount;
  final double? intrestDeductionAmount;
  final double? bonus;
  final double multipleAmount;
  ContractModelAmount({
    this.totalTadingFunds,
    this.lossWarningLine,
    this.lossFlatLine,
    this.interestRate,
    this.deductionAmount,
    this.actualAmount,
    this.intrestDeductionAmount,
    this.bonus,
    this.multipleAmount = 0,
  });

  factory ContractModelAmount.fromJson(Map<String, dynamic> json) => ContractModelAmount(
        totalTadingFunds: json['totalTadingFunds'],
        lossWarningLine: json['lossWarningLine'],
        lossFlatLine: json['lossFlatLine'],
        interestRate: json['interestRate'],
        deductionAmount: json['deductionAmount'],
        actualAmount: json['actualAmount'],
        intrestDeductionAmount: json['intrestDeductionAmount'],
        bonus: json['bonus'],
      );

  Map<String, dynamic> toJson() => {
        'totalTadingFunds': totalTadingFunds,
        'lossWarningLine': lossWarningLine,
        'lossFlatLine': lossFlatLine,
        'interestRate': interestRate,
        'deductionAmount': deductionAmount,
        'actualAmount': actualAmount,
        'intrestDeductionAmount': intrestDeductionAmount,
        'bonus': bonus,
      };
}

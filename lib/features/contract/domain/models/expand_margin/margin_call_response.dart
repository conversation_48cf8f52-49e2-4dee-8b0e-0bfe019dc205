import 'package:easy_localization/easy_localization.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../../../shared/constants/enums.dart';

part 'margin_call_response.freezed.dart';
part 'margin_call_response.g.dart';

@freezed
class MarginCallResponse with _$MarginCallResponse {
  const factory MarginCallResponse({
    @Json<PERSON>ey(name: 'code') int? code,
    @Json<PERSON><PERSON>(name: 'data') MarginCallData? data,
    @Json<PERSON>ey(name: 'msg') String? msg,
    @Json<PERSON>ey(name: 'sign') String? sign,
  }) = _MarginCallResponse;

  factory MarginCallResponse.fromJson(Map<String, dynamic> json) => _$MarginCallResponseFromJson(json);
}

@freezed
class MarginCallData with _$MarginCallData {
  const MarginCallData._(); // Added private constructor for getters

  const factory MarginCallData({
    @<PERSON><PERSON><PERSON><PERSON>(name: 'amountList') List<MarginCallAmount>? amountList,
    @Json<PERSON><PERSON>(name: 'bonusAmountList') List<double>? bonusAmountList,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'closeAmount') double? closeAmount,
    @JsonKey(name: 'closeValue') double? closeValue,
    @JsonKey(name: 'giveDay') int? giveDay,
    @JsonKey(name: 'id') int? id,
    @JsonKey(name: 'initCash') double? initCash,
    @JsonKey(name: 'interestRate') double? interestRate,
    @JsonKey(name: 'marketType') String? marketType,
    @JsonKey(name: 'multiple') int? multiple,
    @JsonKey(name: 'periodType') int? periodType,
    @JsonKey(name: 'totalCash') double? totalCash,
    @JsonKey(name: 'totalFinance') double? totalFinance,
    @JsonKey(name: 'totalPower') double? totalPower,
    @JsonKey(name: 'type') int? type,
    @JsonKey(name: 'warnAmount') double? warnAmount,
    @JsonKey(name: 'warnValue') double? warnValue,
  }) = _MarginCallData;

  factory MarginCallData.fromJson(Map<String, dynamic> json) => _$MarginCallDataFromJson(json);

  String? get typeText => type == null ? null : marginCallTypeTranslation[type]?.tr();

  String? get contractTypeText => type == null ? null : contractMarketTranslation2[marketType];

  String? get periodTypeText => periodType == null ? null : 'contract.period_$periodType'.tr();

  String? get marketTypeText => marketType == null ? null : contractMarketTranslation[marketType]?.tr();

  ContractType get contractType => ContractType.fromValue(type ?? 0);
}

@freezed
class MarginCallAmount with _$MarginCallAmount {
  const factory MarginCallAmount({
    @JsonKey(name: 'id') int? id,
    @JsonKey(name: 'applyAmount') double? applyAmount,
  }) = _MarginCallAmount;

  factory MarginCallAmount.fromJson(Map<String, dynamic> json) => _$MarginCallAmountFromJson(json);
}

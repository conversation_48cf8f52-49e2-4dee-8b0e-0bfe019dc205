// To parse this JSON data, do
//
//     final contractApplicationResponse = contractApplicationResponseFromJson(jsonString);

import 'dart:convert';

import 'package:freezed_annotation/freezed_annotation.dart';

part 'contract_application.freezed.dart';
part 'contract_application.g.dart';

ContractApplicationResponse contractApplicationResponseFromJson(str) => ContractApplicationResponse.fromJson((str));

String contractApplicationResponseToJson(ContractApplicationResponse data) => json.encode(data.toJson());

@freezed
class ContractApplicationResponse with _$ContractApplicationResponse {
  const factory ContractApplicationResponse({
    int? code,
    ContractApplicationData? data,
    String? msg,
  }) = _ContractApplicationResponse;

  factory ContractApplicationResponse.fromJson(Map<String, dynamic> json) =>
      _$ContractApplicationResponseFromJson(json);
}

@freezed
class ContractApplicationData with _$ContractApplicationData {
  const factory ContractApplicationData({
    List<ApplyAmount>? amountList,
    List<ContractConfigMap>? contractConfigMap,
    String? currency,
    int? interestCash,
    List<RuleMap>? ruleMap,
    double? useAmount,
  }) = _ContractApplicationData;

  factory ContractApplicationData.fromJson(Map<String, dynamic> json) => _$ContractApplicationDataFromJson(json);
}

@freezed
class ApplyAmount with _$ApplyAmount {
  const factory ApplyAmount({
    int? applyAmount,
    int? id,
  }) = _ApplyAmount;

  factory ApplyAmount.fromJson(Map<String, dynamic> json) => _$ApplyAmountFromJson(json);
}

@freezed
class ContractConfigMap with _$ContractConfigMap {
  const factory ContractConfigMap({
    List<ConfigList>? configList,
    int? periodType,
  }) = _ContractConfigMap;

  factory ContractConfigMap.fromJson(Map<String, dynamic> json) => _$ContractConfigMapFromJson(json);
}

@freezed
class ConfigList with _$ConfigList {
  const factory ConfigList({
    int? id,
    int? interestRate,
    int? multiple,
  }) = _ConfigList;

  factory ConfigList.fromJson(Map<String, dynamic> json) => _$ConfigListFromJson(json);
}

@freezed
class RuleMap with _$RuleMap {
  const factory RuleMap({
    @JsonKey(name: "closeLossRadio") double? closeLossRadio,
    int? id,
    String? market,
    @JsonKey(name: "warnLossRadio") double? warnLossRadio,
  }) = _RuleMap;

  factory RuleMap.fromJson(Map<String, dynamic> json) => _$RuleMapFromJson(json);
}

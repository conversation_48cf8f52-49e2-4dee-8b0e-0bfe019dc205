import 'package:dio/dio.dart';
import 'package:gp_stock_app/features/contract/domain/models/contract_calculation/contract_calculation_model.dart';
import 'package:gp_stock_app/features/contract/domain/models/contract_withdrawal_config/contract_withdrawal_config.dart';
import 'package:injectable/injectable.dart';

import '../../../../core/api/network/endpoint/api_endpoints.dart';
import '../../../../core/api/network/models/result.dart';
import '../../../../core/api/network/network.dart';
import '../models/assets_records/assets_records.dart';
import '../models/contract_activity/contract_activity_response.dart';
import '../models/contract_application/contract_application.dart';
import '../models/contract_apply_records/contract_apply_records.dart';
import '../models/expand_margin/margin_call_response.dart';
import '../repository/contract_repository.dart';

@Injectable(as: ContractRepository)
class ContractService implements ContractRepository {
  @override
  Future<ResponseResult<ContractApplyRecords>> getContractApplyRecords({required int pageNum}) async {
    try {
      final Response response = await NetworkProvider().get(
        ApiEndpoints.getContractApplyRecords,
        isAuthRequired: true,
        queryParameters: {
          'pageNumber': pageNum,
          'pageSize': 20,
        },
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(
            data: ContractApplyRecords.fromJson(response.data['data']),
          );
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get contract apply records');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  @override
  Future<ResponseResult<ContractApplicationData>> getOrdinaryConfigInfo({required int type}) async {
    try {
      final Response response = await NetworkProvider().get(
        ApiEndpoints.getOrdinaryConfigInfo,
        isAuthRequired: true,
        queryParameters: {
          'type': type,
        },
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(data: ContractApplicationData.fromJson(response.data['data']));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get ordinary config info');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  @override
  Future<ResponseResult<ContractActivityData>> getContractActivity({required int type, required int parentType}) async {
    try {
      final Response response = await NetworkProvider().get(
        ApiEndpoints.getContractActivity,
        isAuthRequired: true,
        queryParameters: {
          'type': type,
          'parentType': parentType,
        },
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(data: ContractActivityData.fromJson(response.data['data']));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get contract activity');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  @override
  Future<ResponseResult<bool>> applyOrdinaryContract({
    required int applyAmount,
    required int contractConfigId,
    required int riskId,
    required int type,
  }) async {
    try {
      final Response response = await NetworkProvider().post(
        ApiEndpoints.applyOrdinaryContract,
        isAuthRequired: true,
        data: {
          'applyAmount': applyAmount,
          'contractConfigId': contractConfigId,
          'riskId': riskId,
          'type': type,
        },
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(data: true);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to apply ordinary contract');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  @override
  Future<ResponseResult<bool>> terminateContract({required String contractId}) async {
    try {
      final Response response = await NetworkProvider().post(
        ApiEndpoints.endContract,
        isAuthRequired: true,
        data: {
          'contractId': contractId,
        },
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(data: true);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to terminate contract');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  @override
  Future<ResponseResult<AssetsRecords>> getAssetRecord({
    required int? contractId,
    required int pageNum,
    required String? commentAssetId,
    String? fromType,
    String? startTime,
    String? endTime,
  }) async {
    try {
      final Response response = await NetworkProvider().get(
        ApiEndpoints.getAssetRecord,
        isAuthRequired: true,
        queryParameters: {
          'contractId': contractId,
          'pageNumber': pageNum,
          'pageSize': 20,

          if (commentAssetId != null && contractId == null) 'commentAssetId': commentAssetId,
          if (fromType != null) 'fromType': fromType,
          if (startTime != null) 'createTimeStart': startTime,
          if (endTime != null) 'createTimeEnd': endTime,
        }
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(data: AssetsRecords.fromJson(response.data));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get asset record');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  @override
  Future<ResponseResult<AssetsRecords>> getContractAccountRecord({
    required int? contractId,
    required int pageNum,
  }) async {
    try {
      final Response response = await NetworkProvider().get(
        ApiEndpoints.getContractAccountRecord,
        isAuthRequired: true,
        queryParameters: {
          'contractId': contractId,
          'pageNum': pageNum,
          'pageSize': 20,
        },
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(data: AssetsRecords.fromJson(response.data));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get contract account record');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  @override
  Future<ResponseResult<MarginCallResponse>> getMarginCall({required int contractId}) async {
    try {
      final Response response = await NetworkProvider().get(
        '${ApiEndpoints.getContractAccountDetail}/$contractId',
        isAuthRequired: true,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(
              data: MarginCallResponse.fromJson(response.data));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get margin call');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  @override
  Future<ResponseResult<bool>> addExpandMargin({
    required String contractId,
    required double applyAmount,
    required int type,
  }) async {
    try {
      final Response response = await NetworkProvider().post(
        ApiEndpoints.addExpandMargin,
        isAuthRequired: true,
        data: {
          'contractId': contractId,
          'applyAmount': applyAmount,
          'type': type,
        },
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(data: true);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to apply margin call');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  @override
  Future<ResponseResult<bool>> renewalContract({required String contractId}) async {
    try {
      final Response response = await NetworkProvider().post(
        ApiEndpoints.renewalContract,
        isAuthRequired: true,
        data: {
          'id': contractId,
        },
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(data: true);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to renewal contract');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  @override
  Future<ResponseResult<bool>> applyExperienceContract({
    required int activityId,
    required int activityRiskId,
    required double applyAmount,
    required int type,
  }) async {
    try {
      final Response response = await NetworkProvider().post(
        ApiEndpoints.applyExperienceContract,
        isAuthRequired: true,
        data: {
          'activityId': activityId,
          'activityRiskId': activityRiskId,
          'applyAmount': applyAmount,
          'type': type,
        },
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(data: true);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to apply experience contract');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  @override
  Future<ResponseResult<bool>> applyBonusContract({
    required int activityId,
    required int activityRiskId,
    required double applyAmount,
    required int type,
  }) async {
    try {
      final Response response = await NetworkProvider().post(
        ApiEndpoints.applyBonusContract,
        isAuthRequired: true,
        data: {
          'activityId': activityId,
          'activityRiskId': activityRiskId,
          'applyAmount': applyAmount,
          'type': type,
        },
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(data: true);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to apply bonus contract');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  @override
  Future<ResponseResult<bool>> withdraw({required String applyAmount, required String id}) async {
    try {
      final Response response = await NetworkProvider().post(
        ApiEndpoints.contractWithdraw,
        isAuthRequired: true,
        data: {
          'applyAmount': applyAmount,
          'id': id,
        },
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(data: true);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to withdraw');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  @override
  Future<ResponseResult<ContractWithdrawalConfig>> getWithdrawConfig({required String contractId}) async {
    try {
      final Response response = await NetworkProvider().get(
        '${ApiEndpoints.getWithdrawAmount}/$contractId',
        isAuthRequired: true,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(data: ContractWithdrawalConfig.fromJson(response.data['data']));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get withdrawal config');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  @override
  Future<ResponseResult<ContractCalculationModel>> getOrdinaryApplyAmount({
    required int contractConfigId,
    required int riskId,
    required int applyAmount,
    required int type,
  }) async {
    try {
      final Response response = await NetworkProvider().get(
        ApiEndpoints.getOrdinaryApplyAmount,
        isAuthRequired: true,
        queryParameters: {
          'contractConfigId': contractConfigId,
          'riskId': riskId,
          'applyAmount': applyAmount,
          'type': type,
        },
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(data: ContractCalculationModel.fromJson(response.data['data']));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get ordinary apply amount');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  @override
  Future<ResponseResult<ContractCalculationModel>> getBonusContractAmount({
    required int activityId,
    required int activityRiskId,
    required int applyAmount,
    required int type,
  }) async {
    try {
      final Response response = await NetworkProvider().get(
        ApiEndpoints.getBonusContractAmount,
        isAuthRequired: true,
        queryParameters: {
          'activityId': activityId,
          'activityRiskId': activityRiskId,
          'applyAmount': applyAmount,
          'type': type,
        },
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(data: ContractCalculationModel.fromJson(response.data['data']));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get bonus contract amount');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  @override
  Future<ResponseResult<List<int>>> getOpenContractType() async {
    try {
      final response = await NetworkProvider().get(ApiEndpoints.getOpenContractType, isAuthRequired: true);
      return ResponseResult(data: List<int>.from(response.data['data']));
    } catch (e) {
      return ResponseResult(error: e.toString());
    }
  }
}

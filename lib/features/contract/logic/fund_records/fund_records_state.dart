part of 'fund_records_cubit.dart';

class FundRecordsState extends Equatable {
  final DataStatus assetsRecordsFetchStatus;
  final AssetsRecords? assetsRecords;
  final String? error;
  final DateFilter? dateFilter;
  final TransactionType? transactionType;
  const FundRecordsState({
    this.assetsRecordsFetchStatus = DataStatus.idle,
    this.assetsRecords,
    this.dateFilter,
    this.transactionType,
    this.error,
  });

  @override
  List<Object?> get props => [assetsRecordsFetchStatus, assetsRecords, error, dateFilter, transactionType];

  FundRecordsState copyWith({
    DataStatus? assetsRecordsFetchStatus,
    AssetsRecords? assetsRecords,
    String? error,
    DateFilter? Function()? dateFilter,
    TransactionType? Function()? transactionType,
  }) {
    return FundRecordsState(
      assetsRecordsFetchStatus: assetsRecordsFetchStatus ?? this.assetsRecordsFetchStatus,
      assetsRecords: assetsRecords ?? this.assetsRecords,
      error: error ?? this.error,
      dateFilter: dateFilter != null ? dateFilter() : this.dateFilter,
      transactionType: transactionType != null ? transactionType() : this.transactionType,
    );
  }
}

part of 'contract_terminate_cubit.dart';

class ContractTerminateState extends Equatable {
  final DataStatus terminateStatus;
  final String? errorMessage;
  const ContractTerminateState({this.terminateStatus = DataStatus.idle, this.errorMessage});

  @override
  List<Object?> get props => [terminateStatus, errorMessage];

  ContractTerminateState copyWith({DataStatus? terminateStatus, String? errorMessage}) {
    return ContractTerminateState(
      terminateStatus: terminateStatus ?? this.terminateStatus,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}

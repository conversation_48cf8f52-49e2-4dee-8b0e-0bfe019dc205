import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:injectable/injectable.dart';

import '../../domain/repository/contract_repository.dart';

part 'contract_terminate_state.dart';

@injectable
class ContractTerminateCubit extends Cubit<ContractTerminateState> {
  final ContractRepository _contractService;
  ContractTerminateCubit(this._contractService) : super(const ContractTerminateState());

  Future<void> terminateContract({required String contractId}) async {
    emit(state.copyWith(terminateStatus: DataStatus.loading));
    final response = await _contractService.terminateContract(contractId: contractId);
    if (response.data == true) {
      emit(state.copyWith(terminateStatus: DataStatus.success));
    } else {
      emit(state.copyWith(terminateStatus: DataStatus.failed, errorMessage: response.error));
    }
  }
}

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/features/contract/domain/models/contract_withdrawal_config/contract_withdrawal_config.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:injectable/injectable.dart';

import '../../domain/repository/contract_repository.dart';

part 'contract_withdraw_state.dart';

@injectable
class ContractWithdrawCubit extends Cubit<ContractWithdrawState> {
  final ContractRepository _contractService;
  ContractWithdrawCubit(this._contractService) : super(const ContractWithdrawState());

  Future<void> withdrawContract({required String contractId, required String applyAmount}) async {
    emit(state.copyWith(withdrawStatus: DataStatus.loading));
    final response = await _contractService.withdraw(id: contractId, applyAmount: applyAmount);
    if (response.data == true) {
      emit(state.copyWith(withdrawStatus: DataStatus.success));
    } else {
      emit(state.copyWith(withdrawStatus: DataStatus.failed, errorMessage: response.error));
    }
  }

  Future<void> getWithdrawConfig({required String contractId}) async {
    emit(state.copyWith(withdrawAmountStatus: DataStatus.loading));
    final response = await _contractService.getWithdrawConfig(contractId: contractId);
    if (response.data != null) {
      emit(state.copyWith(withdrawAmountStatus: DataStatus.success, withdrawConfig: response.data));
    } else {
      emit(state.copyWith(
        withdrawAmountStatus: DataStatus.failed,
        errorMessage: response.error,
      ));
    }
  }
}

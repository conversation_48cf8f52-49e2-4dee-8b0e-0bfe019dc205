part of 'contract_withdraw_cubit.dart';

class ContractWithdrawState extends Equatable {
  final DataStatus withdrawStatus;
  final DataStatus withdrawAmountStatus;
  final String? errorMessage;
  final ContractWithdrawalConfig? withdrawConfig;

  const ContractWithdrawState({
    this.withdrawStatus = DataStatus.idle,
    this.withdrawAmountStatus = DataStatus.idle,
    this.errorMessage,
    this.withdrawConfig,
  });

  @override
  List<Object?> get props => [withdrawStatus, withdrawAmountStatus, errorMessage, withdrawConfig];

  ContractWithdrawState copyWith({
    DataStatus? withdrawStatus,
    DataStatus? withdrawAmountStatus,
    String? errorMessage,
    ContractWithdrawalConfig? withdrawConfig,
  }) {
    return ContractWithdrawState(
      withdrawStatus: withdrawStatus ?? this.withdrawStatus,
      withdrawAmountStatus: withdrawAmountStatus ?? this.withdrawAmountStatus,
      errorMessage: errorMessage ?? this.errorMessage,
      withdrawConfig: withdrawConfig ?? this.withdrawConfig,
    );
  }
}

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/features/contract/domain/models/contract_calculation/contract_calculation_model.dart';
import 'package:injectable/injectable.dart';

import '../../../../shared/constants/enums.dart';
import '../../domain/models/contract_application/contract_application.dart';
import '../../domain/models/contract_apply_records/contract_apply_records.dart';
import '../../domain/models/contract_model.dart';
import '../../domain/repository/contract_repository.dart';

part 'contract_state.dart';

@injectable
class ContractCubit extends Cubit<ContractState> {
  final ContractRepository _contractService;
  ContractCubit(this._contractService) : super(const ContractState());

  void getContractApplyRecords({
    bool isLoadMore = false,
  }) async {
    // Reset the page number to 1 if not loading more (e.g., during refresh or initial load)
    final pageNum = isLoadMore ? (state.contractApplyRecords?.current ?? 0) + 1 : 1;

    if (!isLoadMore) {
      emit(state.copyWith(
        contractApplyRecordsFetchStatus: DataStatus.loading,
        contractApplyRecords: const ContractApplyRecords(), // Reset records
      ));
    }

    try {
      final result = await _contractService.getContractApplyRecords(pageNum: pageNum);

      if (!result.isSuccess) {
        emit(
          state.copyWith(
            contractApplyRecordsFetchStatus: DataStatus.failed,
            error: result.error,
          ),
        );
        return;
      }

      final records =
          isLoadMore ? [...?state.contractApplyRecords?.records, ...?result.data?.records] : result.data?.records ?? [];

      emit(state.copyWith(
        contractApplyRecordsFetchStatus: DataStatus.success,
        contractApplyRecords: ContractApplyRecords(
          records: records,
          current: result.data?.current ?? 0,
          hasNext: result.data?.hasNext ?? false,
          total: result.data?.total ?? 0,
        ),
      ));
    } on Exception catch (e) {
      emit(state.copyWith(
        contractApplyRecordsFetchStatus: DataStatus.failed,
        error: e.toString(),
      ));
    }
  }

  void getConfigInfo(double exchangeRate, {required MainContractType contractType}) async {
    emit(state.copyWith(ordinaryConfigInfoFetchStatus: DataStatus.loading, contractType: contractType));
    try {
      final result = await _contractService.getOrdinaryConfigInfo(type: contractType.value);
      if (result.isSuccess) {
        final contractConfigMap = result.data?.contractConfigMap;
        if (contractConfigMap != null && contractConfigMap.isNotEmpty) {
          selectConfigList(contractConfigMap.first.configList?.first ?? ConfigList(), exchangeRate);
          selectContractConfig(contractConfigMap.first, exchangeRate);
          selectMarket(result.data?.ruleMap?.firstOrNull ?? RuleMap(), exchangeRate);
          selectAmount(
            result.data?.amountList?.firstOrNull?.applyAmount,
            exchangeRate,
          );
        }

        emit(state.copyWith(ordinaryConfigInfoFetchStatus: DataStatus.success, ordinaryConfigInfo: result.data));
      } else {
        emit(state.copyWith(ordinaryConfigInfoFetchStatus: DataStatus.failed, error: result.error));
      }
    } on Exception catch (e) {
      emit(state.copyWith(ordinaryConfigInfoFetchStatus: DataStatus.failed, error: e.toString()));
    }
  }

  String getPeriodTypeLabel(int periodType) {
    switch (periodType) {
      case 1:
        return 'perDay';
      case 2:
        return 'perWeek';
      case 3:
        return 'perMonth';
      default:
        return 'unknown';
    }
  }

  void selectContractConfig(ContractConfigMap contractConfig, double exchangeRate) {
    selectConfigList(contractConfig.configList?.first ?? ConfigList(), exchangeRate);
    emit(state.copyWith(selectedContractConfig: contractConfig));
  }

  void selectConfigList(ConfigList configList, double exchangeRate) {
    emit(state.copyWith(selectedConfigList: configList));
    calculateContractAmount(exchangeRate);
  }

  void selectMarket(RuleMap rule, double exchangeRate) {
    emit(state.copyWith(selectedMarket: rule));
    _updateCurrency(rule.market ?? '');
    calculateContractAmount(exchangeRate);
  }

  void selectAmount(int? amount, double exchangeRate) {
    emit(state.copyWith(selectedAmount: () => amount));
    calculateContractAmount(exchangeRate);
  }

  void calculateContractAmount(double exchangeRate) async {
    getOrdinaryApplyAmount(contractType: state.contractType);
    final amount = state.selectedAmount;
    try {
      final principal = amount?.toDouble() ?? 0;
      final multiple = state.selectedConfigList?.multiple ?? 1;
      // final bonusRatio = state.selectedConfigList?.interestRate ?? 0;

      final warnRatio = state.selectedMarket?.warnLossRadio ?? 0;
      final closeRatio = state.selectedMarket?.closeLossRadio ?? 0;

      // Check if there's a bonus ratio
      // double bonus = 0;
      double adjustedPrincipal = principal;
      // if (bonusRatio > 0) {
      //   bonus = principal * bonusRatio / 100;
      //   adjustedPrincipal = principal + bonus;
      // }

      // Calculate leverage amount
      final multipleAmount = adjustedPrincipal * multiple;

      // Total trading funds
      final total = multipleAmount + adjustedPrincipal;

      // Warning loss line
      final warn = multipleAmount + (adjustedPrincipal * warnRatio / 100);

      // Close loss line
      final close = multipleAmount + ((adjustedPrincipal * (100 - closeRatio) / 100));

      // Calculate interest
      final interestRate = state.selectedConfigList?.interestRate ?? 0;
      final percent = multipleAmount * interestRate / 100;

      final deductionAmount = (adjustedPrincipal + percent) / exchangeRate;

      // Update contract model amount
      final contractModelAmount = ContractModelAmount(
        totalTadingFunds: total,
        lossWarningLine: warn,
        lossFlatLine: close,
        interestRate: percent,
        deductionAmount: deductionAmount,
        actualAmount: adjustedPrincipal,
        intrestDeductionAmount: 0, //todo
      );

      emit(state.copyWith(contractModelAmount: contractModelAmount));
    } catch (e) {
      emit(state.copyWith(error: 'Error calculating contract amount: ${e.toString()}'));
    }
  }

  void updateIsAgree(bool isAgree) => emit(state.copyWith(isAgree: isAgree));

  void applyOrdinaryContract({required MainContractType contractType}) async {
    emit(state.copyWith(applyOrdinaryContractStatus: DataStatus.loading));
    try {
      final result = await _contractService.applyOrdinaryContract(
        applyAmount: state.selectedAmount ?? 0,
        contractConfigId: int.parse(state.selectedConfigList?.id.toString() ?? '0'),
        riskId: int.parse(state.selectedMarket?.id.toString() ?? '0'),
        type: contractType.value,
      );
      if (result.isSuccess) {
        emit(state.copyWith(applyOrdinaryContractStatus: DataStatus.success));
      } else {
        emit(state.copyWith(applyOrdinaryContractStatus: DataStatus.failed, error: result.error));
      }
    } on Exception catch (e) {
      emit(state.copyWith(applyOrdinaryContractStatus: DataStatus.failed, error: e.toString()));
    }
  }

  /// Updates currency based on the market type
  void _updateCurrency(String market) => emit(state.copyWith(
          currency: switch (market) {
        'CN' => 'CNY',
        'HK' => 'HKD',
        'US' => 'USD',
        _ => state.currency,
      }));

  Future<void> getOrdinaryApplyAmount({required MainContractType contractType}) async {
    if (!((state.selectedAmount ?? 0) > 0)) return;
    try {
      final result = await _contractService.getOrdinaryApplyAmount(
        contractConfigId: int.parse(state.selectedConfigList?.id.toString() ?? '0'),
        riskId: int.parse(state.selectedMarket?.id.toString() ?? '0'),
        applyAmount: state.selectedAmount ?? 0,
        // 1 for ordinary contract
        type: contractType.value,
      );

      if (result.isSuccess && result.data != null) {
        emit(state.copyWith(contractCalculation: result.data));
      } else {
        emit(state.copyWith(error: result.error));
      }
    } catch (e) {
      emit(state.copyWith(error: e.toString()));
    }
  }

  Future<void> getOpenContractType() async {
    emit(state.copyWith(openContractTypeFetchStatus: DataStatus.loading));
    try {
      final result = await _contractService.getOpenContractType();
      if (result.isSuccess) {
        emit(state.copyWith(
          openContractTypeFetchStatus: DataStatus.success,
          openContractTypes: result.data,
        ));
      } else {
        emit(state.copyWith(
          openContractTypeFetchStatus: DataStatus.failed,
          error: result.error,
        ));
      }
    } catch (e) {
      emit(state.copyWith(
        openContractTypeFetchStatus: DataStatus.failed,
        error: e.toString(),
      ));
    }
  }
}

part of 'contract_cubit.dart';

class ContractState extends Equatable {
  final ContractApplyRecords? contractApplyRecords;
  final DataStatus contractApplyRecordsFetchStatus;
  final ContractApplicationData? configInfo;
  final DataStatus configInfoFetchStatus;
  final DataStatus applyOrdinaryContractStatus;
  final ContractConfigMap? selectedContractConfig;
  final ConfigList? selectedConfigList;
  final int? selectedAmount;
  final ContractModelAmount? contractModelAmount;
  final RuleMap? selectedMarket;
  final String currency;
  final bool isAgree;
  final String? error;
  final ContractCalculationModel? contractCalculation;
  final MainContractType contractType;
  final DataStatus openContractTypeFetchStatus;
  final List<int> openContractTypes;

  const ContractState({
    this.contractApplyRecords,
    this.contractApplyRecordsFetchStatus = DataStatus.idle,
    this.configInfo,
    this.configInfoFetchStatus = DataStatus.idle,
    this.applyOrdinaryContractStatus = DataStatus.idle,
    this.selectedContractConfig,
    this.selectedConfigList,
    this.selectedAmount,
    this.contractModelAmount,
    this.selectedMarket,
    this.currency = 'CNY',
    this.error,
    this.isAgree = true,
    this.contractCalculation,
    this.contractType = MainContractType.stock,
    this.openContractTypeFetchStatus = DataStatus.idle,
    this.openContractTypes = const [],
  });

  ContractState copyWith({
    ContractApplyRecords? contractApplyRecords,
    DataStatus? contractApplyRecordsFetchStatus,
    ContractApplicationData? ordinaryConfigInfo,
    DataStatus? ordinaryConfigInfoFetchStatus,
    DataStatus? applyOrdinaryContractStatus,
    ContractConfigMap? selectedContractConfig,
    ConfigList? selectedConfigList,
    int? Function()? selectedAmount,
    RuleMap? selectedMarket,
    ContractModelAmount? contractModelAmount,
    String? currency,
    String? error,
    bool? isAgree,
    ContractCalculationModel? contractCalculation,
    MainContractType? contractType,
    DataStatus? openContractTypeFetchStatus,
    List<int>? openContractTypes,
  }) =>
      ContractState(
          contractApplyRecords: contractApplyRecords ?? this.contractApplyRecords,
          contractApplyRecordsFetchStatus: contractApplyRecordsFetchStatus ?? this.contractApplyRecordsFetchStatus,
          configInfo: ordinaryConfigInfo ?? configInfo,
          configInfoFetchStatus: ordinaryConfigInfoFetchStatus ?? configInfoFetchStatus,
          applyOrdinaryContractStatus: applyOrdinaryContractStatus ?? this.applyOrdinaryContractStatus,
          selectedContractConfig: selectedContractConfig ?? this.selectedContractConfig,
          selectedConfigList: selectedConfigList ?? this.selectedConfigList,
          selectedAmount: selectedAmount != null ? selectedAmount() : this.selectedAmount,
          contractModelAmount: contractModelAmount ?? this.contractModelAmount,
          selectedMarket: selectedMarket ?? this.selectedMarket,
          currency: currency ?? this.currency,
          isAgree: isAgree ?? this.isAgree,
          contractType: contractType ?? this.contractType,
          error: error ?? this.error,
          openContractTypeFetchStatus: openContractTypeFetchStatus ?? this.openContractTypeFetchStatus,
          openContractTypes: openContractTypes ?? this.openContractTypes,
          contractCalculation: contractCalculation ?? this.contractCalculation);

  @override
  List<Object?> get props => [
        contractApplyRecords,
        contractApplyRecordsFetchStatus,
        configInfo,
        configInfoFetchStatus,
        applyOrdinaryContractStatus,
        selectedContractConfig,
        selectedConfigList,
        selectedAmount,
        contractModelAmount,
        selectedMarket,
        currency,
        isAgree,
        error,
        contractCalculation,
        contractType,
        openContractTypeFetchStatus,
        openContractTypes,
      ];
}

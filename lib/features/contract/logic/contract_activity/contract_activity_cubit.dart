import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/features/contract/domain/models/contract_calculation/contract_calculation_model.dart';
import 'package:injectable/injectable.dart';

import '../../../../shared/constants/enums.dart';
import '../../domain/models/contract_activity/contract_activity_response.dart';
import '../../domain/models/contract_model.dart';
import '../../domain/repository/contract_repository.dart';

part 'contract_activity_state.dart';

/// Manages the state and business logic for contract activities
@injectable
class ContractActivityCubit extends Cubit<ContractActivityState> {
  final ContractRepository _contractRepository;

  ContractActivityCubit(this._contractRepository) : super(ContractActivityState());

  /// Fetches contract activity data based on the specified type
  Future<void> getContractActivity({required int type, required double exchangeRate, required int parentType}) async {
    emit(state.copyWith(contractActivityFetchStatus: DataStatus.loading));

    try {
      final response = await _contractRepository.getContractActivity(type: type, parentType: parentType);

      if (response.isSuccess && response.data != null) {
        final contractActivity = response.data!;
        emit(state.copyWith(
          contractActivity: contractActivity,
          contractActivityFetchStatus: DataStatus.success,
        ));

        // Select the first market from the activity risk map if available
        final firstMarket = contractActivity.activityRiskMap?.firstOrNull ?? ActivityRiskMap();
        selectMarket(firstMarket, exchangeRate);

        // Update amount list from the selected market
        // _updateAmountList(firstMarket);
        manageAmountList(contractActivity, exchangeRate);
      } else {
        emit(state.copyWith(
          contractActivityFetchStatus: DataStatus.failed,
          error: response.error,
        ));
      }
    } catch (e) {
      emit(state.copyWith(
        contractActivityFetchStatus: DataStatus.failed,
        error: e.toString(),
      ));
    }
  }

  /// Updates the amount list dropdown values based on contract activity data
  void manageAmountList(ContractActivityData response, double exchangeRate) {
    final amount = response.activityRiskMap?.first.giveAmount;
    final amountList = response.activityRiskMap?.first.applyAmountList;

    if (amountList == null || amountList.isEmpty) {
      if (amount != null) {
        selectAmount(amount.toInt(), exchangeRate);
      }
    } else {
      selectAmount(amountList.firstOrNull?.toInt(), exchangeRate);
    }
  }

  // /// Updates the amount list based on the selected market
  // void _updateAmountList(ActivityRiskMap market) {
  //   final amountList = market.applyAmountList;

  //   if (amountList != null) {
  //     final dropdownValues = amountList.map((e) => DropDownValue(id: e.toString(), value: e.toString())).toList();

  //     emit(state.copyWith(amountList: dropdownValues));
  //   }
  // }

  /// Selects a market and updates related state
  void selectMarket(ActivityRiskMap market, double exchangeRate) {
    emit(state.copyWith(selectedMarket: market));
    _updateCurrency(market.marketType ?? '');
    calculateContractAmount(exchangeRate);
  }

  /// Updates the agreement status
  void updateIsAgree(bool value) => emit(state.copyWith(isAgree: value));

  /// Selects an amount and recalculates contract amount
  void selectAmount(int? amount, double exchangeRate) {
    emit(state.copyWith(selectedAmount: amount));
    calculateContractAmount(exchangeRate);
  }

  double calculateGiftAmount(double currentAmount, ActivityRiskMap currentMarket) {
    final giveRatio = currentMarket.giveRatio;
    final giveAmount = currentMarket.giveAmount;
    if (giveAmount != null && giveAmount > 0) return giveAmount;
    return ((giveRatio ?? 0) * (currentAmount / 100));
  }

  /// Calculates contract amounts based on selected values
  void calculateContractAmount(double exchangeRate) {
    getBonusContractAmount();
    try {
      final amount = state.selectedAmount;
      double principal = amount?.toDouble() ?? 0.0;
      final market = state.selectedMarket;
      final contractActivity = state.contractActivity;

      if (market == null || contractActivity == null) return;
      final bonusRatio = market.giveRatio ?? 0;
      principal = principal + ((principal * bonusRatio) / 100);
      // Determine values based on contract type
      int multiple;
      double warnLossRadio;
      double closeLossRadio;
      double interestRate;

      // Experience contract type
      warnLossRadio = market.warnLossRadio ?? 0;
      closeLossRadio = market.closeLossRadio ?? 0;
      multiple = market.multiple ?? 0;
      interestRate = market.interestRate ?? 0;
      // Calculate base values
      final multipleAmount = principal * multiple;
      final total = multipleAmount + principal;
      final warnLine = multipleAmount + (principal * warnLossRadio / 100);
      final closeLine = multipleAmount + (principal * (100 - closeLossRadio) / 100);

      // Calculate interest
      final percent = multipleAmount * interestRate / 100;
      final interestCash = contractActivity.interestCash ?? 0;
      final interest = interestCash - percent;
      // Calculate payment amount (converted to base currency)

      final payAmount = ((amount ?? 0) + (interest > 0 ? 0 : percent)) / exchangeRate;

      // Create contract model amount
      final contractModelAmount = ContractModelAmount(
        totalTadingFunds: total,
        lossWarningLine: warnLine,
        lossFlatLine: closeLine,
        interestRate: percent,
        deductionAmount: payAmount,
        actualAmount: principal,
        intrestDeductionAmount: interest > 0 ? percent : 0,
        bonus: calculateGiftAmount(principal, market),
      );

      emit(state.copyWith(contractModelAmount: contractModelAmount));
    } catch (e) {
      emit(state.copyWith(
        error: 'Error calculating contract amount: ${e.toString()}',
      ));
    }
  }

  /// Updates currency based on the market type
  void _updateCurrency(String market) => emit(state.copyWith(
          currency: switch (market) {
        'CN' => 'CNY',
        'HK' => 'HKD',
        'US' => 'USD',
        _ => state.currency,
      }));

  /// Applies for an experience contract
  Future<void> applyExperienceContract() async {
    emit(state.copyWith(applyExperienceContractStatus: DataStatus.loading));

    try {
      final market = state.selectedMarket;

      if (market == null) {
        emit(state.copyWith(
          applyExperienceContractStatus: DataStatus.failed,
          error: 'No market selected',
        ));
        return;
      }

      final response = await _contractRepository.applyExperienceContract(
        activityId: market.activityId ?? 0,
        activityRiskId: market.riskId ?? 0,
        applyAmount: market.giveAmount ?? 0,
        type: state.contractType?.value ?? 0,
      );

      if (response.isSuccess) {
        emit(state.copyWith(
          applyExperienceContractStatus: DataStatus.success,
        ));
      } else {
        emit(state.copyWith(
          applyExperienceContractStatus: DataStatus.failed,
          error: response.error,
        ));
      }
    } catch (e) {
      emit(state.copyWith(
        applyExperienceContractStatus: DataStatus.failed,
        error: e.toString(),
      ));
    }
  }

  /// Applies for an bonus contract
  Future<void> applyBonusContract() async {
    emit(state.copyWith(applyBonusContractStatus: DataStatus.loading));

    try {
      final market = state.selectedMarket;

      if (market == null) {
        emit(state.copyWith(
          applyBonusContractStatus: DataStatus.failed,
          error: 'No market selected',
        ));
        return;
      }

      final response = await _contractRepository.applyBonusContract(
        activityId: market.activityId ?? 0,
        activityRiskId: market.riskId ?? 0,
        applyAmount: (state.selectedAmount ?? 0).toDouble(),
        type: state.contractType?.value ?? 0,
      );

      if (response.isSuccess) {
        emit(state.copyWith(
          applyBonusContractStatus: DataStatus.success,
        ));
      } else {
        emit(state.copyWith(
          applyBonusContractStatus: DataStatus.failed,
          error: response.error,
        ));
      }
    } catch (e) {
      emit(state.copyWith(
        applyBonusContractStatus: DataStatus.failed,
        error: e.toString(),
      ));
    }
  }

  void setContractType(MainContractType contractType, ContractType contractSubType) =>
      emit(state.copyWith(contractType: contractType, contractSubType: contractSubType));

  Future<void> getBonusContractAmount() async {
    if (state.contractSubType != ContractType.bonus) return;
    try {
      final market = state.selectedMarket;
      if (market == null) return;

      final result = await _contractRepository.getBonusContractAmount(
        activityId: market.activityId ?? 0,
        activityRiskId: market.riskId ?? 0,
        type: state.contractType?.value ?? 0,
        applyAmount: (state.selectedAmount ?? 0).toInt(),
      );

      if (result.isSuccess && result.data != null) {
        emit(state.copyWith(bonusContractCalculation: result.data));
      } else {
        emit(state.copyWith(error: result.error));
      }
    } catch (e) {
      emit(state.copyWith(error: e.toString()));
    }
  }
}

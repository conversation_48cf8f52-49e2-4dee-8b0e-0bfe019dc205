part of 'contract_activity_cubit.dart';

class ContractActivityState extends Equatable {
  final ContractActivityData? contractActivity;
  final DataStatus contractActivityFetchStatus;
  final DataStatus applyExperienceContractStatus;
  final DataStatus applyBonusContractStatus;
  final ActivityRiskMap? selectedMarket;
  final int? selectedAmount;
  final bool isAgree;
  final ContractModelAmount? contractModelAmount;
  final String? currency;
  final MainContractType? contractType;
  final ContractType? contractSubType;
  final ContractCalculationModel? bonusContractCalculation;

  final String error;
  const ContractActivityState({
    this.contractActivity,
    this.contractActivityFetchStatus = DataStatus.idle,
    this.applyExperienceContractStatus = DataStatus.idle,
    this.applyBonusContractStatus = DataStatus.idle,
    this.selectedMarket,
    this.selectedAmount,
    this.isAgree = true,
    this.error = '',
    this.contractModelAmount,
    this.contractType,
    this.currency,
    this.contractSubType,
    this.bonusContractCalculation,
  });

  @override
  List<Object?> get props => [
        contractActivity,
        contractActivityFetchStatus,
        applyExperienceContractStatus,
        applyBonusContractStatus,
        error,
        isAgree,
        selectedMarket,
        selectedAmount,
        contractModelAmount,
        currency,
        contractSubType,
        contractType,
        bonusContractCalculation
      ];

  ContractActivityState copyWith({
    ContractActivityData? contractActivity,
    DataStatus? contractActivityFetchStatus,
    DataStatus? applyExperienceContractStatus,
    DataStatus? applyBonusContractStatus,
    ActivityRiskMap? selectedMarket,
    int? selectedAmount,
    String? error,
    bool? isAgree,
    ContractModelAmount? contractModelAmount,
    String? currency,
    MainContractType? contractType,
    ContractCalculationModel? bonusContractCalculation,
    ContractType? contractSubType,
  }) =>
      ContractActivityState(
        contractActivity: contractActivity ?? this.contractActivity,
        contractActivityFetchStatus: contractActivityFetchStatus ?? this.contractActivityFetchStatus,
        applyExperienceContractStatus: applyExperienceContractStatus ?? this.applyExperienceContractStatus,
        applyBonusContractStatus: applyBonusContractStatus ?? this.applyBonusContractStatus,
        selectedMarket: selectedMarket ?? this.selectedMarket,
        selectedAmount: selectedAmount ?? this.selectedAmount,
        error: error ?? this.error,
        isAgree: isAgree ?? this.isAgree,
        contractModelAmount: contractModelAmount ?? this.contractModelAmount,
        currency: currency ?? this.currency,
        contractType: contractType ?? this.contractType,
        bonusContractCalculation: bonusContractCalculation ?? this.bonusContractCalculation,
        contractSubType: contractSubType ?? this.contractSubType,
      );
}

part of 'margin_call_cubit.dart';

class MarginCallState extends Equatable {
  final MarginCallResponse? marginCallResponse;
  final DataStatus marginCallStatus;
  final DataStatus applyMarginCallStatus;
  final int? selectedAmount;
  final ContractModelAmount? contractModelAmount;
  final ContractAction? contractType;
  final bool isAgree;
  final String? error;

  const MarginCallState(
      {this.marginCallResponse,
      this.marginCallStatus = DataStatus.idle,
      this.applyMarginCallStatus = DataStatus.idle,
      this.error,
      this.selectedAmount,
      this.contractModelAmount,
      this.contractType,
      this.isAgree = true});

  @override
  List<Object?> get props => [
        marginCallResponse,
        marginCallStatus,
        applyMarginCallStatus,
        error,
        selectedAmount,
        contractModelAmount,
        isAgree,
        contractType,
      ];

  MarginCallState copyWith({
    int? selectedAmount,
    MarginCallResponse? marginCallResponse,
    DataStatus? marginCallStatus,
    DataStatus? applyMarginCallStatus,
    String? error,
    bool? isAgree,
    ContractModelAmount? contractModelAmount,
    ContractAction? contractType,
  }) {
    return MarginCallState(
      selectedAmount: selectedAmount ?? this.selectedAmount,
      marginCallResponse: marginCallResponse ?? this.marginCallResponse,
      marginCallStatus: marginCallStatus ?? this.marginCallStatus,
      applyMarginCallStatus: applyMarginCallStatus ?? this.applyMarginCallStatus,
      error: error ?? this.error,
      isAgree: isAgree ?? this.isAgree,
      contractModelAmount: contractModelAmount ?? this.contractModelAmount,
      contractType: contractType ?? this.contractType,
    );
  }
}

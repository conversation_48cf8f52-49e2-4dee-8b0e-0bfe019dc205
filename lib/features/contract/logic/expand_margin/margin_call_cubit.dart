import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:injectable/injectable.dart';

import '../../../../shared/constants/enums.dart';
import '../../domain/models/contract_model.dart';
import '../../domain/models/expand_margin/margin_call_response.dart';
import '../../domain/repository/contract_repository.dart';

part 'margin_call_state.dart';

@injectable
class MarginCallCubit extends Cubit<MarginCallState> {
  final ContractRepository _contractService;
  MarginCallCubit(this._contractService) : super(const MarginCallState());

  Future<void> getMarginCall({required int contractId}) async {
    emit(state.copyWith(marginCallStatus: DataStatus.loading));
    try {
      final response = await _contractService.getMarginCall(contractId: contractId);
      if (response.data?.data != null) {
        emit(state.copyWith(marginCallStatus: DataStatus.success, marginCallResponse: response.data));
        List amounts = [];
        final isBonus = response.data?.data?.contractType == ContractType.bonus;
        if (isBonus) {
          amounts = response.data?.data?.bonusAmountList ?? [];
        } else {
          amounts = response.data?.data?.amountList ?? [];
        }
        if (amounts.isNotEmpty) {
          int? selectedAmount;
          if (isBonus) {
            selectedAmount = amounts.firstOrNull?.toInt();
          } else {
            selectedAmount = amounts.firstOrNull?.applyAmount?.toInt();
          }
          emit(state.copyWith(selectedAmount: selectedAmount));
          calculateContractAmount();
        } else {
          setSelectedAmount(1000);
        }
      } else {
        emit(state.copyWith(marginCallStatus: DataStatus.failed, error: response.error));
      }
    } catch (e) {
      emit(state.copyWith(marginCallStatus: DataStatus.failed, error: e.toString()));
    }
  }

  void setSelectedAmount(int? value) {
    if (value == null || value <= 0) return;
    emit(state.copyWith(selectedAmount: value));
    // if (contractType == Contract.marginExpand) {
    calculateContractAmount();
    // }
  }

  void setIsAgree(bool value) => emit(state.copyWith(isAgree: value));

  void calculateContractAmount() async {
    final amount = state.selectedAmount?.toDouble();
    try {
      final principal = amount ?? 0;
      final multiple = state.marginCallResponse?.data?.multiple ?? 1;

      double adjustedPrincipal = principal;

      // Calculate leverage amount
      final multipleAmount = adjustedPrincipal * multiple;

      // Total trading funds
      final total = multipleAmount + adjustedPrincipal;

      // Warning loss line
      final warnValue = state.marginCallResponse?.data?.warnValue ?? 0;
      final warn = ((principal * warnValue) / 100) + multipleAmount;

      // Close loss line
      final closeValue = state.marginCallResponse?.data?.closeValue ?? 0;
      final close = ((principal * (100 - closeValue)) / 100) + multipleAmount;

      // Calculate interest
      final interestRate = state.marginCallResponse?.data?.interestRate ?? 0;

      final percent = ((state.contractType == ContractAction.replenish
                  ? state.marginCallResponse?.data?.totalFinance ?? 0
                  : multipleAmount) *
              interestRate) /
          100;
      final deductionAmount = adjustedPrincipal + percent;

      // Update contract model amount
      final contractModelAmount = ContractModelAmount(
        totalTadingFunds: total,
        lossWarningLine: warn,
        lossFlatLine: close,
        interestRate: percent,
        deductionAmount: deductionAmount,
        actualAmount: adjustedPrincipal,
        intrestDeductionAmount: 0,
        multipleAmount: multipleAmount,
      );

      emit(state.copyWith(contractModelAmount: contractModelAmount));
    } catch (e) {
      emit(state.copyWith(error: 'Error calculating contract amount: ${e.toString()}'));
    }
  }

  Future<void> addExpandMargin(int contractType) async {
    emit(state.copyWith(applyMarginCallStatus: DataStatus.loading));
    try {
      final response = await _contractService.addExpandMargin(
        contractId: state.marginCallResponse?.data?.id?.toString() ?? '',
        applyAmount: state.selectedAmount?.toDouble() ?? 0,
        type: contractType,
      );

      if (response.data != null) {
        emit(state.copyWith(applyMarginCallStatus: DataStatus.success));
      } else {
        emit(state.copyWith(applyMarginCallStatus: DataStatus.failed, error: response.error));
      }
    } catch (e) {
      emit(state.copyWith(applyMarginCallStatus: DataStatus.failed, error: e.toString()));
    }
  }

  Future<void> renewalContract() async {
    emit(state.copyWith(applyMarginCallStatus: DataStatus.loading));
    try {
      final response =
          await _contractService.renewalContract(contractId: state.marginCallResponse?.data?.id?.toString() ?? '');

      if (response.data != null) {
        emit(state.copyWith(applyMarginCallStatus: DataStatus.success));
      } else {
        emit(state.copyWith(applyMarginCallStatus: DataStatus.failed, error: response.error));
      }
    } catch (e) {
      emit(state.copyWith(applyMarginCallStatus: DataStatus.failed, error: e.toString()));
    }
  }

  void setContractType(ContractAction contractType) => emit(state.copyWith(contractType: contractType));
}

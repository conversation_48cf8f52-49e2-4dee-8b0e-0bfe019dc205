import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';

class FutureFundingWidget extends StatelessWidget {
  const FutureFundingWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            const Color(0xFF4366DE),
            const Color(0xFF3150BD),
          ],
        ),
        borderRadius: BorderRadius.circular(8.gr),
        border: Border.all(
          color: Colors.transparent,
        ),
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 8.gw, vertical: 4.gh),
        child: Text(
          'futures.futuresFunding'.tr(),
          style: FontPalette.medium14.copyWith(
            color: Colors.white,
          ),
        ),
      ),
    );
  }
}

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_lucide/flutter_lucide.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/quotes/quotes_view.dart';
import 'package:gp_stock_app/features/account/utils/trading_utils.dart';
import 'package:gp_stock_app/features/account/widgets/account_market_table_trading.dart';
import 'package:gp_stock_app/features/main/domain/enums/navigation_item.dart';
import 'package:gp_stock_app/features/main/logic/main/main_cubit.dart';
import 'package:gp_stock_app/features/market/logic/market/market_cubit.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/models/instrument/instrument.dart';
import 'package:gp_stock_app/shared/routes/navigator.dart';
import 'package:gp_stock_app/shared/theme/my_color_scheme.dart';
import 'package:gp_stock_app/shared/widgets/flip_text.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';

import '../../../../core/dependency_injection/injectable.dart';
import '../../../../core/utils/utils.dart';
import '../../../../shared/models/market_depth/market_depth.dart';
import '../../../../shared/theme/font_pallette.dart';
import '../../../../shared/widgets/tab/tab_bar_widget.dart';
import '../../../market/domain/models/search/market_search.dart';
import '../../../market/domain/models/stock_reponse/stock_info_response.dart';
import '../../../market/logic/search/search_cubit.dart';
import '../../../market/market_search_screen.dart';
import '../../logic/account/account_cubit.dart' show AccountCubit;
import '../../logic/trading/trading_cubit.dart';
import 'widgets/stock_widget_header.dart';
import 'widgets/trading_form_section.dart';

class TradingCenterScreen extends StatefulWidget {
  final Instrument instrument;

  final int selectedIndex;
  final bool shouldNavigateToIndex;
  final bool isIndexTrading;

  const TradingCenterScreen({
    super.key,
    required this.instrument,
    this.selectedIndex = 1,
    this.shouldNavigateToIndex = false,
    this.isIndexTrading = false,
  });

  @override
  State<TradingCenterScreen> createState() => _TradingCenterScreenState();
}

class _TradingCenterScreenState extends State<TradingCenterScreen> {
  late int selectedIndex;
  late Instrument model;
  late bool isIndexTrading;

  @override
  void initState() {
    super.initState();
    selectedIndex = widget.selectedIndex;
    model = widget.instrument;
    isIndexTrading = widget.isIndexTrading;
    reloadData();
  }

  void reloadData() {
    context.read<AccountCubit>().getContractSummary();

    context.read<TradingCubit>()
      ..subscribeToMarketSymbols(instrument: model)
      ..setTradeType(TradeTabType.values[widget.selectedIndex])
      ..getStockInfo(model.instrument)
      ..getMarketDepth(model.instrument)
      ..getBrokerQueue(model.instrument)
      ..getMarketStatus(
        market: model.market,
        symbol: model.symbol,
        securityType: model.securityType,
      )
      ..getCalculateConfig(
        market: model.market,
        securityType: model.securityType,
        chargePackageId: null,
      )
      ..getCompanyInfo(
        market: model.market,
        symbol: model.symbol,
        securityType: model.securityType,
      )
      ..getTickList(
        instrument: model.instrument,
      )
      ..getDistFlow(
        market: model.market,
        symbol: model.symbol,
        securityType: model.securityType,
      );
    if (model.marketType.type == 'CN' || model.marketType.type == 'HK') {
      context.read<TradingCubit>().setStockWidgetCount(StockWidgetCount.five);
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: true,
      onPopInvokedWithResult: (didPop, result) {
        context.read<TradingCubit>().unsubscribeFromMarketSymbols(instrument: model);
        context.read<AccountCubit>().stopTradingCenterPolling();
      },
      child: Scaffold(
        appBar: AppBar(
          centerTitle: true,
          backgroundColor: myColorScheme(context).cardColor,
          surfaceTintColor: Colors.transparent,
          title: TabComponentBox(
            label1: 'trade'.tr(),
            label2: 'quotation'.tr(),
            selectTab: (index) {
              if (widget.isIndexTrading && index == 0) {
                // Navigate to the trade tab on bottom tabs if the index trading is selected
                context.read<MainCubit>().selectedNavigationItem(NavigationItem.trade);
                // Switch to Index tab inside market screenm
                context.read<MarketCubit>().updateMainHeaderTab(1);
                Navigator.popUntil(navigatorKey.currentContext!, (route) => route.isFirst);
                return;
              }
              // Change the tab index
              context.read<TradingCubit>().setTradeType(TradeTabType.values[index]);
            },
          ),
          actions: [
            //search icon button
            IconButton(
              onPressed: () => context.verifyAuth(
                () => Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => BlocProvider(
                      create: (context) => getIt<SearchCubit>(),
                      child: MarketSearchScreen(
                        onSelect: (MarketSearchData selected) {
                          isIndexTrading = selected.isIndex;
                          model = selected.getInstrument;
                          selectedIndex = TradeTabType.Trading.index;
                          reloadData();
                        },
                      ),
                    ),
                  ),
                ),
              ),
              icon: Icon(
                LucideIcons.search,
                size: 18.gsp,
              ),
            ),

            // IconButton(
            //   onPressed: () {
            //     context.read<MainCubit>().selectedNavigationItem(NavigationItem.trade);
            //     if (widget.shouldNavigateToIndex) {
            //       context.read<MarketCubit>().updateMainHeaderTab(1);
            //     } else {
            //       final market = model.market;
            //       context.read<MarketCubit>().updateMainHeaderTab(0);
            //       context.read<MarketCubit>().updateTodaysTab(TodaysTab.byMarket(market));
            //     }
            //     Navigator.popUntil(navigatorKey.currentContext!, (route) => route.isFirst);
            //   },
            //   icon: const Icon(LucideIcons.chart_no_axes_combined),
            // ),
          ],
        ),
        body: BlocSelector<TradingCubit, TradingState, TradeTabType>(
          selector: (state) => state.tradeType,
          builder: (context, state) {
            if (state == TradeTabType.Quotes) {
              return QuotesView(
                instrument: model,
                marketType: model.marketType,
                isIndexTrading: widget.isIndexTrading,
              );
            }
            return TradingCenterView(instrument: model);
          },
        ),
      ),
    );
  }
}

class TradingCenterView extends StatelessWidget {
  const TradingCenterView({
    super.key,
    required this.instrument,
  });

  final Instrument instrument;

  @override
  Widget build(BuildContext context) {
    final isIndexTrading = context.read<TradingCubit>().state.isIndexTrading;

    return SingleChildScrollView(
      padding: EdgeInsets.symmetric(horizontal: 14.gw, vertical: 8.gh),
      child: AnimationLimiter(
        child: Column(
          children: AnimationConfiguration.toStaggeredList(
            duration: const Duration(milliseconds: 600),
            childAnimationBuilder: (widget) => SlideAnimation(
              verticalOffset: 50.0,
              child: FadeInAnimation(child: widget),
            ),
            children: [
              Container(
                padding: EdgeInsets.symmetric(horizontal: 11.gw, vertical: 16.gh),
                decoration: BoxDecoration(
                  color: myColorScheme(context).cardColor,
                  borderRadius: BorderRadius.circular(10.gr),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (!isIndexTrading) ...[
                      StockPriceSection(mainMarketType: instrument.marketType),
                      StockWidget(mainMarketType: instrument.marketType),
                      8.verticalSpace,
                    ],
                    TradingFormSection(instrument: instrument),
                  ],
                ),
              ),
              16.verticalSpace,
              TradingDataTable(
                market: instrument.market,
                securityType: instrument.securityType,
                symbol: instrument.symbol,
                isIndexTrading: isIndexTrading,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class StockWidget extends StatelessWidget {
  final MainMarketType mainMarketType;

  const StockWidget({super.key, required this.mainMarketType});

  @override
  Widget build(BuildContext context) {
    return BlocSelector<TradingCubit, TradingState, (DataStatus, MarketDepthData?, StockWidgetCount)>(
      selector: (state) => (state.marketDepthStatus, state.marketDepthConstant, state.stockWidgetCount),
      builder: (context, state) {
        if (state.$1 == DataStatus.loading) {
          return ShimmerWidget(height: 80.gh);
        }
        if (state.$1 == DataStatus.failed) {
          return const Center(child: Icon(Icons.error));
        }
        return AnimationLimiter(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  Expanded(
                    child: buildList('buy', state.$2?.ask ?? [], state.$3),
                  ),
                  Expanded(
                    child: buildList('sell', state.$2?.bid ?? [], state.$3),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget buildList(String type, List<Ask> data, StockWidgetCount stockWidgetCount) {
    return Builder(
      builder: (context) {
        final isBuy = type == 'buy';
        final itemCount = switch (stockWidgetCount) {
          StockWidgetCount.five => 5,
          StockWidgetCount.one => 1,
        };

        return AnimationLimiter(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: List.generate(
              // itemCount,
              data.length >= itemCount ? itemCount : data.length,
              (index) => _buildListItem(context, data[index], index, isBuy),
            ),
          ),
        );
      },
    );
  }

  Widget _buildListItem(BuildContext context, Ask item, int index, bool isBuy) {
    final itemBg = isBuy
        ? myColorScheme(context).greenColor.withValues(alpha: 0.1)
        : myColorScheme(context).redColor.withValues(alpha: 0.1);
    final serialBg = isBuy ? myColorScheme(context).greenColor : myColorScheme(context).redColor;
    final textColor = isBuy ? myColorScheme(context).greenColor : myColorScheme(context).redColor;

    return AnimationConfiguration.staggeredList(
      position: index,
      duration: const Duration(milliseconds: 300),
      child: SlideAnimation(
        verticalOffset: 20.0,
        child: FadeInAnimation(
          child: Container(
            height: 30.gh,
            color: itemBg,
            padding: EdgeInsets.symmetric(horizontal: 16.gw),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  height: 18.gh,
                  width: 18.gw,
                  decoration: BoxDecoration(
                    color: serialBg,
                    borderRadius: BorderRadius.circular(2.gr),
                  ),
                  child: Center(
                    child: Text(
                      item.depthNo.toString(),
                      style: FontPalette.semiBold12.copyWith(
                        color: myColorScheme(context).cardColor,
                        fontFamily: 'Akzidenz-Grotesk',
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: Align(
                    alignment: Alignment.centerRight,
                    child: Text(
                      item.price.toString(),
                      style: FontPalette.semiBold12.copyWith(
                        color: textColor,
                        fontFamily: 'Akzidenz-Grotesk',
                      ),
                    ),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Align(
                    alignment: Alignment.centerRight,
                    child: Text(
                      '${formatNumberWithChineseUnits((item.vol ?? 0.0).toDouble() / 100)} (${item.no ?? '-'})',
                      style: FontPalette.semiBold12.copyWith(
                        fontFamily: 'Akzidenz-Grotesk',
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class StockPriceSection extends StatelessWidget {
  final MainMarketType mainMarketType;

  const StockPriceSection({super.key, required this.mainMarketType});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<TradingCubit, TradingState>(
      builder: (context, state) {
        if (state.stockInfoConstant == null || state.marketDepthConstant == null) {
          return ShimmerWidget(height: 100.gh);
        }
        return AnimationLimiter(
          child: Column(
              children: AnimationConfiguration.toStaggeredList(
            duration: const Duration(milliseconds: 400),
            childAnimationBuilder: (widget) => SlideAnimation(
              horizontalOffset: 50.0,
              child: FadeInAnimation(
                child: widget,
              ),
            ),
            children: [
              _buildStockPriceSection(context, stock: state.stockInfoConstant),
              16.verticalSpace,
              // Buy/Sell Distribution Bar
              _buildBuySellDistribution(
                stockInfo: state.stockInfoConstant,
                marketDepth: state.marketDepthConstant,
                mainMarketType: mainMarketType,
                context: context,
              ),
            ],
          )),
        );
      },
    );
  }

  Widget _buildStockPriceSection(BuildContext context, {required StockInfoData? stock}) {
    final chg = TradingUtils.formatNumber(stock?.chg);
    final gain = TradingUtils.formatPercentage((stock?.gain ?? 0) * 100, isPercent: false, decimalPlaces: 3);
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                width: 0.7.gsw,
                child: Text(
                  '${stock?.name} (${stock?.symbol})',
                  style: FontPalette.semiBold15,
                ),
              ),
              SizedBox(height: 4),
              Row(
                children: [
                  FlipText(
                    stock?.latestPrice ?? 0,
                    style: SecFontPalette.bold24.copyWith(
                      color: (stock?.gain ?? 0).getValueColor(context),
                    ),
                    fractionDigits: 3,
                  ),
                  Icon(
                    (stock?.gain ?? 0) >= 0 ? Icons.arrow_drop_up : Icons.arrow_drop_down,
                    color: (stock?.gain ?? 0).getValueColor(context),
                  ),
                ],
              ),
              Text(
                "${TradingUtils.getSign(stock?.gain)}$chg  ${TradingUtils.getSign(stock?.gain)}$gain %",
                style: SecFontPalette.bold14.copyWith(
                  color: stock?.gain?.getValueColor(context),
                ),
              ),
            ],
          ),
        ),
        TextButton(
          onPressed: () => context.read<TradingCubit>().setTradeType(TradeTabType.Quotes),
          child: Text(
            '详情',
            style: FontPalette.normal14.copyWith(color: myColorScheme(context).primaryColor),
            textAlign: TextAlign.end,
          ),
        ),
      ],
    );
  }

  Widget _buildBuySellDistribution(
      {required StockInfoData? stockInfo,
      required MarketDepthData? marketDepth,
      required MainMarketType mainMarketType,
      required BuildContext context}) {
    final buy = marketDepth?.bid?.first.vol ?? 0;
    final sell = marketDepth?.ask?.first.vol ?? 0;
    double buyPer = 0.0;
    double sellPer = 0.0;
    int total = buy + sell;

    if (total > 0) {
      buyPer = (buy / total) * 100;
      sellPer = (sell / total) * 100;
    } else {
      buyPer = 0.0;
      sellPer = 0.0;
    }

    return Column(
      spacing: 5.gh,
      children: [
        StockWidgetHeader(
          mainMarketType: mainMarketType,
          position: RelativeRect.fromLTRB(
            MediaQuery.of(context).size.width - 50,
            .3.gsh,
            60.gw,
            0,
          ),
        ),
        Row(
          children: [
            Expanded(
                flex: 12,
                child: Text(
                  '${sellPer.toStringAsFixed(2)}%',
                  style: FontPalette.semiBold10
                      .copyWith(color: myColorScheme(context).greenColor, fontFamily: 'Akzidenz-Grotesk'),
                )),
            Expanded(
              flex: 90,
              child: Container(
                height: 10.gh,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(3),
                ),
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    return Row(
                      children: [
                        Expanded(
                          flex: sellPer.round(),
                          child: Container(
                            height: 10.gh,
                            decoration: BoxDecoration(
                              color: myColorScheme(context).greenColor,
                              borderRadius: BorderRadius.horizontal(
                                left: Radius.circular(10),
                                right: sellPer == 1 ? Radius.circular(10) : Radius.zero,
                              ),
                            ),
                          ),
                        ),
                        Expanded(
                          flex: buyPer.round(),
                          child: Container(
                            height: 10.gh,
                            decoration: BoxDecoration(
                              color: myColorScheme(context).redColor,
                              borderRadius: BorderRadius.horizontal(
                                right: Radius.circular(10),
                                left: buyPer == 1 ? Radius.circular(10) : Radius.zero,
                              ),
                            ),
                          ),
                        ),
                      ],
                    );
                  },
                ),
              ),
            ),
            5.horizontalSpace,
            Expanded(
                flex: 12,
                child: Text(
                  '${buyPer.toStringAsFixed(2)}%',
                  style: FontPalette.semiBold10
                      .copyWith(color: myColorScheme(context).redColor, fontFamily: 'Akzidenz-Grotesk'),
                )),
          ],
        ),
      ],
    );
  }
}

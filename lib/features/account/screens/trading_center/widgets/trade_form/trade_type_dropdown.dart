import 'package:collection/collection.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/domain/models/account_summary/contract_summary_response.dart';
import 'package:gp_stock_app/features/account/logic/account/account_cubit.dart';
import 'package:gp_stock_app/features/account/logic/trading/trading_cubit.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/logic/account_info/account_info_cubit.dart';
import 'package:gp_stock_app/shared/models/dropdown/dropdown_value.dart';
import 'package:gp_stock_app/shared/models/instrument/instrument.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:gp_stock_app/shared/theme/my_color_scheme.dart';
import 'package:gp_stock_app/shared/widgets/dropdown/common_dropdown.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';

class TradeTypeDropdown extends StatelessWidget {
  /// A dropdown widget that allows users to select trading methods based on market type and trading conditions.
  ///
  /// This widget is responsible for:
  /// - Displaying available trading methods based on market type and index trading status
  /// - Managing the selection of trading contracts
  /// - Updating balance on which trading will be performed
  /// - Refreshing order lists when trading method changes
  ///
  /// The widget uses [BlocSelector] to efficiently manage state updates from both
  /// [AccountCubit] and [TradingCubit].
  /// [AccountCubit] is responsible for listing the contracts available for trading.
  /// Required parameters:
  /// - [instrument]: The instrument for which the trading type is being selected
  const TradeTypeDropdown({
    super.key,
    required this.instrument,
  });

  final Instrument instrument;

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: BlocSelector<AccountCubit, AccountState, (ContractSummaryPage?, DataStatus)>(
        selector: (state) => (state.contractSummary, state.contractSummaryFetchStatus),
        builder: (context, accountState) {
          return BlocSelector<TradingCubit, TradingState,
              ({ContractSummaryData? contract, String? tradeType, bool isIndexTrading})>(
            selector: (state) =>
                (contract: state.contract, tradeType: state.tradingType, isIndexTrading: state.isIndexTrading),
            builder: (context, state) {
              final (contractSummary, fetchStatus) = accountState;

              if (!state.isIndexTrading && fetchStatus == DataStatus.loading && contractSummary == null) {
                return ShimmerWidget(
                  height: 35.gh,
                  width: double.infinity,
                  radius: 5.gr,
                  color: myColorScheme(context).textFillColor,
                );
              }
              // Get available trading methods based on market type and index trading status
              final dropDownValue =
                  context.read<AccountCubit>().getContractsByMarketType(instrument.marketType, state.isIndexTrading);

              // Determine selected trading method based on trading mode
              DropDownValue? selectedItem = _getSelectedTradeType(
                state.isIndexTrading,
                dropDownValue,
                state.tradeType,
                context,
              );

              return CommonDropdown(
                height: 35.gh,
                showSearchBox: false,
                isEnabled: !state.isIndexTrading && fetchStatus != DataStatus.loading,
                selectedItem: selectedItem,
                dropDownValue: dropDownValue,
                onChanged: (value) => _handleTradeTypeChange(
                  context: context,
                  value: value,
                  instrument: instrument,
                ),
                hintText: 'trading_methods'.tr(),
                borderRadius: 5.gr,
                textStyle: FontPalette.normal13.copyWith(color: myColorScheme(context).titleColor),
              );
            },
          );
        },
      ),
    );
  }

  /// Determines the selected trade type based on trading mode and available options
  /// If index trading is enabled, only spot trading is available and selected.
  /// Otherwise, the first available option is selected.
  DropDownValue? _getSelectedTradeType(
    bool isIndexTrading,
    List<DropDownValue> dropDownValue,
    String? tradeType,
    BuildContext context,
  ) {
    if (isIndexTrading) {
      final selectedItem = dropDownValue.firstOrNull;
      context.read<TradingCubit>().setTradingType(selectedItem?.value ?? '');
      return selectedItem;
    }
    return dropDownValue.firstWhereOrNull((element) => element.value == tradeType);
  }

  /// Handles trade type selection changes and updates related states
  void _handleTradeTypeChange({
    required BuildContext context,
    required DropDownValue value,
    required Instrument instrument,
  }) {
    // Update trading type in TradingCubit
    context.read<TradingCubit>().setTradingType(value.value ?? '');

    // Find selected contract from contract list
    final contract = context
        .read<AccountCubit>()
        .state
        .contractSummary
        ?.records
        ?.firstWhereOrNull((element) => element.id == int.tryParse(value.id ?? ''));

    // Get balance information for contract based trading
    final contractBalance = contract?.useAmount;
    // Get account balance for spot trading
    final accountBalance = context.read<AccountInfoCubit>().state.accountInfo?.usableCash ?? 0.00;

    // Update trading state with new contract information
    context.read<TradingCubit>()
      // if contract is selected, use contract balance, else use account balance
      ..setAccountBalance(contractBalance ?? accountBalance)
      ..setContractId(int.tryParse(value.id ?? ''))
      ..setContract(contract);

    // Refresh order lists for current positions and order details
    _refreshOrderLists(
      context: context,
      symbol: instrument.symbol,
      market: instrument.market,
      securityType: instrument.securityType,
      contractId: int.tryParse(value.id ?? ''),
    );
  }

  /// Refreshes order lists for both current positions and order details
  void _refreshOrderLists({
    required BuildContext context,
    required String symbol,
    required String market,
    required String securityType,
    int? contractId,
  }) {
    context.read<AccountCubit>()
      ..getOrderList(
        AccountMarketType.currentPositions,
        symbol: symbol,
        market: market,
        securityType: securityType,
        contractId: contractId,
      )
      ..getOrderList(
        AccountMarketType.orderDetails,
        symbol: symbol,
        market: market,
        securityType: securityType,
        contractId: contractId,
      );
  }
}

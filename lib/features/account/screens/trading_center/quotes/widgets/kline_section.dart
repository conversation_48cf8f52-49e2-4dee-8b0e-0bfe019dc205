import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/log.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/domain/constants/kline_constants.dart';
import 'package:gp_stock_app/features/account/domain/models/kline_option.dart';
import 'package:gp_stock_app/features/account/logic/trading/trading_cubit.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/quotes/widgets/kline_selector.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/quotes/widgets/tick_list_section.dart';
import 'package:gp_stock_app/features/market/domain/models/stock_kline_data.dart';
import 'package:gp_stock_app/features/market/logic/market_status/market_status_cubit.dart';
import 'package:gp_stock_app/shared/app/extension/time_zone.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/constants/web_socket_actions.dart';
import 'package:gp_stock_app/shared/constants/web_socket_types.dart';
import 'package:gp_stock_app/shared/mixin/web_socket_mixin.dart';
import 'package:gp_stock_app/shared/models/instrument/instrument.dart';
import 'package:gp_stock_app/shared/models/web_scoket_message.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';
import 'package:k_chart_plus/k_chart_plus.dart';

import '../../../../../../shared/theme/my_color_scheme.dart';

class KlineSection extends StatefulWidget {
  const KlineSection({super.key, required this.instrument, this.showTicks = false});

  final Instrument instrument;
  final bool showTicks;

  @override
  State<KlineSection> createState() => _KlineSectionState();
}

class _KlineSectionState extends State<KlineSection> with WebSocketMixin {
  StreamSubscription<WebSocketMessage>? _marketSubscription;
  bool isInitial = true;
  Future<List<KLineEntity>> _processData(List<KlineItem>? list, bool isLine) async {
    if (list == null) return [];

    List<KLineEntity> klineEntities = [];
    double? previousPrice;
    //! check here
    int offset = countryTimeOffsets['CN'] ?? 0;

    for (var item in list) {
      final openPrice = previousPrice ?? (isLine ? item.price ?? 0 : item.open ?? 0);

      final adjustedTime = DateTime.fromMillisecondsSinceEpoch(
        (item.time ?? 0) * 1000,
        isUtc: true,
      ).add(Duration(hours: offset)).millisecondsSinceEpoch;

      final klineEntity = KLineEntity.fromCustom(
        time: adjustedTime,
        close: isLine ? item.price ?? 0 : item.close ?? 0,
        open: openPrice,
        high: isLine ? item.price ?? 0 : item.high ?? 0,
        low: isLine ? item.price ?? 0 : item.low ?? 0,
        vol: item.volume ?? 0,
        amount: item.price ?? 0,
      );

      klineEntities.add(klineEntity);
      previousPrice = item.price ?? 0;
    }

    DataUtil.calculate(klineEntities);
    return klineEntities;
  }

  void _listenToSocketUpdates() {
    // Listen for market data updates
    _marketSubscription = onMessage(SocketEvents.market).listen(_handleMarketUpdate);

    // Subscribe to timeline data for this instrument
    webSocketService.send({
      'type': SocketEvents.market,
      'action': SocketActions.timeLine,
      'params': {
        'instrument': widget.instrument.instrument,
        'period': 'day',
        'operate': 'subscribe',
      }
    });
  }

  void _handleMarketUpdate(WebSocketMessage message) {
    // Validate response code
    if (message.data['code'] != 200) return;

    // Parse message data
    final stockMarketUpdate = StockKlineResponse.fromJson(message.data);
    logDev("Socket message data: ${message.data}", "KlineSection");
    logDev("Parsed stockMarketUpdate: $stockMarketUpdate", "KlineSection");
    logDev("stockMarketUpdate.data: ${stockMarketUpdate.data}", "KlineSection");
    if (stockMarketUpdate.data == null) return;

    // Check if this update is for our instrument
    final isMatchingInstrument = stockMarketUpdate.data?.detail?.instrument == widget.instrument.instrument;
    logDev(
        "Instrument check: ${stockMarketUpdate.data?.detail?.instrument} == ${widget.instrument.instrument} = $isMatchingInstrument",
        "KlineSection");
    if (!isMatchingInstrument) return;

    // Check if market is open
    final isMarketOpen = getIt<MarketStatusCubit>().isMarketOpen(stockMarketUpdate.data?.detail?.market);
    logDev("Market status check: ${stockMarketUpdate.data?.detail?.market} isOpen = $isMarketOpen", "KlineSection");
    if (!isMarketOpen) return;

    final tradingCubit = context.read<TradingCubit>();
    final currentKlineData = tradingCubit.state.klineDetailList;

    if (currentKlineData != null && currentKlineData.data != null) {
      // Create updated kline data by merging existing data with new data
      final updatedKlineData = currentKlineData.copyWith(
        data: currentKlineData.data!.copyWith(
          list: [
            ...?currentKlineData.data!.list?.where((e) => e.time != stockMarketUpdate.data?.list?.last.time),
            ...?stockMarketUpdate.data?.list
          ],
          detail: stockMarketUpdate.data?.detail,
        ),
      );

      if (mounted) {
        tradingCubit.updateKlineDetailList(updatedKlineData);
      }
    }
  }

  @override
  void initState() {
    super.initState();
    context.read<TradingCubit>().getKlineDetailList(widget.instrument.instrument, KlineConstants.options[0]);
    _listenToSocketUpdates();
  }

  @override
  void dispose() {
    _marketSubscription?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: myColorScheme(context).cardColor,
      child: Column(
        children: [
          KlineSelector(instrument: widget.instrument.instrument),
          BlocSelector<TradingCubit, TradingState, (DataStatus, StockKlineResponse?, KlineOption?)>(
            selector: (state) => (state.klineDetailListStatus, state.klineDetailList, state.klineOption),
            builder: (context, state) {
              if (state.$1 == DataStatus.loading && state.$2?.data?.list == null) {
                return ShimmerWidget(height: 180.gh);
              }
              if (state.$1 == DataStatus.failed || state.$2?.data?.list == null) {
                return Center(child: Icon(Icons.error));
              }
              final isLine = state.$3?.type == "timeLine";
              final showTicks = state.$3?.id == "intraday" && widget.showTicks;
              final scaleX = switch (state.$3?.id) {
                "weekly-kline" => 0.5,
                "monthly-kline" => 0.5,
                "yearly-kline" => 1.0,
                "intraday" => 0.15,
                "5day" => 0.03,
                _ => 0.8,
              };

              return Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.fromLTRB(8, 0, 8, 4),
                      child: FutureBuilder<List<KLineEntity>>(
                        future: _processData(state.$2?.data?.list, isLine),
                        builder: (context, snapshot) {
                          if (snapshot.connectionState == ConnectionState.waiting && isInitial) {
                            return ShimmerWidget(height: 180.gh);
                          }
                          if (snapshot.hasError || !snapshot.hasData || snapshot.data!.isEmpty) {
                            return Center(child: Icon(Icons.error, color: Colors.red));
                          }
                          isInitial = false;
                          final closePrice = state.$2?.data?.detail?.close;

                          return Stack(
                            children: [
                              Padding(
                                padding: const EdgeInsets.fromLTRB(0, 10, 0, 0),
                                child: KChartWidget(
                                  snapshot.data!,
                                  ChartStyle(),
                                  ChartColors(
                                    upColor: const Color(0xFFD2544F),
                                    dnColor: const Color(0xFF5DAF78),
                                    gridColor: Colors.transparent,
                                    bgColor: myColorScheme(context).cardColor,
                                  ),
                                  getColorCallback: (value) => value.getValueColor(context),
                                  mBaseHeight: 0.27.gsh,
                                  isTrendLine: false,
                                  scaleX: scaleX,
                                  mainState: MainState.MA,
                                  volHidden: false,
                                  isTapShowInfoDialog: true,
                                  secondaryStateLi: {},
                                  timeFormat: TimeFormat.YEAR_MONTH_DAY_WITH_HOUR,
                                  verticalTextAlignment: VerticalTextAlignment.right,
                                  isLine: isLine,
                                  xFrontPadding: 0,
                                  closePrice: closePrice,
                                  showDate: !showTicks,
                                  locale: context.locale.languageCode,
                                  isMarketOpen: getIt<MarketStatusCubit>().isMarketOpen(state.$2?.data?.detail?.market),
                                ),
                              ),
                              if (state.$1 == DataStatus.loading)
                                Opacity(
                                  opacity: 0.8,
                                  child: ShimmerWidget(height: 0.34.gsh),
                                ),
                            ],
                          );
                        },
                      ),
                    ),
                  ),
                  if (showTicks)
                    TickListSection(
                        instrument: widget.instrument, getColorCallback: (value) => value.getValueColor(context)),
                ],
              );
            },
          ),
        ],
      ),
    );
  }
}

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/logic/account_info/account_info_cubit.dart';
import 'package:gp_stock_app/shared/logic/account_info/account_info_state.dart';
import 'package:gp_stock_app/shared/logic/selected_exchange_cubit/selected_exchange_cubit.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:gp_stock_app/shared/theme/my_color_scheme.dart';
import 'package:gp_stock_app/shared/widgets/pagination/common_refresher.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shimmer/shimmer.dart';

import '../../../shared/constants/enums.dart';
import '../../../shared/routes/routes.dart';
import '../../market/widgets/market_table_header.dart';
import '../domain/models/account_info/account_info_response.dart';
import '../domain/models/account_summary/contract_summary_response.dart';
import '../logic/account/account_cubit.dart';
import '../widgets/action_buttons.dart';
import '../widgets/asset_section.dart';
import '../widgets/contracts/contract_tile.dart';
import '../widgets/data_field.dart';
import '../widgets/spot_account/detail_header.dart';
import '../widgets/table_empty.dart';

/// 合约
class AccountScreen extends StatelessWidget {
  const AccountScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: myColorScheme(context).backgroundColor,
      body: BlocBuilder<AccountCubit, AccountState>(
        builder: (context, state) => Column(
          children: [
            const _Header(),
            const _TableTabs(),
            if (state.selectedAccountTableType == AccountTableType.ACCOUNT_INFO)
              const Expanded(child: _AccountDetails())
            else if (state.selectedAccountTableType == AccountTableType.CONTRACT_SUMMARY) ...[
              const Expanded(child: _ContractSummary()),
              SizedBox(height: 100.gh)
            ]
          ],
        ),
      ),
    );
  }
}

class _Header extends StatefulWidget {
  const _Header();

  @override
  State<_Header> createState() => _HeaderState();
}

class _HeaderState extends State<_Header> with SingleTickerProviderStateMixin {
  late final AnimationController _controller;
  late final Animation<double> _fadeAnimation;
  late final Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );

    _scaleAnimation = Tween<double>(begin: 0.9, end: 1.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: myColorScheme(context).cardColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withNewOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        borderRadius: BorderRadius.vertical(bottom: Radius.circular(8.gr)),
      ),
      padding: EdgeInsets.only(bottom: 16.gh),
      child: BlocSelector<AccountCubit, AccountState, AccountTableType>(
        selector: (state) => state.selectedAccountTableType,
        builder: (context, accountType) => FadeTransition(
          opacity: _fadeAnimation,
          child: ScaleTransition(
            scale: _scaleAnimation,
            child: Column(
              children: [
                const AssetsSection(),
                if (accountType == AccountTableType.ACCOUNT_INFO) ActionButtonsRowType1() else ActionButtonsRowType2(),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class _TableTabs extends StatefulWidget {
  const _TableTabs();

  @override
  State<_TableTabs> createState() => _TableTabsState();
}

class _TableTabsState extends State<_TableTabs> with SingleTickerProviderStateMixin {
  late final AnimationController _controller;
  late final Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    _scaleAnimation = Tween<double>(begin: 0.9, end: 1.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(left: 16.gw, right: 16.gw, top: 20.gh),
      child: BlocConsumer<AccountCubit, AccountState>(
        listenWhen: (previous, current) => previous.selectedAccountTableType != current.selectedAccountTableType,
        listener: (_, __) => _controller.forward(from: 0),
        builder: (context, state) => Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ScaleTransition(
              scale: _scaleAnimation,
              child: MarketTableHeader(
                title: 'contractAccount'.tr(),
                isSelected: state.selectedAccountTableType == AccountTableType.CONTRACT_SUMMARY,
                onTap: () => context.read<AccountCubit>().updateAccountTableType(AccountTableType.CONTRACT_SUMMARY),
                disableScaleAnimation: true,
              ),
            ),
            15.horizontalSpace,
            ScaleTransition(
              scale: _scaleAnimation,
              child: MarketTableHeader(
                title: 'spotAccount'.tr(),
                isSelected: state.selectedAccountTableType == AccountTableType.ACCOUNT_INFO,
                onTap: () => context.read<AccountCubit>().updateAccountTableType(AccountTableType.ACCOUNT_INFO),
                disableScaleAnimation: true,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _AccountDetails extends StatefulWidget {
  const _AccountDetails();

  @override
  State<_AccountDetails> createState() => _AccountDetailsState();
}

class _AccountDetailsState extends State<_AccountDetails> {
  final RefreshController _refreshController = RefreshController();

  @override
  void dispose() {
    _refreshController.dispose();
    super.dispose();
  }

  void _onRefresh(BuildContext context) {
    context.read<AccountInfoCubit>().getAccountInfo();
    _refreshController
      ..resetNoData()
      ..refreshCompleted();
  }

  Widget _buildBalanceRow(
      BuildContext context, dynamic accountInfo, SelectedExchangeCubit exchangeCubit, double selectedRate) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      spacing: 10.gh,
      children: [
        Expanded(
          child: DataField(
            label: 'availableBalance'.tr(),
            value: (accountInfo.usableCash ?? 0) * selectedRate,
            selectedExchangeCubit: exchangeCubit,
          ),
        ),
        Expanded(
          child: DataField(
            label: 'frozenAmount'.tr(),
            value: (accountInfo.freezeCash ?? 0) * selectedRate,
            selectedExchangeCubit: exchangeCubit,
          ),
        ),
      ],
    );
  }

  Widget _buildProfitRow(
      BuildContext context, AccountInfoData accountInfo, SelectedExchangeCubit exchangeCubit, double selectedRate) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      spacing: 10.gh,
      children: [
        Expanded(
          child: DataField(
            label: 'todayProfit'.tr(),
            value: (accountInfo.todayWinAmount ?? 0.00) * selectedRate,
            textStyle: FontPalette.semiBold14.copyWith(
              fontFamily: 'Akzidenz-Grotesk',
              letterSpacing: -0.5,
              color: (accountInfo.todayWinAmount ?? 0.00).getValueColor(context),
            ),
            selectedExchangeCubit: exchangeCubit,
          ),
        ),
        Expanded(
          child: DataField(
            label: '',
            suffix: ' %',
            value: accountInfo.todayWinRate ?? 0.00,
            textStyle: FontPalette.semiBold14.copyWith(
              fontFamily: 'Akzidenz-Grotesk',
              color: (accountInfo.todayWinRate ?? 0.00).getValueColor(context),
            ),
            isCurrency: false,
          ),
        ),
      ],
    );
  }

  Widget _buildAccountInfoCard(BuildContext context, dynamic accountInfo, double selectedRate) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.gw, vertical: 10.gh),
      decoration: BoxDecoration(
        color: myColorScheme(context).cardColor,
        borderRadius: BorderRadius.circular(12.gr),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withNewOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: BlocProvider(
        create: (_) => getIt<SelectedExchangeCubit>(),
        child: Builder(
          builder: (context) {
            final exchangeCubit = context.read<SelectedExchangeCubit>();

            return Padding(
              padding: EdgeInsets.all(8.gr),
              child: Column(
                spacing: 3.gh,
                children: [
                  DetailHeader(
                    label: 'totalAssets'.tr(),
                    value: (accountInfo.assetAmount ?? 0) * selectedRate,
                    showArrow: true,
                    selectedExchangeCubit: exchangeCubit,
                  ),
                  2.verticalSpace,
                  _buildBalanceRow(context, accountInfo, exchangeCubit, selectedRate),
                  _buildProfitRow(context, accountInfo, exchangeCubit, selectedRate),
                  10.verticalSpace,
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => getIt<SelectedExchangeCubit>(),
      child: Builder(
        builder: (context) {
          final selectedRate = context.watch<SelectedExchangeCubit>().currentExchangeRate;

          return CommonRefresher(
            controller: _refreshController,
            enablePullDown: true,
            onRefresh: () => _onRefresh(context),
            child: SingleChildScrollView(
              physics: const NeverScrollableScrollPhysics(),
              child: BlocSelector<AccountInfoCubit, AccountInfoState, (DataStatus, dynamic)>(
                selector: (state) => (state.accountInfoFetchStatus, state.accountInfo),
                builder: (context, data) {
                  final (status, accountInfo) = data;

                  if (status == DataStatus.loading && accountInfo == null) {
                    return _AccountInfoShimmer();
                  }

                  if (status == DataStatus.failed || accountInfo == null) {
                    return const Center(
                      child: TableEmptyWidget(height: 60, width: 60),
                    );
                  }

                  return GestureDetector(
                    onTap: () => context.verifyAuth(
                      () => Navigator.pushNamed(context, routeAccountDetails),
                    ),
                    child: _buildAccountInfoCard(context, accountInfo, selectedRate.rate),
                  );
                },
              ),
            ),
          );
        },
      ),
    );
  }
}

class _ContractSummary extends StatefulWidget {
  const _ContractSummary();

  @override
  State<_ContractSummary> createState() => _ContractSummaryState();
}

class _ContractSummaryState extends State<_ContractSummary> {
  final RefreshController _refreshController = RefreshController();

  @override
  void dispose() {
    _refreshController.dispose();
    super.dispose();
  }

  void _onRefresh(BuildContext context) {
    context.read<AccountCubit>().getContractSummary();
    _refreshController
      ..resetNoData()
      ..refreshCompleted();
  }

  void _onLoadMore(BuildContext context, ContractSummaryPage? contractSummary) {
    if (contractSummary?.records?.length == contractSummary?.total) {
      _refreshController.loadNoData();
    } else {
      context.read<AccountCubit>().getContractSummary(isLoadMore: true);
      _refreshController.loadComplete();
    }
  }

  Widget _buildContractList(ContractSummaryPage contractSummary) {
    return AnimationLimiter(
      child: ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: contractSummary.records?.length ?? 0,
        padding: EdgeInsets.symmetric(horizontal: 16.gw, vertical: 10.gh),
        separatorBuilder: (_, __) => 10.verticalSpace,
        itemBuilder: (context, index) {
          return AnimationConfiguration.staggeredList(
            position: index,
            duration: const Duration(milliseconds: 600),
            child: SlideAnimation(
              verticalOffset: 30.0,
              child: FadeInAnimation(
                child: ContractTile(
                  contractSummary: contractSummary.records![index],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocSelector<AccountCubit, AccountState, (DataStatus, ContractSummaryPage?)>(
      selector: (state) => (
        state.contractSummaryFetchStatus,
        state.activeContractSummary ?? state.contractSummary,
      ),
      builder: (context, data) {
        final (status, contractSummary) = data;

        return CommonRefresher(
          controller: _refreshController,
          enablePullDown: true,
          enablePullUp: true,
          onRefresh: () => _onRefresh(context),
          onLoading: () => _onLoadMore(context, contractSummary),
          child: Builder(
            builder: (context) {
              if (status == DataStatus.loading && contractSummary == null) {
                return _ContractShimmer();
              }

              final hasNoData = status == DataStatus.failed ||
                  contractSummary == null ||
                  contractSummary.records == null ||
                  contractSummary.records!.isEmpty;

              if (hasNoData) {
                return const Center(
                  child: TableEmptyWidget(height: 60, width: 60),
                );
              }

              return _buildContractList(contractSummary);
            },
          ),
        );
      },
    );
  }
}

class _ContractShimmer extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: myColorScheme(context).dividerColor,
      highlightColor: myColorScheme(context).cardColor.withValues(alpha: 0.5),
      child: ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: 3,
        padding: EdgeInsets.symmetric(horizontal: 16.gw, vertical: 10.gh),
        separatorBuilder: (_, __) => 10.verticalSpace,
        itemBuilder: (_, __) => Container(
          height: 80.gh,
          decoration: BoxDecoration(
            color: myColorScheme(context).cardColor,
            borderRadius: BorderRadius.circular(12.gr),
          ),
        ),
      ),
    );
  }
}

class _AccountInfoShimmer extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: myColorScheme(context).dividerColor,
      highlightColor: myColorScheme(context).cardColor.withValues(alpha: 0.5),
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: 16.gw, vertical: 10.gh),
        decoration: BoxDecoration(
          color: myColorScheme(context).cardColor,
          borderRadius: BorderRadius.circular(12.gr),
        ),
        child: Padding(
          padding: EdgeInsets.all(8.gr),
          child: Column(
            children: [
              _ShimmerRow(),
              2.verticalSpace,
              _ShimmerDataRow(),
              _ShimmerDataRow(),
              10.verticalSpace,
            ],
          ),
        ),
      ),
    );
  }
}

class _ShimmerRow extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Container(
          width: 100.gw,
          height: 16.gh,
          color: myColorScheme(context).cardColor,
        ),
        Container(
          width: 60.gw,
          height: 16.gh,
          color: myColorScheme(context).cardColor,
        ),
      ],
    );
  }
}

class _ShimmerDataRow extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(child: _ShimmerDataColumn()),
        Expanded(child: _ShimmerDataColumn()),
      ],
    );
  }
}

class _ShimmerDataColumn extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: 80.gw,
          height: 14.gh,
          color: myColorScheme(context).cardColor,
        ),
        4.verticalSpace,
        Container(
          width: 60.gw,
          height: 16.gh,
          color: myColorScheme(context).cardColor,
        ),
      ],
    );
  }
}

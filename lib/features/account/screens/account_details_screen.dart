import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/widgets/build_action_buttons.dart';
import 'package:gp_stock_app/features/main/domain/enums/navigation_item.dart';
import 'package:gp_stock_app/features/main/logic/main/main_cubit.dart';
import 'package:gp_stock_app/features/market/logic/market/market_cubit.dart';
import 'package:gp_stock_app/shared/logic/account_info/account_info_cubit.dart';
import 'package:gp_stock_app/shared/logic/account_info/account_info_state.dart';
import 'package:gp_stock_app/shared/routes/navigator.dart';

import '../../../shared/app/extension/helper.dart';
import '../../../shared/constants/assets.dart';
import '../../../shared/routes/routes.dart';
import '../../../shared/theme/my_color_scheme.dart';
import '../logic/account/account_cubit.dart';
import '../widgets/account_market_table.dart';
import '../widgets/assets_card.dart';

class AccountDetailsScreen extends StatefulWidget {
  const AccountDetailsScreen({super.key});

  @override
  State<AccountDetailsScreen> createState() => _AccountDetailsScreenState();
}

class _AccountDetailsScreenState extends State<AccountDetailsScreen> {
  @override
  void initState() {
    super.initState();
    Helper.afterInit(_init);
  }

  Future<void> _init() async {
    final accountCubit = context.read<AccountCubit>();
    // Start polling for account data every 5 seconds
    accountCubit.startAccountDataPolling();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      onPopInvokedWithResult: (didPop, result) {
        final accountCubit = context.read<AccountCubit>();
        accountCubit.stopContractSummaryPolling();
      },
      child: Scaffold(
        appBar: AppBar(
          surfaceTintColor: Colors.transparent,
          backgroundColor: myColorScheme(context).cardColor,
          title: Text('spotAccount'.tr()),
        ),
        body: SingleChildScrollView(
          child: Column(
            children: [
              Container(
                decoration: BoxDecoration(
                  color: myColorScheme(context).cardColor,
                  borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(20.gr),
                    bottomRight: Radius.circular(20.gr),
                  ),
                ),
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16.gw),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      // Assets Card
                      BlocBuilder<AccountInfoCubit, AccountInfoState>(
                        builder: (context, state) {
                          return AssetsCard(
                            totalAssets: state.accountInfo?.assetAmount ?? 0,
                            todaysEarnings: state.accountInfo?.todayWinAmount ?? 0,
                            availableBalance: state.accountInfo?.usableCash ?? 0,
                            myInterest: state.accountInfo?.interestCash ?? 0,
                            frozenAmount: state.accountInfo?.freezeCash ?? 0,
                            currency: state.accountInfo?.currency ?? 'CNY',
                          );
                        },
                      ),
                      14.verticalSpace,
                      // Action Buttons
                      _ActionButtons(),
                    ],
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.gw),
                child: Column(
                  children: [
                    AccountMarketTable(),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _ActionButtons extends StatelessWidget {
  const _ActionButtons();

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AccountCubit, AccountState>(
      builder: (context, state) {
        return GridView.count(
          shrinkWrap: true,
          padding: EdgeInsets.zero,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 5,
          crossAxisSpacing: 0,
          children: [
            BuildActionButton(
              label: 'topUpDeposit'.tr(),
              icon: Assets.myAssetIcon,
              onTap: () {
                context.verifyAuth(
                  () => context.verifyRealName(
                    () => Navigator.pushNamed(context, routeDepositMain),
                  ),
                );
              },
            ),
            BuildActionButton(
              label: 'cashOut'.tr(),
              icon: Assets.withdrawIcon,
              onTap: () {
                context.verifyAuth(() => Navigator.pushNamed(context, routeWithdrawMain));
              },
            ),
            BuildActionButton(
              label: 'tradingCenter'.tr(),
              icon: Assets.tradingIcon,
              onTap: () {
                context.read<MainCubit>().selectedNavigationItem(NavigationItem.trade);
                context.read<MarketCubit>().updateMainHeaderTab(1);
                Navigator.popUntil(navigatorKey.currentContext!, (route) => route.isFirst);
              },
            ),
            BuildActionButton(
              label: 'fundRecords'.tr(),
              icon: Assets.recordsIcon,
              onTap: () => Navigator.pushNamed(context, routeFundRecords),
            ),
            BuildActionButton(
              label: 'transactionHistory'.tr(),
              icon: Assets.historyIcon,
              onTap: () => Navigator.pushNamed(context, routeSpotAndContractHistory),
            ),
          ],
        );
      },
    );
  }
}

part of 'account_cubit.dart';

class AccountState extends Equatable {
  final AccountMarketType selectedTableTab;
  final ContractHistoryType selectedContractHistoryType;
  final ContractSummaryPage? contractSummary;
  final ContractSummaryPage? activeContractSummary; // for settlementStatus=1
  final ContractSummaryPage? settledContractSummary; // for settlementStatus=2
  final DataStatus contractSummaryFetchStatus;
  final AccountTableType selectedAccountTableType;
  final OrderData? currentPositions;
  final OrderData? tradeDetails;
  final OrderData? orderDetails;
  final DataStatus currentPositionsFetchStatus;
  final DataStatus tradeDetailsFetchStatus;
  final DataStatus orderDetailsFetchStatus;
  final DataStatus contractHistoryTransactionFetchStatus;
  final DataStatus contractHistoryCommissionFetchStatus;
  final OrderData? contractHistoryTransaction;
  final OrderData? contractHistoryCommission;
  final String? error;
  final ContractSummaryData? currentContractSummary;
  final DataStatus contractSettlementFetchStatus;

  // Order detail
  final OrderRecord? orderDetail;
  final DataStatus orderDetailFetchStatus;
  // Separate pagination states for each table type
  final PaginationState currentPositionsPagination;
  final PaginationState tradeDetailsPagination;
  final PaginationState orderDetailsPagination;
  final PaginationState contractHistoryTransactionPagination;
  final PaginationState contractHistoryCommissionPagination;

  const AccountState({
    this.selectedTableTab = AccountMarketType.currentPositions,
    this.selectedContractHistoryType = ContractHistoryType.transaction,
    this.contractSummary,
    this.activeContractSummary,
    this.settledContractSummary,
    this.contractSummaryFetchStatus = DataStatus.idle,
    this.selectedAccountTableType = AccountTableType.CONTRACT_SUMMARY,
    this.currentPositions,
    this.tradeDetails,
    this.orderDetails,
    this.currentPositionsFetchStatus = DataStatus.idle,
    this.tradeDetailsFetchStatus = DataStatus.idle,
    this.orderDetailsFetchStatus = DataStatus.idle,
    this.error,
    this.contractHistoryTransactionFetchStatus = DataStatus.idle,
    this.contractHistoryCommissionFetchStatus = DataStatus.idle,
    this.contractHistoryTransaction,
    this.contractHistoryCommission,
    this.currentPositionsPagination = const PaginationState(),
    this.tradeDetailsPagination = const PaginationState(),
    this.orderDetailsPagination = const PaginationState(),
    this.contractHistoryTransactionPagination = const PaginationState(),
    this.contractHistoryCommissionPagination = const PaginationState(),
    this.currentContractSummary,
    this.contractSettlementFetchStatus = DataStatus.idle,
    this.orderDetail,
    this.orderDetailFetchStatus = DataStatus.idle,
  });

  @override
  List<Object?> get props => [
        selectedTableTab,
        contractSummary,
        activeContractSummary,
        settledContractSummary,
        contractSummaryFetchStatus,
        error,
        selectedAccountTableType,
        currentPositions,
        tradeDetails,
        orderDetails,
        currentPositionsFetchStatus,
        tradeDetailsFetchStatus,
        orderDetailsFetchStatus,
        contractHistoryTransactionFetchStatus,
        contractHistoryCommissionFetchStatus,
        contractHistoryTransaction,
        contractHistoryCommission,
        selectedContractHistoryType,
        currentPositionsPagination,
        tradeDetailsPagination,
        orderDetailsPagination,
        contractHistoryTransactionPagination,
        contractHistoryCommissionPagination,
        currentContractSummary,
        contractSettlementFetchStatus,
        orderDetail,
        orderDetailFetchStatus,
      ];

  PaginationState getPaginationState(AccountMarketType type) {
    return switch (type) {
      AccountMarketType.currentPositions => currentPositionsPagination,
      AccountMarketType.tradeDetails => tradeDetailsPagination,
      AccountMarketType.orderDetails => orderDetailsPagination,
    };
  }

  AccountState copyWith({
    AccountMarketType? selectedTableTab,
    ContractSummaryPage? contractSummary,
    ContractSummaryPage? activeContractSummary,
    ContractSummaryPage? settledContractSummary,
    DataStatus? contractSummaryFetchStatus,
    String? error,
    AccountTableType? selectedAccountTableType,
    DataStatus? currentPositionsFetchStatus,
    DataStatus? tradeDetailsFetchStatus,
    DataStatus? orderDetailsFetchStatus,
    OrderData? Function()? currentPositions,
    OrderData? Function()? tradeDetails,
    OrderData? Function()? orderDetails,
    DataStatus? contractHistoryTransactionFetchStatus,
    DataStatus? contractHistoryCommissionFetchStatus,
    OrderData? contractHistoryTransaction,
    OrderData? contractHistoryCommission,
    ContractHistoryType? selectedContractHistoryType,
    PaginationState? currentPositionsPagination,
    PaginationState? tradeDetailsPagination,
    PaginationState? orderDetailsPagination,
    PaginationState? contractHistoryTransactionPagination,
    PaginationState? contractHistoryCommissionPagination,
    ContractSummaryData? Function()? currentContractSummary,
    DataStatus? contractSettlementFetchStatus,
    OrderRecord? orderDetail,
    DataStatus? orderDetailFetchStatus,
    StockInfoData? Function()? stockInfo,
  }) {
    return AccountState(
      selectedTableTab: selectedTableTab ?? this.selectedTableTab,
      contractSummary: contractSummary ?? this.contractSummary,
      activeContractSummary: activeContractSummary ?? this.activeContractSummary,
      settledContractSummary: settledContractSummary ?? this.settledContractSummary,
      contractSummaryFetchStatus: contractSummaryFetchStatus ?? this.contractSummaryFetchStatus,
      error: error ?? this.error,
      selectedAccountTableType: selectedAccountTableType ?? this.selectedAccountTableType,
      currentPositions: currentPositions != null ? currentPositions() : this.currentPositions,
      tradeDetails: tradeDetails != null ? tradeDetails() : this.tradeDetails,
      orderDetails: orderDetails != null ? orderDetails() : this.orderDetails,
      currentPositionsFetchStatus: currentPositionsFetchStatus ?? this.currentPositionsFetchStatus,
      tradeDetailsFetchStatus: tradeDetailsFetchStatus ?? this.tradeDetailsFetchStatus,
      orderDetailsFetchStatus: orderDetailsFetchStatus ?? this.orderDetailsFetchStatus,
      contractHistoryTransactionFetchStatus:
          contractHistoryTransactionFetchStatus ?? this.contractHistoryTransactionFetchStatus,
      contractHistoryCommissionFetchStatus:
          contractHistoryCommissionFetchStatus ?? this.contractHistoryCommissionFetchStatus,
      contractHistoryTransaction: contractHistoryTransaction ?? this.contractHistoryTransaction,
      contractHistoryCommission: contractHistoryCommission ?? this.contractHistoryCommission,
      selectedContractHistoryType: selectedContractHistoryType ?? this.selectedContractHistoryType,
      currentPositionsPagination: currentPositionsPagination ?? this.currentPositionsPagination,
      tradeDetailsPagination: tradeDetailsPagination ?? this.tradeDetailsPagination,
      orderDetailsPagination: orderDetailsPagination ?? this.orderDetailsPagination,
      contractHistoryTransactionPagination:
          contractHistoryTransactionPagination ?? this.contractHistoryTransactionPagination,
      contractHistoryCommissionPagination:
          contractHistoryCommissionPagination ?? this.contractHistoryCommissionPagination,
      currentContractSummary: currentContractSummary != null ? currentContractSummary() : this.currentContractSummary,
      contractSettlementFetchStatus: contractSettlementFetchStatus ?? this.contractSettlementFetchStatus,
      orderDetail: orderDetail ?? this.orderDetail,
      orderDetailFetchStatus: orderDetailFetchStatus ?? this.orderDetailFetchStatus,
    );
  }
}

class PaginationState extends Equatable {
  final int page;
  final bool isPaginating;
  final bool hasReachedMax;

  const PaginationState({
    this.page = 1,
    this.isPaginating = false,
    this.hasReachedMax = false,
  });

  PaginationState copyWith({
    int? page,
    bool? isPaginating,
    bool? hasReachedMax,
  }) {
    return PaginationState(
      page: page ?? this.page,
      isPaginating: isPaginating ?? this.isPaginating,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
    );
  }

  @override
  List<Object?> get props => [page, isPaginating, hasReachedMax];
}

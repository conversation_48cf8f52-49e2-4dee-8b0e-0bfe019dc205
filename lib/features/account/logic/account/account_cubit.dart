import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/utils/utils.dart';
import 'package:gp_stock_app/features/market/domain/models/stock_reponse/stock_info_response.dart';
import 'package:gp_stock_app/features/profile/logic/auth_aware_cubit.dart';
import 'package:gp_stock_app/features/sign_in/domain/models/login/login_response.dart';
import 'package:gp_stock_app/features/sign_in/domain/repository/sign_in_repository.dart';
import 'package:gp_stock_app/shared/logic/account_info/account_info_cubit.dart';
import 'package:gp_stock_app/shared/models/dropdown/dropdown_value.dart';
import 'package:gp_stock_app/shared/models/instrument/instrument.dart';
import 'package:gp_stock_app/shared/services/polling/polling_service.dart';
import 'package:injectable/injectable.dart';

import '../../../../shared/constants/enums.dart';
import '../../domain/models/account_summary/contract_summary_response.dart';
import '../../domain/models/order/order_response.dart';
import '../../domain/repository/account_repository.dart';

part 'account_state.dart';

@injectable
class AccountCubit extends AuthAwareCubit<AccountState> {
  final AccountRepository _accountService = getIt<AccountRepository>();
  final PollingService _pollingService = getIt<PollingService>();
  AccountCubit(SignInRepository signInRepo) : super(signInRepo, const AccountState()) {
    // onMessage(SocketTypes.getAccountInfo, loginRequired: true).listen(_handleAccountInfoUpdate);
    // onMessage(SocketTypes.position, loginRequired: true).listen(_handlePositionUpdate);
    // onMessage(SocketTypes.entrustDeal, loginRequired: true).listen(_handleEntrustDealUpdate);
    // onMessage(SocketTypes.entrust, loginRequired: true).listen(_handleEntrustUpdate);
    // onMessage(SocketTypes.contract, loginRequired: true).listen(_handleContractUpdate);
  }

  @override
  void onLoggedIn(LoginResponse loginResponse) => startContractSummaryPolling();

  @override
  void onLoggedOut() {
    stopContractSummaryPolling();
    emit(const AccountState());
  }

  void updateTableTab(AccountMarketType type) => emit(state.copyWith(selectedTableTab: type));

  void updateAccountTableType(AccountTableType type) => emit(state.copyWith(selectedAccountTableType: type));

  /// Get contracts by market type for trading purpose. This is used in dropdown inside trading form section.
  /// If index trading is enabled, only spot trading is available.
  /// Otherwise, all contracts of the given market type along with spot trading are returned.
  List<DropDownValue> getContractsByMarketType(MainMarketType marketType, bool isIndexTrading) {
    if (isIndexTrading) {
      return [
        DropDownValue(id: null, value: 'spotTrading'.tr()),
      ];
    }
    final contracts =
        (state.contractSummary?.records ?? []).where((contract) => contract.marketType == marketType.type).toList();
    return contracts.map((e) => DropDownValue(id: e.id.toString(), value: getContractLabel(e))).toList()
      ..insert(0, DropDownValue(id: null, value: 'spotTrading'.tr()));
  }

  /// Get available quantity to sell from current positions. This is used in trading form section.
  double availableQuantityToSell() {
    final contractRestNumSum =
        state.currentPositions?.records?.fold<double>(0, (sum, position) => sum + (position.restNum ?? 0));
    if (contractRestNumSum == null) return 0;
    return contractRestNumSum;
  }

  Future<void> getCurrentContractSummary(int contractId, {bool isPolling = false}) async {
    emit(state.copyWith(
        contractSummaryFetchStatus: DataStatus.loading,
        currentContractSummary: () => isPolling ? state.currentContractSummary : null));
    try {
      final result = await _accountService.getCurrentContractSummary(contractId);
      if (result.isSuccess) {
        emit(state.copyWith(
          contractSummaryFetchStatus: DataStatus.success,
          currentContractSummary: () => result.data,
        ));
      } else {
        emit(
          state.copyWith(
            contractSummaryFetchStatus: DataStatus.failed,
            error: result.error,
          ),
        );
      }
    } on Exception catch (e) {
      emit(
        state.copyWith(
          contractSummaryFetchStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
    }
  }

  Future<void> getContractSummaryWithSettlementStatus({
    bool isLoadMore = false,
    required int settlementStatus,
    bool isPolling = false,
  }) async {
    if (isClosed) return;
    emit(state.copyWith(contractSettlementFetchStatus: DataStatus.loading));
    int page = isLoadMore ? (state.contractSummary?.current ?? 0 + 1) : 1;
    try {
      final result = await _accountService.getContractSummary(page: page, settlementStatus: settlementStatus);
      if (!result.isSuccess) {
        emit(state.copyWith(
          contractSummaryFetchStatus: DataStatus.failed,
          error: result.error,
        ));
        return;
      }
      final records = isLoadMore && !isPolling
          ? [...?state.contractSummary?.records, ...?result.data?.data?.records]
          : result.data?.data?.records ?? [];

      emit(state.copyWith(
        contractSettlementFetchStatus: DataStatus.success,
        settledContractSummary: ContractSummaryPage(
          records: records,
          current: result.data?.data?.current ?? 0,
          hasNext: result.data?.data?.hasNext ?? false,
          total: result.data?.data?.total ?? 0,
        ),
      ));
      // subscribeToContractsData(params: {
      //   'settlementStatus': settlementStatus,
      //   'pageSize': 20,
      //   'pageNumber': 1,
      // });
    } on Exception catch (e) {
      emit(state.copyWith(
        contractSettlementFetchStatus: DataStatus.failed,
        error: e.toString(),
      ));
    }
  }

  Future<void> getContractSummary({
    bool isLoadMore = false,
    int? settlementStatus = 1,
    bool isPolling = false,
  }) async {
    if (isClosed) return;
    if (!isLoadMore && !isPolling) {
      emit(state.copyWith(contractSummaryFetchStatus: DataStatus.loading));
    }
    if (settlementStatus == 2) {
      emit(state.copyWith(contractSettlementFetchStatus: DataStatus.loading));
    }

    int page = isLoadMore ? (state.contractSummary?.current ?? 0 + 1) : 1;

    try {
      final result = await _accountService.getContractSummary(page: page, settlementStatus: settlementStatus);

      if (!result.isSuccess) {
        emit(state.copyWith(
          contractSummaryFetchStatus: DataStatus.failed,
          error: result.error,
        ));
        return;
      }

      final records = isLoadMore && !isPolling
          ? [...?state.contractSummary?.records, ...?result.data?.data?.records]
          : result.data?.data?.records ?? [];

      if (settlementStatus == 1) {
        if (isClosed) return;
        emit(state.copyWith(
          contractSummaryFetchStatus: DataStatus.success,
          activeContractSummary: ContractSummaryPage(
            records: records,
            current: result.data?.data?.current ?? 0,
            hasNext: result.data?.data?.hasNext ?? false,
            total: result.data?.data?.total ?? 0,
          ),
          // Keep original for backwards compatibility
          contractSummary: ContractSummaryPage(
            records: records,
            current: result.data?.data?.current ?? 0,
            hasNext: result.data?.data?.hasNext ?? false,
            total: result.data?.data?.total ?? 0,
          ),
        ));
      } else if (settlementStatus == 2) {
        if (isClosed) return;
        emit(state.copyWith(
          contractSummaryFetchStatus: DataStatus.success,
          contractSettlementFetchStatus: DataStatus.success,
          settledContractSummary: ContractSummaryPage(
            records: records,
            current: result.data?.data?.current ?? 0,
            hasNext: result.data?.data?.hasNext ?? false,
            total: result.data?.data?.total ?? 0,
          ),
        ));
      }
      // subscribeToContractsData(params: {
      //   'settlementStatus': settlementStatus,
      //   'pageSize': 20,
      //   'pageNumber': 1,
      // });
    } on Exception catch (e) {
      emit(state.copyWith(
        contractSummaryFetchStatus: DataStatus.failed,
        contractSettlementFetchStatus: DataStatus.failed,
        error: e.toString(),
      ));
    }
  }

  ///Loads 3 tables in the spot account details page: current positions, trade details, and order details.
  Future<void> getOrderList(
    AccountMarketType type, {
    int? contractId,
    String? symbol,
    bool isLoadMore = false,
    int? status,
    String? market,
    String? securityType,
    String? commentAssetId,
    bool isPolling = false,
  }) async {
    // Don't show loading indicators for polling requests to avoid irritating users
    if (!isLoadMore && !isPolling) {
      final fetchStatus = switch (type) {
        AccountMarketType.currentPositions =>
          state.copyWith(currentPositionsFetchStatus: DataStatus.loading, currentPositions: () => null),
        AccountMarketType.tradeDetails =>
          state.copyWith(tradeDetailsFetchStatus: DataStatus.loading, tradeDetails: () => null),
        AccountMarketType.orderDetails =>
          state.copyWith(orderDetailsFetchStatus: DataStatus.loading, orderDetails: () => null),
      };
      emit(fetchStatus);
    }

    try {
      // For polling requests, always use page 1 to get fresh data
      // For load more requests (not polling), increment the page
      final page = isLoadMore && !isPolling
          ? switch (type) {
              AccountMarketType.currentPositions => (state.currentPositions?.current ?? 0) + 1,
              AccountMarketType.tradeDetails => (state.tradeDetails?.current ?? 0) + 1,
              AccountMarketType.orderDetails => (state.orderDetails?.current ?? 0) + 1,
            }
          : 1;
      final result = type == AccountMarketType.currentPositions
          ? await _accountService.getPositionList(
              page: page,
              contractId: contractId,
              market: market,
              securityType: securityType,
              symbol: symbol,
              commentAssetId: commentAssetId,
            )
          : await _accountService.getOrderList(
              page: page,
              contractId: contractId,
              market: market,
              securityType: securityType,
              symbol: symbol,
              commentAssetId: commentAssetId,
              status: status ??
                  switch (type) {
                    AccountMarketType.tradeDetails => 2,
                    AccountMarketType.orderDetails => 0,
                    _ => 0,
                  });

      if (result.isSuccess) {
        final records = switch (type) {
          // For polling requests, always replace existing data to get fresh real-time updates
          // For load more requests (not polling), append new data to existing data
          AccountMarketType.currentPositions => isLoadMore && !isPolling
              ? [...?state.currentPositions?.records, ...?result.data?.data?.records]
              : result.data?.data?.records ?? [],
          AccountMarketType.tradeDetails => isLoadMore && !isPolling
              ? [...?state.tradeDetails?.records, ...?result.data?.data?.records]
              : result.data?.data?.records ?? [],
          AccountMarketType.orderDetails => isLoadMore && !isPolling
              ? [...?state.orderDetails?.records, ...?result.data?.data?.records]
              : result.data?.data?.records ?? [],
        };

        final successState = switch (type) {
          AccountMarketType.currentPositions => state.copyWith(
              currentPositionsFetchStatus: DataStatus.success,
              currentPositions: () => result.data?.data?.copyWith(records: records),
            ),
          AccountMarketType.tradeDetails => state.copyWith(
              tradeDetailsFetchStatus: DataStatus.success,
              tradeDetails: () => result.data?.data?.copyWith(records: records),
            ),
          AccountMarketType.orderDetails => state.copyWith(
              orderDetailsFetchStatus: DataStatus.success,
              orderDetails: () => result.data?.data?.copyWith(records: records),
            ),
        };
        if (isClosed) return;
        emit(successState);
        // final params = <String, dynamic>{
        //   "pageNumber": page,
        //   "pageSize": 20,
        // }
        //   ..addAll(status != null ? {'status': '$status'} : {})
        //   ..addAll(contractId != null ? {'contractId': '$contractId'} : {})
        //   ..addAll(symbol != null ? {'symbol': symbol} : {})
        //   ..addAll(market != null ? {'market': market} : {})
        //   ..addAll(securityType != null ? {'securityType': securityType} : {})
        //   ..addAll(commentAssetId != null ? {'commentAssetId': commentAssetId} : {});
        // subscribeToSpotAccountsData(type, params: params);
      } else {
        // Don't show error messages for polling requests to avoid irritating users
        if (!isPolling) {
          final failedState = switch (type) {
            AccountMarketType.currentPositions => state.copyWith(
                currentPositionsFetchStatus: DataStatus.failed,
                error: result.error,
              ),
            AccountMarketType.tradeDetails => state.copyWith(
                tradeDetailsFetchStatus: DataStatus.failed,
                error: result.error,
              ),
            AccountMarketType.orderDetails => state.copyWith(
                orderDetailsFetchStatus: DataStatus.failed,
                error: result.error,
              ),
          };
          emit(failedState);
        }
      }
    } on Exception catch (e) {
      // Don't show error messages for polling requests to avoid irritating users
      if (!isPolling) {
        final errorState = switch (type) {
          AccountMarketType.currentPositions => state.copyWith(
              currentPositionsFetchStatus: DataStatus.failed,
              error: e.toString(),
            ),
          AccountMarketType.tradeDetails => state.copyWith(
              tradeDetailsFetchStatus: DataStatus.failed,
              error: e.toString(),
            ),
          AccountMarketType.orderDetails => state.copyWith(
              orderDetailsFetchStatus: DataStatus.failed,
              error: e.toString(),
            ),
        };
        emit(errorState);
      }
    }
  }

  int getCountForMarketType(AccountMarketType type) {
    return switch (type) {
      AccountMarketType.currentPositions => state.currentPositions?.total ?? 0,
      AccountMarketType.tradeDetails => state.tradeDetails?.total ?? 0,
      AccountMarketType.orderDetails => state.orderDetails?.total ?? 0,
    };
  }

  Future<void> getContractHistoryTransaction({bool isLoadMore = false, int? contractId, String? commentAssetId}) async {
    if (!isLoadMore) {
      emit(state.copyWith(contractHistoryTransactionFetchStatus: DataStatus.loading));
    }
    try {
      final result = await _accountService.getOrderHistoryTransaction(
        page: isLoadMore ? (state.contractHistoryTransaction?.current ?? 0) + 1 : 1,
        contractId: contractId,
        commentAssetId: commentAssetId,
        status: 2,
      );
      if (result.isSuccess) {
        final records = isLoadMore
            ? [...?state.contractHistoryTransaction?.records, ...?result.data?.data?.records]
            : result.data?.data?.records ?? [];
        emit(state.copyWith(
          contractHistoryTransactionFetchStatus: DataStatus.success,
          contractHistoryTransaction: result.data?.data?.copyWith(
            records: records,
            current: result.data?.data?.current ?? 0,
            hasNext: result.data?.data?.hasNext ?? false,
            total: result.data?.data?.total ?? 0,
          ),
        ));
      } else {
        emit(state.copyWith(contractHistoryTransactionFetchStatus: DataStatus.failed, error: result.error));
      }
    } on Exception catch (e) {
      emit(state.copyWith(contractHistoryTransactionFetchStatus: DataStatus.failed, error: e.toString()));
    }
  }

  Future<void> getContractHistoryCommission({bool isLoadMore = false, int? contractId, String? commentAssetId}) async {
    if (!isLoadMore) {
      emit(state.copyWith(contractHistoryCommissionFetchStatus: DataStatus.loading));
    }
    try {
      final result = await _accountService.getOrderHistoryCommission(
        page: isLoadMore ? (state.contractHistoryCommission?.current ?? 0) + 1 : 1,
        contractId: contractId,
        commentAssetId: commentAssetId,
      );

      if (result.isSuccess) {
        final records = isLoadMore
            ? [...?state.contractHistoryCommission?.records, ...?result.data?.data?.records]
            : result.data?.data?.records ?? [];
        emit(state.copyWith(
          contractHistoryCommissionFetchStatus: DataStatus.success,
          contractHistoryCommission: result.data?.data?.copyWith(
            records: records,
            current: result.data?.data?.current ?? 0,
            hasNext: result.data?.data?.hasNext ?? false,
            total: result.data?.data?.total ?? 0,
          ),
        ));
      } else {
        emit(state.copyWith(contractHistoryCommissionFetchStatus: DataStatus.failed, error: result.error));
      }
    } on Exception catch (e) {
      emit(state.copyWith(contractHistoryCommissionFetchStatus: DataStatus.failed, error: e.toString()));
    }
  }

  void updateContractHistoryType(ContractHistoryType type) {
    emit(state.copyWith(selectedContractHistoryType: type));
  }

  /// Reset contract history type to default (transaction)
  void resetContractHistoryType() {
    emit(state.copyWith(selectedContractHistoryType: ContractHistoryType.transaction));
  }

  /// Starts polling
  void startAccountDataPolling() {
    final assetId = getIt<AccountInfoCubit>().state.accountInfo?.assetId.toString();
    _pollingService.startPolling(
      'account_data_polling',
      (isActive) {
        // For polling, always use isLoadMore: false to get fresh data from page 1
        getOrderList(AccountMarketType.currentPositions, commentAssetId: assetId, isLoadMore: false, isPolling: true);
        getOrderList(AccountMarketType.tradeDetails, commentAssetId: assetId, isLoadMore: false, isPolling: true);
        getOrderList(AccountMarketType.orderDetails, commentAssetId: assetId, isLoadMore: false, isPolling: true);
      },
    );
  }

  void startContractSummaryPolling() {
    _pollingService.startPolling(
      'contract_summary_polling',
      (isActive) {
        getContractSummary(isPolling: isActive);
      },
    );
  }

  void startContractDataPolling(int contractId) {
    getOrderList(AccountMarketType.currentPositions, contractId: contractId);
    getOrderList(AccountMarketType.tradeDetails, contractId: contractId);
    getOrderList(AccountMarketType.orderDetails, contractId: contractId);
    _pollingService.startPolling(
      'contract_data_polling',
      (isActive) {
        // For polling, always use isLoadMore: false to get fresh data from page 1
        getOrderList(AccountMarketType.currentPositions, contractId: contractId, isLoadMore: false, isPolling: true);
        getOrderList(AccountMarketType.tradeDetails, contractId: contractId, isLoadMore: false, isPolling: true);
        getOrderList(AccountMarketType.orderDetails, contractId: contractId, isLoadMore: false, isPolling: true);
      },
    );
  }

  void startTradingCenterPolling(Instrument instrument, [int? contractId]) {
    final isActive = _pollingService.isPollingActive('trading_center_polling');
    if (isActive) _pollingService.stopPolling('trading_center_polling');
    _pollingService.startPolling(
      'trading_center_polling',
      (isActive) {
        getContractSummary(isPolling: isActive);
        getOrderList(
          AccountMarketType.currentPositions,
          symbol: instrument.symbol,
          market: instrument.market,
          securityType: instrument.securityType,
          contractId: contractId,
          isPolling: true,
          isLoadMore: false, // Always use false for polling to get fresh data
        );
        getOrderList(
          AccountMarketType.orderDetails,
          symbol: instrument.symbol,
          status: 0,
          market: instrument.market,
          securityType: instrument.securityType,
          contractId: contractId,
          isPolling: true,
          isLoadMore: false, // Always use false for polling to get fresh data
        );
      },
    );
  }

  /// Stops polling
  void stopAccountDataPolling() {
    _pollingService.stopPolling('account_data_polling');
  }

  void stopContractDataPolling() {
    _pollingService.stopPolling('contract_data_polling');
  }

  void stopContractSummaryPolling() {
    _pollingService.stopPolling('contract_summary_polling');
  }

  void stopTradingCenterPolling() {
    _pollingService.stopPolling('trading_center_polling');
  }

  // ==================== WebSocket Functions Start ====================
  // Functions for handling WebSocket subscriptions, message formatting, and real-time updates
  // Future<void> subscribeToSpotAccountsData(AccountMarketType type, {Map<String, dynamic>? params}) async {
  //   webSocketService.send({
  //     'action': SocketActions.subscribe,
  //     'type': type.socketEvent,
  //     'params': params,
  //   });
  // }

  // Future<void> subscribeToContractsData({Map<String, dynamic>? params}) async {
  //   webSocketService.send({
  //     'action': SocketActions.subscribe,
  //     'type': SocketTypes.contract,
  //     'params': params,
  //   });
  // }

  // Future<void> subscribeToIndividualContractData(int contractId) async {
  //   webSocketService.send({
  //     'action': SocketActions.subscribe,
  //     'type': SocketTypes.contract,
  //     'params': {
  //       'contractAccountId': contractId,
  //     },
  //   });
  // }

  // Future<void> unsubscribeFromIndividualContractData(int contractId) async {
  //   webSocketService.send({
  //     'action': SocketActions.unsubscribe,
  //     'type': SocketTypes.contract,
  //     'params': {
  //       'contractAccountId': contractId,
  //     },
  //   });
  // }

  // Future<void> unsubscribe() async {
  //   webSocketService
  // ..send({
  //   'action': SocketActions.unsubscribe,
  //   'type': AccountMarketType.currentPositions.socketEvent,
  // })
  // ..send({
  //   'action': SocketActions.unsubscribe,
  //   'type': AccountMarketType.tradeDetails.socketEvent,
  // })
  // ..send({
  //   'action': SocketActions.unsubscribe,
  //   'type': AccountMarketType.orderDetails.socketEvent,
  // })
  //       .send({
  //     'action': SocketActions.unsubscribe,
  //     'type': SocketTypes.contract,
  //   });
  // }

  // void _handlePositionUpdate(WebSocketMessage event) {
  //   try {
  //     final data = OrderResponse.fromJson(event.data);
  //     if (!isClosed) {
  //       for (final order in data.data?.records ?? <OrderRecord>[]) {
  //         final orderToUpdate = state.currentPositions?.records?.firstWhereOrNull((element) => element.id == order.id);
  //         if (orderToUpdate != null) {
  //           emit(
  //             state.copyWith(
  //               currentPositions: state.currentPositions?.copyWith(
  //                 records: state.currentPositions?.records?.map((e) {
  //                       if (e.id == order.id) {
  //                         return order;
  //                       } else {
  //                         return e;
  //                       }
  //                     }).toList() ??
  //                     [],
  //               ),
  //             ),
  //           );
  //         }
  //       }
  //     }
  //   } catch (e) {
  //     debugPrint('WebSocketChannelService: Error handling position update: $e');
  //   }
  // }

  // void _handleEntrustDealUpdate(WebSocketMessage event) {
  //   try {
  //     final data = OrderResponse.fromJson(event.data['data']);
  //     if (!isClosed) {
  //       for (final order in data.data?.records ?? <OrderRecord>[]) {
  //         final orderToUpdate = state.tradeDetails?.records?.firstWhereOrNull((element) => element.id == order.id);
  //         if (orderToUpdate != null) {
  //           emit(
  //             state.copyWith(
  //               tradeDetails: state.tradeDetails?.copyWith(
  //                 records: state.tradeDetails?.records?.map((e) {
  //                       if (e.id == order.id) {
  //                         return order;
  //                       } else {
  //                         return e;
  //                       }
  //                     }).toList() ??
  //                     [],
  //               ),
  //             ),
  //           );
  //         }
  //       }
  //     }
  //   } catch (e) {
  //     debugPrint('WebSocketChannelService: Error handling entrust deal update: $e');
  //   }
  // }

  // void _handleEntrustUpdate(WebSocketMessage event) {
  //   try {
  //     final data = OrderResponse.fromJson(event.data['data']);
  //     if (!isClosed) {
  //       for (final order in data.data?.records ?? <OrderRecord>[]) {
  //         final orderToUpdate = state.orderDetails?.records?.firstWhereOrNull((element) => element.id == order.id);
  //         if (orderToUpdate != null) {
  //           emit(
  //             state.copyWith(
  //               orderDetails: state.orderDetails?.copyWith(
  //                 records: state.orderDetails?.records?.map((e) {
  //                       if (e.id == order.id) {
  //                         return order;
  //                       } else {
  //                         return e;
  //                       }
  //                     }).toList() ??
  //                     [],
  //               ),
  //             ),
  //           );
  //         }
  //       }
  //     }
  //   } catch (e) {
  //     debugPrint('WebSocketChannelService: Error handling entrust update: $e');
  //   }
  // }

  // void _handleAccountInfoUpdate(WebSocketMessage event) {
  //   final data = AccountInfoData.fromJson(event.data['data']);
  //   if (!isClosed) {
  //     emit(state.copyWith(accountInfo: data));
  //   }
  // }

  // void _handleContractUpdate(WebSocketMessage event) {
  //   final data = ContractSummaryPage.fromJson(event.data['data']);
  //   if (!isClosed) {
  //     for (final contract in data.records ?? []) {
  //       final contractId = contract.id;
  //       if (contractId != null) {
  //         final contractData = state.contractSummary?.records?.firstWhereOrNull((element) => element.id == contractId);
  //         if (contractData != null) {
  //           emit(
  //             state.copyWith(
  //               contractSummary: state.contractSummary?.copyWith(
  //                 records: state.contractSummary?.records?.map<ContractSummaryData>((e) {
  //                       if (e.id == contractId) {
  //                         return contract;
  //                       }
  //                       return e;
  //                     }).toList() ??
  //                     [],
  //               ),
  //             ),
  //           );
  //         }
  //       }
  //     }
  //   }
  // }

  // ==================== WebSocket Functions End ====================

  Future<void> getOrderById(int id) async {
    emit(state.copyWith(
      orderDetailFetchStatus: DataStatus.loading,
    ));
    final result = await _accountService.getOrderById(id: id);
    if (result.data != null) {
      emit(state.copyWith(
        orderDetail: result.data,
        orderDetailFetchStatus: DataStatus.success,
      ));
    } else {
      emit(state.copyWith(
        error: result.error,
        orderDetailFetchStatus: DataStatus.failed,
      ));
    }
  }
}

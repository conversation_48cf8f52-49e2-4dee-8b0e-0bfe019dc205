// To parse this JSON data, do
//
//     final contractSummaryResponse = contractSummaryResponseFromJson(jsonString);

import 'dart:convert';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gp_stock_app/core/utils/utils.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

part 'contract_summary_response.freezed.dart';

part 'contract_summary_response.g.dart';

ContractSummaryResponse contractSummaryResponseFromJson(str) => ContractSummaryResponse.fromJson((str));

String contractSummaryResponseToJson(ContractSummaryResponse data) => json.encode(data.toJson());

@freezed
class ContractSummaryResponse with _$ContractSummaryResponse {
  const factory ContractSummaryResponse({
    int? code,
    ContractSummaryPage? data,
    String? msg,
    String? sign,
  }) = _ContractSummaryResponse;

  factory ContractSummaryResponse.fromJson(Map<String, dynamic> json) => _$ContractSummaryResponseFromJson(json);
}

@freezed
class ContractSummaryPage with _$ContractSummaryPage {
  const factory ContractSummaryPage({
    int? current,
    bool? hasNext,
    List<ContractSummaryData>? records,
    int? total,
  }) = _ContractSummaryPage;

  factory ContractSummaryPage.fromJson(Map<String, dynamic> json) => _$ContractSummaryPageFromJson(json);
}

@freezed
class ContractSummaryData with _$ContractSummaryData {
  const ContractSummaryData._();

  const factory ContractSummaryData({
    int? id,
    double? accountWinAmount,
    double? allAsset,
    double? closeRemindAmount,
    double? coverLossAmount,
    double? negativeAmount,
    String? currency,
    double? expendAmount,
    String? expireTime,
    double? freezePower,
    double? interestAmount,
    // 实际支付利息总额 折扣后的
    // The actual interest paid after the discount.
    double? receivableInterest,
    double? interestRate, // 利率
    String? marketType,
    int? multiple,
    String? openTime,
    int? periodType, // 期限类型 1：按天 2：按周 3：按月 4：免息
    double? positionAmount,
    int? settlementStatus, // 结算状态 1未结算 2已结束
    double? todayWinAmount,
    double? todayWinRate,
    double? totalAccountAmount,
    double? totalCash,
    double? totalFinance,
    double? totalPower,
    int? type, //	合约类型 1：普通 2：体验 3：彩金
    double? useAmount,
    double? warnRemindAmount,
    double? withdrawAmount,
    double? yesterdayAsset,
    double? winAmount,
    double? initCash,
    double? giveAmount,
    double? contractAssetAmount, // 净资产
    double? gapWarnRemindAmount, // 距离预警线金额
    double? gapCloseRemindAmount, // 距离强平线金额
    @JsonKey(name: 'isAutoRenew') @Default(false) bool isAutoRenew, // 合约续期状态: true自动续期 false到期结算
    double? winRate, // 亏损率
  }) = _ContractSummaryData;

  factory ContractSummaryData.fromJson(Map<String, dynamic> json) => _$ContractSummaryDataFromJson(json);

  String get label => getContractLabel(this);

  ContractType get contractType => ContractType.fromValue(type ?? 0);
}

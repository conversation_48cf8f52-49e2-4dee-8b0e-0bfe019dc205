// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'contract_summary_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ContractSummaryResponseImpl _$$ContractSummaryResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$ContractSummaryResponseImpl(
      code: (json['code'] as num?)?.toInt(),
      data: json['data'] == null
          ? null
          : ContractSummaryPage.fromJson(json['data'] as Map<String, dynamic>),
      msg: json['msg'] as String?,
      sign: json['sign'] as String?,
    );

Map<String, dynamic> _$$ContractSummaryResponseImplToJson(
        _$ContractSummaryResponseImpl instance) =>
    <String, dynamic>{
      'code': instance.code,
      'data': instance.data,
      'msg': instance.msg,
      'sign': instance.sign,
    };

_$ContractSummaryPageImpl _$$ContractSummaryPageImplFromJson(
        Map<String, dynamic> json) =>
    _$ContractSummaryPageImpl(
      current: (json['current'] as num?)?.toInt(),
      hasNext: json['hasNext'] as bool?,
      records: (json['records'] as List<dynamic>?)
          ?.map((e) => ContractSummaryData.fromJson(e as Map<String, dynamic>))
          .toList(),
      total: (json['total'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$ContractSummaryPageImplToJson(
        _$ContractSummaryPageImpl instance) =>
    <String, dynamic>{
      'current': instance.current,
      'hasNext': instance.hasNext,
      'records': instance.records,
      'total': instance.total,
    };

_$ContractSummaryDataImpl _$$ContractSummaryDataImplFromJson(
        Map<String, dynamic> json) =>
    _$ContractSummaryDataImpl(
      id: (json['id'] as num?)?.toInt(),
      accountWinAmount: (json['accountWinAmount'] as num?)?.toDouble(),
      allAsset: (json['allAsset'] as num?)?.toDouble(),
      closeRemindAmount: (json['closeRemindAmount'] as num?)?.toDouble(),
      coverLossAmount: (json['coverLossAmount'] as num?)?.toDouble(),
      negativeAmount: (json['negativeAmount'] as num?)?.toDouble(),
      currency: json['currency'] as String?,
      expendAmount: (json['expendAmount'] as num?)?.toDouble(),
      expireTime: json['expireTime'] as String?,
      freezePower: (json['freezePower'] as num?)?.toDouble(),
      interestAmount: (json['interestAmount'] as num?)?.toDouble(),
      receivableInterest: (json['receivableInterest'] as num?)?.toDouble(),
      interestRate: (json['interestRate'] as num?)?.toDouble(),
      marketType: json['marketType'] as String?,
      multiple: (json['multiple'] as num?)?.toInt(),
      openTime: json['openTime'] as String?,
      periodType: (json['periodType'] as num?)?.toInt(),
      positionAmount: (json['positionAmount'] as num?)?.toDouble(),
      settlementStatus: (json['settlementStatus'] as num?)?.toInt(),
      todayWinAmount: (json['todayWinAmount'] as num?)?.toDouble(),
      todayWinRate: (json['todayWinRate'] as num?)?.toDouble(),
      totalAccountAmount: (json['totalAccountAmount'] as num?)?.toDouble(),
      totalCash: (json['totalCash'] as num?)?.toDouble(),
      totalFinance: (json['totalFinance'] as num?)?.toDouble(),
      totalPower: (json['totalPower'] as num?)?.toDouble(),
      type: (json['type'] as num?)?.toInt(),
      useAmount: (json['useAmount'] as num?)?.toDouble(),
      warnRemindAmount: (json['warnRemindAmount'] as num?)?.toDouble(),
      withdrawAmount: (json['withdrawAmount'] as num?)?.toDouble(),
      yesterdayAsset: (json['yesterdayAsset'] as num?)?.toDouble(),
      winAmount: (json['winAmount'] as num?)?.toDouble(),
      initCash: (json['initCash'] as num?)?.toDouble(),
      giveAmount: (json['giveAmount'] as num?)?.toDouble(),
      contractAssetAmount: (json['contractAssetAmount'] as num?)?.toDouble(),
      gapWarnRemindAmount: (json['gapWarnRemindAmount'] as num?)?.toDouble(),
      gapCloseRemindAmount: (json['gapCloseRemindAmount'] as num?)?.toDouble(),
      isAutoRenew: json['isAutoRenew'] as bool? ?? false,
      winRate: (json['winRate'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$$ContractSummaryDataImplToJson(
        _$ContractSummaryDataImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'accountWinAmount': instance.accountWinAmount,
      'allAsset': instance.allAsset,
      'closeRemindAmount': instance.closeRemindAmount,
      'coverLossAmount': instance.coverLossAmount,
      'negativeAmount': instance.negativeAmount,
      'currency': instance.currency,
      'expendAmount': instance.expendAmount,
      'expireTime': instance.expireTime,
      'freezePower': instance.freezePower,
      'interestAmount': instance.interestAmount,
      'receivableInterest': instance.receivableInterest,
      'interestRate': instance.interestRate,
      'marketType': instance.marketType,
      'multiple': instance.multiple,
      'openTime': instance.openTime,
      'periodType': instance.periodType,
      'positionAmount': instance.positionAmount,
      'settlementStatus': instance.settlementStatus,
      'todayWinAmount': instance.todayWinAmount,
      'todayWinRate': instance.todayWinRate,
      'totalAccountAmount': instance.totalAccountAmount,
      'totalCash': instance.totalCash,
      'totalFinance': instance.totalFinance,
      'totalPower': instance.totalPower,
      'type': instance.type,
      'useAmount': instance.useAmount,
      'warnRemindAmount': instance.warnRemindAmount,
      'withdrawAmount': instance.withdrawAmount,
      'yesterdayAsset': instance.yesterdayAsset,
      'winAmount': instance.winAmount,
      'initCash': instance.initCash,
      'giveAmount': instance.giveAmount,
      'contractAssetAmount': instance.contractAssetAmount,
      'gapWarnRemindAmount': instance.gapWarnRemindAmount,
      'gapCloseRemindAmount': instance.gapCloseRemindAmount,
      'isAutoRenew': instance.isAutoRenew,
      'winRate': instance.winRate,
    };

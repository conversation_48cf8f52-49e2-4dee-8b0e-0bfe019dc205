// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_bank_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

UserBankModel _$UserBankModelFromJson(Map<String, dynamic> json) {
  return _BankAccountModel.fromJson(json);
}

/// @nodoc
mixin _$UserBankModel {
  String get bankAccount => throw _privateConstructorUsedError;
  String get bankCode => throw _privateConstructorUsedError;
  String get bankFullName => throw _privateConstructorUsedError;
  String get icon => throw _privateConstructorUsedError;
  int get id => throw _privateConstructorUsedError;
  String? get realName => throw _privateConstructorUsedError;
  String get status => throw _privateConstructorUsedError;
  String get tailNumber => throw _privateConstructorUsedError;
  int get userId => throw _privateConstructorUsedError;

  /// Serializes this UserBankModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UserBankModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UserBankModelCopyWith<UserBankModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserBankModelCopyWith<$Res> {
  factory $UserBankModelCopyWith(
          UserBankModel value, $Res Function(UserBankModel) then) =
      _$UserBankModelCopyWithImpl<$Res, UserBankModel>;
  @useResult
  $Res call(
      {String bankAccount,
      String bankCode,
      String bankFullName,
      String icon,
      int id,
      String? realName,
      String status,
      String tailNumber,
      int userId});
}

/// @nodoc
class _$UserBankModelCopyWithImpl<$Res, $Val extends UserBankModel>
    implements $UserBankModelCopyWith<$Res> {
  _$UserBankModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UserBankModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? bankAccount = null,
    Object? bankCode = null,
    Object? bankFullName = null,
    Object? icon = null,
    Object? id = null,
    Object? realName = freezed,
    Object? status = null,
    Object? tailNumber = null,
    Object? userId = null,
  }) {
    return _then(_value.copyWith(
      bankAccount: null == bankAccount
          ? _value.bankAccount
          : bankAccount // ignore: cast_nullable_to_non_nullable
              as String,
      bankCode: null == bankCode
          ? _value.bankCode
          : bankCode // ignore: cast_nullable_to_non_nullable
              as String,
      bankFullName: null == bankFullName
          ? _value.bankFullName
          : bankFullName // ignore: cast_nullable_to_non_nullable
              as String,
      icon: null == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String,
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      realName: freezed == realName
          ? _value.realName
          : realName // ignore: cast_nullable_to_non_nullable
              as String?,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      tailNumber: null == tailNumber
          ? _value.tailNumber
          : tailNumber // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$BankAccountModelImplCopyWith<$Res>
    implements $UserBankModelCopyWith<$Res> {
  factory _$$BankAccountModelImplCopyWith(_$BankAccountModelImpl value,
          $Res Function(_$BankAccountModelImpl) then) =
      __$$BankAccountModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String bankAccount,
      String bankCode,
      String bankFullName,
      String icon,
      int id,
      String? realName,
      String status,
      String tailNumber,
      int userId});
}

/// @nodoc
class __$$BankAccountModelImplCopyWithImpl<$Res>
    extends _$UserBankModelCopyWithImpl<$Res, _$BankAccountModelImpl>
    implements _$$BankAccountModelImplCopyWith<$Res> {
  __$$BankAccountModelImplCopyWithImpl(_$BankAccountModelImpl _value,
      $Res Function(_$BankAccountModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of UserBankModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? bankAccount = null,
    Object? bankCode = null,
    Object? bankFullName = null,
    Object? icon = null,
    Object? id = null,
    Object? realName = freezed,
    Object? status = null,
    Object? tailNumber = null,
    Object? userId = null,
  }) {
    return _then(_$BankAccountModelImpl(
      bankAccount: null == bankAccount
          ? _value.bankAccount
          : bankAccount // ignore: cast_nullable_to_non_nullable
              as String,
      bankCode: null == bankCode
          ? _value.bankCode
          : bankCode // ignore: cast_nullable_to_non_nullable
              as String,
      bankFullName: null == bankFullName
          ? _value.bankFullName
          : bankFullName // ignore: cast_nullable_to_non_nullable
              as String,
      icon: null == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String,
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      realName: freezed == realName
          ? _value.realName
          : realName // ignore: cast_nullable_to_non_nullable
              as String?,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      tailNumber: null == tailNumber
          ? _value.tailNumber
          : tailNumber // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$BankAccountModelImpl implements _BankAccountModel {
  const _$BankAccountModelImpl(
      {required this.bankAccount,
      required this.bankCode,
      required this.bankFullName,
      required this.icon,
      required this.id,
      this.realName,
      required this.status,
      required this.tailNumber,
      required this.userId});

  factory _$BankAccountModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$BankAccountModelImplFromJson(json);

  @override
  final String bankAccount;
  @override
  final String bankCode;
  @override
  final String bankFullName;
  @override
  final String icon;
  @override
  final int id;
  @override
  final String? realName;
  @override
  final String status;
  @override
  final String tailNumber;
  @override
  final int userId;

  @override
  String toString() {
    return 'UserBankModel(bankAccount: $bankAccount, bankCode: $bankCode, bankFullName: $bankFullName, icon: $icon, id: $id, realName: $realName, status: $status, tailNumber: $tailNumber, userId: $userId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BankAccountModelImpl &&
            (identical(other.bankAccount, bankAccount) ||
                other.bankAccount == bankAccount) &&
            (identical(other.bankCode, bankCode) ||
                other.bankCode == bankCode) &&
            (identical(other.bankFullName, bankFullName) ||
                other.bankFullName == bankFullName) &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.realName, realName) ||
                other.realName == realName) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.tailNumber, tailNumber) ||
                other.tailNumber == tailNumber) &&
            (identical(other.userId, userId) || other.userId == userId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, bankAccount, bankCode,
      bankFullName, icon, id, realName, status, tailNumber, userId);

  /// Create a copy of UserBankModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BankAccountModelImplCopyWith<_$BankAccountModelImpl> get copyWith =>
      __$$BankAccountModelImplCopyWithImpl<_$BankAccountModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$BankAccountModelImplToJson(
      this,
    );
  }
}

abstract class _BankAccountModel implements UserBankModel {
  const factory _BankAccountModel(
      {required final String bankAccount,
      required final String bankCode,
      required final String bankFullName,
      required final String icon,
      required final int id,
      final String? realName,
      required final String status,
      required final String tailNumber,
      required final int userId}) = _$BankAccountModelImpl;

  factory _BankAccountModel.fromJson(Map<String, dynamic> json) =
      _$BankAccountModelImpl.fromJson;

  @override
  String get bankAccount;
  @override
  String get bankCode;
  @override
  String get bankFullName;
  @override
  String get icon;
  @override
  int get id;
  @override
  String? get realName;
  @override
  String get status;
  @override
  String get tailNumber;
  @override
  int get userId;

  /// Create a copy of UserBankModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BankAccountModelImplCopyWith<_$BankAccountModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

import 'package:freezed_annotation/freezed_annotation.dart';

part 'user_bank_model.freezed.dart';
part 'user_bank_model.g.dart';

@freezed
class UserBankModel with _$UserBankModel {
  const factory UserBankModel({
    required String bankAccount,
    required String bankCode,
    required String bankFullName,
    required String icon,
    required int id,
    String? realName,
    required String status,
    required String tailNumber,
    required int userId,
  }) = _BankAccountModel;

  factory UserBankModel.fromJson(Map<String, dynamic> json) => _$UserBankModelFromJson(json);
}

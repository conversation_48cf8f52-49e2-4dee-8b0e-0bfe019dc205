import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/profile/logic/auth_n/auth_n_cubit.dart';
import 'package:gp_stock_app/shared/routes/routes.dart';

/// 提示先实名认证弹窗
class RealNameTipsDialog {


  static Future<bool>  show({required BuildContext context}) async {
    await showDialog(context: context, builder: (context) => RealNameTipsDialogContent());
    // 检查 context 是否仍然挂载
    if (context.mounted) {
      // 对话框关闭后，重新获取 isVerified 的状态
      return context.read<AuthNCubit>().isVerified;
    } else {
      // 如果 context 不再挂载，返回一个默认值或处理逻辑
      return false;
    }
  }
}

class RealNameTipsDialogContent extends StatelessWidget {
  const RealNameTipsDialogContent({super.key});

  @override
  Widget build(BuildContext context) {
    return Material(
      type: MaterialType.transparency,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: 302.gw,
              height: 232.gw,
              child: Stack(
                clipBehavior: Clip.none,
                children: [
                  SvgPicture.asset("assets/svg/bg_real_name_auth.svg", width: 302.gw, height: 232.gw),
                  Positioned(
                      top: -10.gw,
                      right: 30.gw,
                      child: Image.asset("assets/images/icon_real_name_auth.png", width: 98.gw, height: 98.gw,)),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        height: 63.gw,
                          alignment: Alignment.centerLeft,
                          padding: EdgeInsets.fromLTRB(24.gw, 10.gw, 24.gw, 0),
                          child: Text("authVerification".tr(), style: TextStyle(fontSize: 26, fontWeight: FontWeight.w700, color: Colors.white),)),
                      Container(
                        height: 169.gw,
                        width: 302.gw,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.all(Radius.circular(18.gw)),
                        ),
                        child: Column(
                          children: [
                            SizedBox(height: 38.gw),
                            Text("tip_pls_complete_auth".tr(), style: TextStyle(fontSize: 20, color: Colors.black),),
                            SizedBox(height: 35.gw),
                            GestureDetector(
                              onTap: () {
                                Navigator.pop(context);
                                Navigator.pushNamed(context, routeAuthN);
                              },
                              child: Container(
                                width: 159.gw,
                                height: 43.gw,
                                decoration: BoxDecoration(
                                  gradient: const LinearGradient(
                                    begin: Alignment.topCenter,
                                    end: Alignment.bottomCenter,
                                    colors: [Color(0xFF2662FF), Color(0xFF2EB9FF)],
                                  ),
                                  borderRadius: BorderRadius.circular(21.5),
                                  boxShadow: const [
                                    BoxShadow(
                                      color: Color(0xFFC3E2FF),
                                      offset: Offset(1, 4),
                                      blurRadius: 6,
                                    ),
                                  ],
                                ),
                                alignment: Alignment.center,
                                child: Text(
                                  'go_to_auth_verification'.tr(),
                                  style: TextStyle(
                                    fontWeight: FontWeight.w500,
                                    fontSize: 14,
                                    height: 1.0,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      )
                    ],
                  )
                ],
              ),
            ),
            SizedBox(height: 16.gw),
            InkWell(
              onTap: () => Navigator.pop(context),
              child: SvgPicture.asset("assets/svg/btn_dialog_close.svg", width: 28.gw, height: 28.gw,),
            )
          ],
        ),
      ),
    );
  }
}

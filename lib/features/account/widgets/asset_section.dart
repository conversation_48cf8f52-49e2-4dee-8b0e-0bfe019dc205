import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/domain/models/account_info/account_info_response.dart';
import 'package:gp_stock_app/shared/logic/account_info/account_info_cubit.dart';
import 'package:gp_stock_app/shared/logic/account_info/account_info_state.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:gp_stock_app/shared/theme/my_color_scheme.dart';

import '../../../shared/widgets/flip_text.dart';

class AssetsSection extends StatelessWidget {
  const AssetsSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      margin: EdgeInsets.all(16.gr),
      padding: EdgeInsets.all(16.gr),
      decoration: BoxDecoration(
        color: myColorScheme(context).primaryColor,
        borderRadius: BorderRadius.circular(8.gr),
        boxShadow: [
          BoxShadow(
            color: myColorScheme(context).primaryColor.withNewOpacity(0.2),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Wrap(
        alignment: WrapAlignment.spaceBetween,
        children: [
          Text(
            'totalAssets'.tr(),
            style: FontPalette.bold20.copyWith(
              color: Colors.white.withNewOpacity(0.8),
            ),
          ),
          BlocSelector<AccountInfoCubit, AccountInfoState, AccountInfoData?>(
            selector: (state) => state.accountInfo,
            builder: (context, accountInfo) {
              return FlipText(
                accountInfo?.accountAmount ?? 0,
                isCurrency: true,
                showCurrencyDropdown: true,
                dropdownIconColor: Colors.white.withNewOpacity(0.8),
                style: FontPalette.semiBold24.copyWith(
                  color: Colors.white,
                  fontSize: 25,
                  height: 1.2,
                  fontFamily: 'Akzidenz-Grotesk',
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}

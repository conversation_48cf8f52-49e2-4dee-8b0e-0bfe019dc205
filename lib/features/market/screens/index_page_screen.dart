import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/utils/convert_helper.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/market/domain/models/index_page/index_page_response_order.dart';
import 'package:gp_stock_app/features/market/domain/models/index_page/index_page_response_position.dart';
import 'package:gp_stock_app/features/market/logic/index_page/index_page_cubit.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/theme/color_pallette.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:gp_stock_app/shared/theme/my_color_scheme.dart';
import 'package:gp_stock_app/shared/widgets/flip_text.dart';
import 'package:gp_stock_app/shared/widgets/shadow_box.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';
import 'package:gp_stock_app/shared/widgets/symbol/symbol_chip.dart';

class IndexPageScreen extends StatefulWidget {
  const IndexPageScreen({super.key, this.isTransactionDetail = false});

  final bool isTransactionDetail;
  @override
  State<IndexPageScreen> createState() => _IndexPageScreenState();
}

class _IndexPageScreenState extends State<IndexPageScreen> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    context.read<IndexPageCubit>().init(isTransactionDetail: widget.isTransactionDetail);
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 200) {
      context.read<IndexPageCubit>().loadMore(isTransactionDetail: widget.isTransactionDetail);
    }
  }

  @override
  Widget build(BuildContext context) {
    final isTxnDetail = widget.isTransactionDetail;
    return Scaffold(
      appBar: AppBar(
        title: Text(isTxnDetail ? 'transactionDetails'.tr() : 'entrust_details'.tr()),
      ),
      body: isTxnDetail ? buildIndexPageOrder() : buildIndexPagePosition(),
    );
  }

  BlocBuilder<IndexPageCubit, IndexPageState> buildIndexPageOrder() {
    return BlocBuilder<IndexPageCubit, IndexPageState>(
      builder: (context, state) {
        if (state.indexPageFetchStatus == DataStatus.loading) {
          return _buildLoadingShimmer();
        }

        if (state.indexPageFetchStatus == DataStatus.failed) {
          return Center(
            child: Text(state.error ?? 'error'.tr()),
          );
        }

        final records = state.indexPageOrderResponse.records;
        final hasNext = state.indexPageOrderResponse.hasNext;
        final isLoadingMore = state.indexPageLoadMoreStatus == DataStatus.loading;

        return ListView.separated(
          controller: _scrollController,
          padding: EdgeInsets.symmetric(horizontal: 12.gw, vertical: 8.gh),
          separatorBuilder: (context, index) => SizedBox(height: 16.gh),
          itemCount: records.length + 1,
          itemBuilder: (context, index) {
            if (index >= (records.length)) {
              if (isLoadingMore) {
                return Center(
                  child: Padding(
                    padding: EdgeInsets.symmetric(vertical: 16.gh),
                    child: CupertinoActivityIndicator(),
                  ),
                );
              }
              if (hasNext == false) {
                return Center(
                  child: Padding(
                    padding: EdgeInsets.symmetric(vertical: 16.gh),
                    child: Text(
                      'noMoreData'.tr(),
                      style: FontPalette.normal13.copyWith(color: ColorPalette.greyColor),
                    ),
                  ),
                );
              }
              return SizedBox.shrink();
            }
            final record = records[index];
            return _buildIndexOrderRecordItem(record);
          },
        );
      },
    );
  }

  BlocBuilder<IndexPageCubit, IndexPageState> buildIndexPagePosition() {
    return BlocBuilder<IndexPageCubit, IndexPageState>(
      builder: (context, state) {
        if (state.indexPageFetchStatus == DataStatus.loading) {
          return _buildLoadingShimmer();
        }

        if (state.indexPageFetchStatus == DataStatus.failed) {
          return Center(
            child: Text(state.error ?? 'error'.tr()),
          );
        }

        final records = state.indexPagePositionResponse.records;
        final hasNext = state.indexPagePositionResponse.hasNext;
        final isLoadingMore = state.indexPageLoadMoreStatus == DataStatus.loading;

        return ListView.separated(
          controller: _scrollController,
          padding: EdgeInsets.symmetric(horizontal: 12.gw, vertical: 8.gh),
          separatorBuilder: (context, index) => SizedBox(height: 16.gh),
          itemCount: records.length + 1,
          itemBuilder: (context, index) {
            if (index == records.length) {
              if (isLoadingMore) {
                return Center(
                  child: Padding(
                    padding: EdgeInsets.symmetric(vertical: 16.gh),
                    child: CupertinoActivityIndicator(),
                  ),
                );
              }
              if (hasNext == false) {
                return Center(
                  child: Padding(
                    padding: EdgeInsets.symmetric(vertical: 16.gh),
                    child: Text(
                      'noMoreData'.tr(),
                      style: FontPalette.normal13.copyWith(color: ColorPalette.greyColor),
                    ),
                  ),
                );
              }
              return SizedBox.shrink();
            }
            final record = records[index];
            return _buildIndexRecordItem(record);
          },
        );
      },
    );
  }

  Widget _buildPriceColumn(
    String label,
    String value, {
    bool isWinAmount = false,
    Color? color,
    bool isCenter = false,
    bool isRight = false,
  }) {
    return Expanded(
      child: Column(
        crossAxisAlignment: isCenter
            ? CrossAxisAlignment.center
            : isRight
                ? CrossAxisAlignment.end
                : CrossAxisAlignment.start,
        children: [
          Text(
            label.tr(),
            style: FontPalette.normal11.copyWith(color: ColorPalette.greyColor),
          ),
          Text(
            value,
            style: FontPalette.normal13.copyWith(
              color: color ?? myColorScheme(context).primaryColor,
              fontFamily: 'Akzidenz-Grotesk',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildIndexOrderRecordItem(IndexOrderRecord record) {
    return ShadowBox(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            spacing: 4.gw,
            children: [
              SymbolChip(name: record.market),
              Text(
                record.symbolName,
                style: FontPalette.normal13.copyWith(color: ColorPalette.titleColor),
              ),
              Text(
                record.symbol,
                style: FontPalette.normal13.copyWith(color: ColorPalette.greyColor),
              ),
              Text(
                record.direction == 1 ? 'buy'.tr() : 'sell'.tr(),
                style: FontPalette.normal13
                    .copyWith(color: record.direction == 1 ? ColorPalette.redColor : ColorPalette.greenColor),
              ),
              Spacer(),
              Text(
                'profitLoss'.tr(),
                style: FontPalette.normal12.copyWith(color: ColorPalette.greyColor),
              ),
              Spacer(),
              FlipText(
                record.winAmount,
                suffix: ' ${record.currency}',
                style: TextStyle(
                  color: record.winAmount.getValueColor(context),
                  fontSize: FontPalette.normal13.fontSize,
                ),
              )
            ],
          ),
          SizedBox(height: 8.gh),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildPriceColumn(
                'type'.tr(),
                TradeType.fromValue(record.tradeType).translationKey.tr(),
                color: TradeType.getColor(context, tradeType: TradeType.fromValue(record.tradeType)),
              ),
              _buildPriceColumn(
                record.direction == 2 ? 'sellPrice'.tr() : 'buyPrice'.tr(),
                record.dealPrice.toString(),
                isCenter: true,
              ),
              _buildPriceColumn(
                'quantity'.tr(),
                record.dealNum.toString(),
                isWinAmount: true,
                isRight: true,
              ),
            ],
          ),
          SizedBox(height: 8.gh),
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              _buildPriceColumn('tradeLeverage'.tr(), '${record.tradeUnit}${'xTimes'.tr()}'),
              _buildPriceColumn(
                record.direction == 2 ? 'sellTime'.tr() : 'buyTime'.tr(),
                ConvertHelper.formatDateTypeIn24Hour(record.updateTime.toString(), separator: '-'),
                isWinAmount: true,
                isCenter: true,
              ),
              Spacer(),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildIndexRecordItem(IndexRecord record) {
    return ShadowBox(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            spacing: 4.gw,
            children: [
              SymbolChip(name: record.market),
              Expanded(
                flex: 6,
                child: Text(
                  record.symbolName,
                  style: FontPalette.normal13.copyWith(color: ColorPalette.titleColor),
                ),
              ),
              Text(
                record.symbol,
                style: FontPalette.normal13.copyWith(color: ColorPalette.greyColor),
              ),
              Spacer(),
              FlipText(
                record.winAmount,
                suffix: ' ${record.currency}',
              )
            ],
          ),
          SizedBox(height: 8.gh),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildPriceColumn(
                'type'.tr(),
                TradeType.fromValue(record.tradeType).translationKey.tr(),
                color: TradeType.getColor(context, tradeType: TradeType.fromValue(record.tradeType)),
              ),
              _buildPriceColumn('averageBuyPrice'.tr(), record.buyAvgPrice.toString(), isCenter: true),
              _buildPriceColumn(
                'buyTime'.tr(),
                ConvertHelper.formatDateType1(record.createTime.toString(), separator: '-'),
                isWinAmount: true,
                isRight: true,
              ),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildPriceColumn('tradeLeverage'.tr(), record.tradeUnit.toString()),
              _buildPriceColumn('averageSellPrice'.tr(), record.sellAvgPrice.toString(), isCenter: true),
              _buildPriceColumn(
                'sellTime'.tr(),
                ConvertHelper.formatDateType1(record.updateTime.toString(), separator: '-'),
                isWinAmount: true,
                isRight: true,
              ),
            ],
          ),
        ],
      ),
    );
  }
}

Widget _buildLoadingShimmer() {
  return ListView.separated(
    padding: EdgeInsets.symmetric(horizontal: 16.gw, vertical: 8.gh),
    separatorBuilder: (context, index) => SizedBox(height: 16.gh),
    itemCount: 10,
    itemBuilder: (context, index) => ShadowBox(
      child: ShimmerWidget(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                ShimmerWidget(
                  width: 60.gw,
                  height: 24.gh,
                ),
                8.horizontalSpace,
                ShimmerWidget(
                  width: 100.gw,
                  height: 16.gh,
                ),
                Spacer(),
                ShimmerWidget(
                  width: 80.gw,
                  height: 16.gh,
                ),
              ],
            ),
            12.verticalSpace,
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: List.generate(
                3,
                (index) => ShimmerWidget(
                  width: 100.gw,
                  height: 32.gh,
                ),
              ),
            ),
            8.verticalSpace,
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: List.generate(
                3,
                (index) => Container(
                  width: 100.gw,
                  height: 32.gh,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(4.gr),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    ),
  );
}

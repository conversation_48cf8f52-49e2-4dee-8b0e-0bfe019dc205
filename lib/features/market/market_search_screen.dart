import 'dart:async';
import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/widgets/table_empty.dart';
import 'package:gp_stock_app/shared/app/extension/string_extension.dart';

import '../../shared/constants/enums.dart';
import '../../shared/models/route_arguments/trading_arguments.dart';
import '../../shared/routes/routes.dart';
import '../../shared/theme/color_pallette.dart';
import '../../shared/theme/font_pallette.dart';
import '../../shared/theme/my_color_scheme.dart';
import '../../shared/widgets/symbol/symbol_chip.dart';
import 'domain/models/search/market_search.dart';
import 'logic/search/search_cubit.dart';

class MarketSearchScreen extends StatefulWidget {
  final void Function(MarketSearchData)? onSelect;

  const MarketSearchScreen({super.key, this.onSelect});

  @override
  State<MarketSearchScreen> createState() => _MarketSearchScreenState();
}

class _MarketSearchScreenState extends State<MarketSearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  Timer? _debounce;

  @override
  void initState() {
    super.initState();
    context.read<SearchCubit>().loadFromHistory();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _debounce?.cancel();
    super.dispose();
  }

  void _onSearchChanged(String query) {
    if (_debounce?.isActive ?? false) _debounce!.cancel();

    if (query.isEmpty) {
      context.read<SearchCubit>().resetSearch();
      return;
    }
    final bool isChineseText = RegExp(r'[\u4e00-\u9fff]').hasMatch(query);
    final debounceTime = isChineseText ? 200 : 400;
    final minLength = isChineseText ? 1 : 2;

    if (query.length < minLength) {
      return;
    }

    if (query.length >= minLength && !isChineseText && Platform.isAndroid) {
      context.read<SearchCubit>().setSearching();
    }

    _debounce = Timer(Duration(milliseconds: debounceTime), () {
      if (query.length >= minLength) {
        context.read<SearchCubit>().fetchSearchResults(query.trim());
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: myColorScheme(context).backgroundColor,
        surfaceTintColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios_outlined,
            color: ColorPalette.titleColor,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Container(
          decoration:
              BoxDecoration(color: myColorScheme(context).cardColor, borderRadius: BorderRadius.circular(20.gr)),
          child: BlocBuilder<SearchCubit, SearchState>(
            builder: (context, state) {
              return TextField(
                controller: _searchController,
                autofocus: true,
                onChanged: _onSearchChanged,
                style: FontPalette.normal16,
                decoration: InputDecoration(
                  hintText: 'searchPlaceholder'.tr(),
                  hintStyle: FontPalette.normal16.copyWith(
                    color: ColorPalette.subTitleColor,
                  ),
                  border: InputBorder.none,
                  focusedBorder: InputBorder.none,
                  prefixIcon: Icon(
                    Icons.search,
                    color: ColorPalette.subTitleColor,
                  ),
                  contentPadding: EdgeInsets.symmetric(vertical: 12.gh),
                  suffixIconConstraints: BoxConstraints(
                    minWidth: 5.gw,
                    minHeight: 5.gh,
                  ),
                  suffixIcon: state.searchValue?.isNotNullNorEmpty ?? false
                      ? Padding(
                          padding: EdgeInsets.only(right: 10.gw),
                          child: InkWell(
                            onTap: () => {
                              _searchController.clear(),
                              context.read<SearchCubit>().resetSearch(),
                            },
                            child: Container(
                              decoration: ShapeDecoration(color: ColorPalette.subTitleColor, shape: CircleBorder()),
                              child: Icon(
                                Icons.close,
                                size: 15.gh,
                                color: ColorPalette.whiteColor,
                              ),
                            ),
                          ),
                        )
                      : null,
                ),
              );
            },
          ),
        ),
        actions: [TextButton(onPressed: () => Navigator.pop(context), child: Text('cancel'.tr()))],
      ),
      body: BlocBuilder<SearchCubit, SearchState>(
        builder: (context, state) {
          if (state.searchFetchStatus == DataStatus.loading) {
            return const Center(child: CircularProgressIndicator.adaptive());
          }
          final searchResults = state.searchData;
          if (state.searchValue == null || state.searchValue!.isEmpty) {
            return SearchHistory(onSelect: widget.onSelect,);
          }

          if (searchResults == null || searchResults.isEmpty) {
            return Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Center(
                  child: TableEmptyWidget(
                    width: 60.gh,
                    height: 60.gh,
                  ),
                ),
              ],
            );
          }

          return ListView.separated(
            itemCount: searchResults.length,
            itemBuilder: (context, index) {
              final item = searchResults[index];
              return _SearchResultTile(
                item: item,
                onSelect: widget.onSelect,
              );
            },
            separatorBuilder: (context, index) => Divider(
              color: myColorScheme(context).cardColor,
            ),
          );
        },
      ),
    );
  }
}

class _SearchResultTile extends StatelessWidget {
  final MarketSearchData item;
  final bool showClear;
  final void Function(MarketSearchData)? onSelect;

  const _SearchResultTile({
    required this.item,
    this.showClear = false,
    this.onSelect,
  });

  @override
  Widget build(BuildContext context) {
    String getDisplayName(MarketSearchData item) {
      if (!(item.name?.zhHk.isNullOrEmpty ?? true)) {
        return item.name!.zhHk!;
      } else if (!(item.name?.enUs.isNullOrEmpty ?? true)) {
        return item.name!.enUs!;
      } else if (!(item.name?.zhCn.isNullOrEmpty ?? true)) {
        return item.name!.zhCn!;
      }
      return item.symbol ?? '';
    }

    return InkWell(
      onTap: () {
        context.verifyAuth(() {
          if (!showClear) context.read<SearchCubit>().addSearchItem(item: item);
          if (onSelect != null) {
            onSelect!(item);
            Navigator.pop(context);
          } else {
            Navigator.pushNamed(
              context,
              routeTradingCenter,
              arguments: TradingArguments(
                instrumentInfo: item.getInstrument,
                selectedIndex: TradeTabType.Trading.index,
                isIndexTrading: item.isIndex,
              ),
            );
          }
        });
      },
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: 20.gw
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              getDisplayName(item),
              style: FontPalette.normal12.copyWith(
                color: myColorScheme(context).textColor,
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Row(
                  spacing: 4.gh,
                  children: [
                    SymbolChip(
                      name: item.market ?? '',
                      chipColor: ColorPalette.primaryColor,
                    ),
                    Text(item.symbol ?? '',
                        style: FontPalette.semiBold10.copyWith(color: myColorScheme(context).textColor2)),
                  ],
                ),
                if (showClear)
                  GestureDetector(
                      onTap: () => context.read<SearchCubit>().clearSearchHistoryItem(item), child: Icon(Icons.clear)),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class SearchHistory extends StatelessWidget {
  final void Function(MarketSearchData)? onSelect;
  const SearchHistory({super.key, this.onSelect});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<SearchCubit, SearchState>(
      builder: (context, state) {
        List<MarketSearchData> data = [];
        if (state.searchData != null) {
          data = state.searchData!;
        } else {
          data = state.searchDataHistory ?? [];
        }

        return Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.only(left: 20.0.gw, top: 20.gh),
              child: Text(
                'searchHistory'.tr(),
                style: FontPalette.bold14.copyWith(color: myColorScheme(context).subTitleColor),
              ),
            ),
            Expanded(
              child: data.isEmpty
                  ? Center(
                      child: Text(
                        'noSearchHistory'.tr(),
                        style: FontPalette.normal14.copyWith(color: myColorScheme(context).subTitleColor),
                      ),
                    )
                  : ListView.separated(
                      padding: EdgeInsets.symmetric(vertical: 8.gh),
                      itemCount: data.length,
                      itemBuilder: (context, index) {
                        final item = data[index];
                        return _SearchResultTile(
                          item: item,
                          showClear: true,
                          onSelect: onSelect,
                        );
                      },
                      separatorBuilder: (context, index) => Divider(
                        color: myColorScheme(context).cardColor,
                      ),
                    ),
            ),
            if (data.isNotEmpty)
              Padding(
                padding: EdgeInsets.symmetric(vertical: 16.gh),
                child: Center(
                  child: GestureDetector(
                    onTap: () => context.read<SearchCubit>().clearAllSearchHistory(),
                    child: Text(
                      'clearSearchHistory'.tr(),
                      style: FontPalette.bold14.copyWith(
                        color: ColorPalette.primaryColor,
                      ),
                    ),
                  ),
                ),
              ),
          ],
        );
      },
    );
  }
}

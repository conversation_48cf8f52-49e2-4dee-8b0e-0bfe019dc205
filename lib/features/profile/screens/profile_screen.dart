import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/features/profile/widgets/profile/profile_service.dart';
import 'package:gp_stock_app/shared/logic/account_info/account_info_cubit.dart';

import '../logic/auth_n/auth_n_cubit.dart';
import '../logic/profile/profile_cubit.dart';
import '../widgets/profile/profile_action_button.dart';
import '../widgets/profile/profile_header.dart';

class ProfileScreen extends StatelessWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      body: RefreshIndicator(
        onRefresh: () async {
          await Future.wait([
            context.read<AccountInfoCubit>().getAccountInfo(),
            context.read<ProfileCubit>().getUserInfo(),
            context.read<AuthNCubit>().getAuthNInfo(),
          ]);
        },
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: AnimationLimiter(
            child: Column(
              children: AnimationConfiguration.toStaggeredList(
                duration: const Duration(milliseconds: 600),
                childAnimationBuilder: (widget) => SlideAnimation(
                  verticalOffset: 30.0,
                  child: FadeInAnimation(
                    child: widget,
                  ),
                ),
                children: [
                  ProfileHeader(),
                  12.verticalSpace,
                  ProfileServiceConsult(),
                  12.verticalSpace,
                  ProfileActionButton(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

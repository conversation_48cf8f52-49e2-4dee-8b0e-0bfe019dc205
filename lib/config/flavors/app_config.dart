import 'package:flutter/material.dart';

/// Defines the flavor of the app
enum Flavor {
  pre, // 预发布环境
  gp, // 测试环境
  rsyp, // 荣顺优配
  yhxt, // 沅和信投
}

/// Configuration for the app based on flavor
class AppConfig {
  final Flavor flavor;
  final String appName;
  final String siteId;
  final String environment;
  final String baseUrl;
  final String marketWsUrl;
  final String inviteLinkUrl;
  final Color primaryColor;
  final String encryptionKey;
  final String wangYiCaptchaKey;
  final List<String> ossUrls;

  static AppConfig? _instance;

  factory AppConfig({
    required Flavor flavor,
    required String appName,
    required String siteId,
    required String environment,
    required String baseUrl,
    required String marketWsUrl,
    required String inviteLinkUrl,
    required Color primaryColor,
    required String encryptionKey,
    required String wangYiCaptchaKey,
    required List<String> ossUrls,
  }) {
    _instance ??= AppConfig._internal(
      flavor: flavor,
      appName: appName,
      siteId: siteId,
      environment: environment,
      baseUrl: baseUrl,
      marketWsUrl: marketWsUrl,
      inviteLinkUrl: inviteLinkUrl,
      primaryColor: primaryColor,
      encryptionKey: encryptionKey,
      wangYiCaptchaKey: wangYiCaptchaKey,
      ossUrls: ossUrls,
    );
    return _instance!;
  }

  AppConfig._internal({
    required this.flavor,
    required this.appName,
    required this.siteId,
    required this.environment,
    required this.baseUrl,
    required this.marketWsUrl,
    required this.inviteLinkUrl,
    required this.primaryColor,
    required this.encryptionKey,
    required this.wangYiCaptchaKey,
    required this.ossUrls,
  });

  static AppConfig get instance {
    assert(_instance != null, 'AppConfig has not been initialized');
    return _instance!;
  }

  bool get isDev => flavor == Flavor.gp;

  String get flavorName => flavor.name;
  String get flavorTitle => flavor.name.toUpperCase();
}

import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/theme/color_pallette.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';

class AppDropdown<T> extends StatelessWidget {
  final List<DropdownMenuItem<T>> items;
  final T? selected;
  final ValueChanged<T?>? onChanged;
  final String? hintText;
  final List<Widget> Function(BuildContext)? selectedItemBuilder;
  final double? height;
  const AppDropdown({
    super.key,
    required this.items,
    required this.selected,
    required this.onChanged,
    this.hintText,
    this.selectedItemBuilder,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    return InputDecorator(
      decoration: InputDecoration(
        floatingLabelBehavior: FloatingLabelBehavior.never,
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(5.gr),
          borderSide: BorderSide(color: ColorPalette.primaryColor),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(5.gr),
          borderSide: BorderSide(color: Colors.transparent),
        ),
        fillColor: ColorPalette.textFieldBackgroundColor,
        filled: true,
        isDense: true,
        hintStyle: FontPalette.normal13.copyWith(color: ColorPalette.titleColor),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(5.gr),
          borderSide: BorderSide(color: ColorPalette.primaryColor.withAlpha(128)),
        ),
        contentPadding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 0.0),
      ),
      child: DropdownButtonHideUnderline(
        child: SizedBox(
          height: height ?? 37.gh,
          child: DropdownButton(
            isDense: true,
            isExpanded: true,
            hint: Text(
              hintText ?? '',
              style: FontPalette.normal13.copyWith(color: ColorPalette.titleColor),
            ),
            selectedItemBuilder: selectedItemBuilder,
            value: selected,
            items: items,
            icon: Padding(
              padding: const EdgeInsets.only(right: 5),
              child: Icon(Icons.keyboard_arrow_down_sharp, size: 15.gr, color: ColorPalette.titleColor),
            ),
            onChanged: onChanged,
          ),
        ),
      ),
    );
  }
}

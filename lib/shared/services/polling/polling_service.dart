import 'dart:async';

import 'package:injectable/injectable.dart';

/// A service that provides polling functionality for periodic data fetching.
///
/// This service allows components to start and stop polling operations with
/// configurable intervals. It's designed to be used across different cubits
/// that need to periodically refresh their data.
///
@singleton
class PollingService {
  /// Map of active polling timers, keyed by a unique identifier
  final Map<String, Timer> _activePolls = {};

  /// Starts a polling operation with the given callback function and interval.
  ///
  /// [id] - A unique identifier for this polling operation
  /// [callback] - The function to call at each interval
  /// [interval] - The time between each call (defaults to 5 seconds)
  /// Returns true if polling was started, false if it was already active
  bool startPolling(String id, void Function(bool isActive) callback, {Duration? interval}) {
    // If polling is already active for this ID, don't start a new one
    if (_activePolls.containsKey(id)) {
      return false;
    }

    // Create and store the timer
    try {
      final timer = Timer.periodic(
        interval ?? const Duration(seconds: 5),
        (_) {
          if (_activePolls.containsKey(id)) {
            callback(true); // Always pass true since the timer is running
          }
        },
      );

      // Store the timer
      _activePolls[id] = timer;

      // Execute callback immediately for initial data
      callback(true);
      return true;
    } catch (e) {
      _activePolls.remove(id);
      return false;
    }
  }

  /// Stops an active polling operation.
  ///
  /// [id] - The unique identifier of the polling operation to stop
  /// Returns true if polling was stopped, false if no active polling was found
  bool stopPolling(String id) {
    final timer = _activePolls[id];
    if (timer != null) {
      timer.cancel();
      _activePolls.remove(id);
      return true;
    }
    return false;
  }

  /// Stops all active polling operations.
  void stopAllPolling() {
    for (final timer in _activePolls.values) {
      timer.cancel();
    }
    _activePolls.clear();
  }

  /// Checks if polling is active for the given ID.
  ///
  /// [id] - The unique identifier to check
  /// Returns true if polling is active, false otherwise
  bool isPollingActive(String id) {
    return _activePolls.containsKey(id);
  }
}

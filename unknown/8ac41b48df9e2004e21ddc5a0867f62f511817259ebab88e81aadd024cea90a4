<template>
    <div class="c-count-to font-digit">
        <span v-if="prefix">{{ prefix }}</span>
        <span ref="target">
            {{ count }}
        </span>
        <span v-if="suffix">{{ suffix }}</span>
    </div>
</template>

<script setup>
import { CountUp } from 'countup.js'
import _ from 'lodash'

const domRef = useTemplateRef('target')

const { count, precision } = defineProps({
    count: {
        type: Number,
        required: true,
    },
    precision: {
        type: Number,
        default: 2,
    },
    prefix: String,
    suffix: String,
})

const countUpInstance = ref()

onMounted(async () => {
    // https://github.com/inorganik/countUp.js
    countUpInstance.value = new CountUp(
        domRef.value,
        count,
        {
            decimalPlaces: precision,
            duration: .5,

            // startVal: 0, // 动画的起始值 (默认 0)
            // decimalPlaces?: number; // 小数点后保留的位数 (默认 0)
            // duration?: number; // 动画持续时间（以秒为单位） (默认 2)
            // useGrouping?: boolean; // 是否使用分组，例如 1,000 与 1000 (默认 true)
            // useIndianSeparators?: boolean; // 是否使用印度数字分组，例如 1,00,000 与 100,000 (默认 false)
            // useEasing: false, // 是否使用缓动效果 (默认 true)
            // smartEasingThreshold?: number; // 如果启用缓动，超过此值的大数使用平滑缓动 (默认 999)
            // smartEasingAmount?: number; // 超过阈值的大数的缓动范围 (默认 333)
            // separator?: string; // 分组分隔符 (默认 ',')
            // decimal?: string; // 小数点符号 (默认 '.')
            // easingFn?: (t: number, b: number, c: number, d: number) => number; // 缓动函数 (默认 easeOutExpo)
            // formattingFn?: (n: number) => string; // 格式化结果的自定义函数
            // prefix?: string; // 在结果前添加的文本
            // suffix?: string; // 在结果后添加的文本
            // numerals?: string[]; // 替代数字字形的数组
            // enableScrollSpy?: boolean; // 目标进入视图时启动动画 (默认 false)
            // scrollSpyDelay?: number; // 目标进入视图后延迟启动动画的时间（毫秒） (默认 0)
            // scrollSpyOnce?: boolean; // 动画是否仅运行一次 (默认 false)
            // onCompleteCallback?: () => any; // 动画完成后调用的回调函数
            // onStartCallback?: () => any; // 动画开始时调用的回调函数
            // plugin?: CountUpPlugin; // 用于替代动画的插件
        },
    )
    countUpInstance.value.start()
})

watch(() => count, (newVal, oldVal) => {
    if (newVal !== oldVal && !_.isNaN(newVal) && countUpInstance.value) {
        countUpInstance.value.reset()
        countUpInstance.value.update(newVal, { decimalPlaces: precision })
        countUpInstance.value.start()
    }
})

defineOptions({ name: 'C-Count-To' })
</script>

<style scoped>
</style>

<template>
    <img
        :src
        :alt="alt ?? `asyncImage-${uid}`"
    >
</template>

<script setup>
import { $theme } from '@/store/index.js'

const uid = useId()

const src = ref()

const { name, theme, alt } = defineProps({
    name: {
        type: String,
        required: true,
    },
    theme: Boolean,
    alt: String,
})

// 加载图片资源
const onLoadAssets = async () => {
    const file = await import(`./assets/${name}${theme ? `_${unref($theme)}` : ''}.png`)
    src.value = file.default
}

onLoadAssets()

// 监听主题变化
watch($theme, () => {
    if (theme) onLoadAssets()
})

// 异步加载图片组件
defineOptions({ name: 'C-Async-Image' })
</script>

<style scoped>

</style>

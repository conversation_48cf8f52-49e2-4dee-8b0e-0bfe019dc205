import { createI18n } from 'vue-i18n'
import { Locale } from 'vant'
import zhCN from 'vant/es/locale/lang/zh-C<PERSON>'
import zhHK from 'vant/es/locale/lang/zh-HK'
import enUS from 'vant/es/locale/lang/en-US'

import zh_cn from './zh_cn.json'
import zh_hans from './zh_hans.json'
import en_us from './en_us.json'
import { useGlobalStore, useNoticeStore } from '@/store/index.js'
import socket from '@/socket.js'

export const LANGUAGE = {
    zhCN: 'zh-CN',
    zhHK: 'zh-HK',
    enUS: 'en-US',
}

const vantLocale = {
    [LANGUAGE.zhCN]: zhCN,
    [LANGUAGE.zhHK]: zhHK,
    [LANGUAGE.enUS]: enUS,
}

export const localeStorage = useLocalStorage('locale', LANGUAGE.zhCN)

const i18n = createI18n({
    legacy: false,
    locale: localeStorage.value,
    fallbackLocale: LANGUAGE.zhCN,
    messages: {
        [LANGUAGE.zhCN]: zh_cn,
        [LANGUAGE.zhHK]: zh_hans,
        [LANGUAGE.enUS]: en_us,
    },
})

const onChangeStockLanguage = language => {
    socket.emit({
        type: 'clientInfo',
        action: 'setClientLanguage',
        params: {
            language,
        },
    })
}
onChangeStockLanguage(localeStorage.value)

export const onChangeLanguage = (lang) => {
    const { dispatch_refreshNotice } = useNoticeStore(),
        { $globalConfig } = storeToRefs(useGlobalStore())

    if (i18n.global.locale.value !== lang) {
        // 刷新公告数据
        dispatch_refreshNotice()

        document.documentElement.lang = lang
        i18n.global.locale.value = lang
        localeStorage.value = lang
        Locale.use(lang, vantLocale[lang])

        // 修改标题
        useTitle($globalConfig.value.title[lang])

        onChangeStockLanguage(lang)
    }
}

document.documentElement.lang = localeStorage.value
Locale.use(localeStorage.value, vantLocale[localeStorage.value])

export default i18n

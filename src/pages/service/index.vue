<template>
    <c-header v-if="showHeader" :title="t('header.service')"/>

    <div :class="[ showHeader ? 'with-header-container__noPadding' : 'h-full' ]">
        <Wujie
            v-if="initial"
            name="service"
            :url
            class="h-full"
            alive
            :props="{
                user: {
                    SDKAppID: +res.sdkAppId,
                    userID: res.userId,
                    userSig: res.userSig
                },
                agent: pImAccount
            }"
        />
    </div>
</template>

<script setup>
import { bus } from 'wujie'
import Wujie from 'wujie-vue3'

const { params: { pImAccount } } = useRoute(),
    { back } = useRouter()

const { $globalConfig } = storeToRefs(useGlobalStore()),
    url = $globalConfig.value.chat
    // url = 'http://**************:5566/'

const { t } = useI18n()

const showHeader = ref(true),
    loading = showLoadingToast(t('common.loading'))

const closeLoading = () => {
    loading.close()
}

// const { res: agent } = useRequest({
//     // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/tencent-IM%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/getUserKefuUsingGET_1
//     url: '/tc/getUserKefu',
//     initialValues: {
//         imAccount: '',
//         pImAccount: '',
//     },
// })

const { res, initial } = useRequest({
    // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/tencent-IM%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/userSigUsingPOST_1
    url: '/tc/userSig',
    method: FETCH_METHOD.POST,
    initialValues: {
        imImage: '',
        nickName: '',
        sdkAppId: '',
        userId: '',
        userSig: '',
    },
    onSuccess: closeLoading,
    onErr: () => {
        loading.close()
        back()
    },
})

bus.$on('tencentLoggedIn', closeLoading)
bus.$on('tencentLoginFailed', closeLoading)
bus.$on('entryChat', () => {
    showHeader.value = false
})
bus.$on('exitChat', () => {
    showHeader.value = true
})

onBeforeUnmount(closeLoading)

defineOptions({ name: 'service' })
</script>

<style scoped>

</style>

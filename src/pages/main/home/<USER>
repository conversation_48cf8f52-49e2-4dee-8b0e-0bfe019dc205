<template>
    <div class="home with-header-container">
        <div class="flex-between mt-2 gap-5 overflow-hidden">
            <c-image-box
                async
                theme
                data-aos="fade-left"
                v-for="({ title, bg, to, handler }, index) in nav"
                :data-aos-delay="`${index * 50}`"
                :key="index"
                :source="bg"
                :width="92"
                :height="32"
                @click="handler ? handler() : $router.push(to)"
            >
                <div class="text-xs text-primary leading-8 pl-9 whitespace-nowrap">
                    {{ title }}
                </div>
            </c-image-box>
        </div>

        <!-- 轮播图 -->
        <van-skeleton :loading="!carouselInitial">
            <template #template>
                <van-skeleton-image data-aos="fade-left" class="w-full! h-[126px]! mt-3 mb-4"/>
            </template>

            <van-swipe class="mt-4" data-aos="fade-left">
                <van-swipe-item
                    v-for="({ id, title, imageUrl, jumpType, jumpUrl }) in carousel"
                    :key="id"
                    @click="utils_jump(jumpType, jumpUrl)"
                >
                    <van-image
                        class="w-full h-[126px] rounded-lg overflow-hidden"
                        :src="imageUrl"
                        :alt="title"
                    />
                </van-swipe-item>
            </van-swipe>
        </van-skeleton>
        <!-- 轮播图 -->

        <!-- 滚动公告 -->
        <van-notice-bar
            ref="marqueeRef"
            data-aos="fade-left"
            data-aos-delay="50"
            class="mt-1 rounded-md"
            scrollable
            background="var(--bg)"
            color="var(--text)"
            left-icon="volume-o"
        >
            <div class="flex-middle gap-4">
                <div
                    v-for="({ id, content }) in $notice.marquee"
                    :key="id"
                    v-html="content"
                />
            </div>
        </van-notice-bar>
        <!-- 滚动公告 -->

        <div
            class="text-sm flex gap-5 text-title h-13 items-center"
            data-aos="fade-left"
            data-aos-delay="100"
            shrink
        >
            <span class="relative" :class="stockTabActive2 === 0 ? 'activeTav' : ''" @click="stockTabActive2 = 0">{{
                    t('stock.index')
                }}</span>
            <span class="relative" :class="stockTabActive2 === 1 ? 'activeTav' : ''" @click="stockTabActive2 = 1">{{
                    t('stock.title')
                }}</span>
            <span class="relative" :class="stockTabActive2 === 2 ? 'activeTav' : ''" @click="stockTabActive2 = 2">{{
                    t('stock.collect')
                }}</span>
        </div>
        <IndexTrend
            v-if="stockTabActive2 === 0"
            class="mt-2.5"
            @select="onRedirectIndexDetails"
        />
        <van-tabs
            v-if="stockTabActive2 === 1"
            shrink
            v-model:active="$marketType"
            style="--van-tab-font-size: 12px;"
        >
            <template #nav-right>
                <router-link to="/quotes/stock" class="text-xs ml-auto">
                    <span>{{ t('common.more') }}</span>
                    <van-icon name="arrow"/>
                </router-link>
            </template>

            <van-tab
                class="pt-2.5"
                v-for="(_, symbol) in STOCK_CONFIG"
                :key="symbol"
                :name="symbol"
                :title="t(`stock.${symbol}`)"
            >
                <StockHot :market="symbol"/>
            </van-tab>
        </van-tabs>
        <c-table
            v-if="stockTabActive2 === 2"
            data-aos="fade-left"
            :columns
            :onRefresh="onRefreshCollect"
            :data-source="collectList"
            v-model:refresh-loading="collectRefreshLoading"
            @row-click="dispatch_checkStock"
            @filter="onFilterCollect"
        />
        <van-tabs
            class="h-full"
            data-aos="fade-left"
            data-aos-anchor="#app"
            shrink
            v-model:active="$activityActiveTab"
        >
            <!-- 新闻 -->
            <van-tab
                :title="t('header.news')"
                name="news"
                class="pt-2.5"
            >
                <NewsList/>
            </van-tab>
            <!-- 新闻 -->

            <!-- 活动 -->
            <van-tab
                :title="t('header.activity')"
                name="activity"
                class="pt-2.5"
            >
                <NoticeList :type="NOTICE_TYPE.ACTIVITY"/>
            </van-tab>
            <!-- 活动 -->

            <template #nav-right>
                <router-link to="/activity" class="text-xs ml-auto">
                    <span>{{ t('common.more') }}</span>
                    <van-icon name="arrow"/>
                </router-link>
            </template>
        </van-tabs>

        <van-floating-bubble
            class="download"
            axis="xy"
            magnetic="x"
            :offset="{ y: 240 }"
            :gap="{ x: 20, y: 60 }"
            @click="onDownload"
        >
            <div>APP</div>
            <div>下载</div>
        </van-floating-bubble>
    </div>
</template>

<script setup>
import { utils_jump, utils_link } from '@/utils'
import { STOCK_CONFIG } from '@/config'
import { $activityActiveTab, NOTICE_TYPE } from '@/pages/main/activity/store.js'
import NewsList from '@/pages/main/news/components/NewsList.vue'
import NoticeList from '@/pages/main/activity/components/NoticeList.vue'
import IndexTrend from '@/pages/main/quotes/routes/index/components/IndexTrend.vue'
import StockHot from './components/StockHot.vue'

const { $notice } = storeToRefs(useNoticeStore()),
    { dispatch_checkStock } = useStockStore(),
    { $marketType } = storeToRefs(useQuotesStore()),
    { $globalConfig } = storeToRefs(useGlobalStore())

const onDownload = useDownload()

// 轮播图
const {
    res: carousel,
    initial: carouselInitial,
} = useRequest({
    // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/Carousel%20function%20controller/getBannerPageUsingGET_1
    url: '/banner/page',
    initialValues: [],
})

const columns = useStockColumns()

const {
    list: collectList,
    refreshLoading: collectRefreshLoading,
    onRefresh: onRefreshCollect,
    onFilter: onFilterCollect,
} = useStockCollect({}, {
    paginationOptions: {
        pageSize: 5,
    },
})

const stockTabActive2 = ref(0)
const onRedirectIndexDetails = (instrument) => {
    const [ market, securityType, symbol ] = instrument.split('|')
    dispatch_checkStock({ market, securityType, symbol })
}

const marqueeRefRef = useTemplateRef('marqueeRef')
const onResetMarquee = () => {
    marqueeRefRef.value.reset()
}
watch($theme, onResetMarquee)
watch($notice, onResetMarquee)

const nav = computed(() => [
    {
        title: t('header.about'),
        bg: 'nav_4',
        to: '/about',
    },
    {
        title: t('_ai'),
        bg: 'nav_5',
        to: '/ai',
    },
    {
        title: t('header.service'),
        bg: 'nav_3',
        handler: () => {
            utils_link($globalConfig.value.service)
        },
    },
])

const { t } = useI18n({
    useScope: 'global',
    messages: {
        [LANGUAGE.zhCN]: {
            _ai: 'AI分析',
        },
        [LANGUAGE.zhHK]: {
            _ai: 'AI分析',
        },
        [LANGUAGE.enUS]: {
            _ai: 'AI',
        },
    },
})

defineOptions({ name: 'Home' })
</script>

<style scoped>
.activeTav {
    font-weight: bold;
    color: var(--active);
}

.activeTav::after {
    content: ' ';
    position: absolute;
    z-index: 1;
    width: var(--van-tabs-bottom-bar-width);
    height: var(--van-tabs-bottom-bar-height);
    background: var(--van-tabs-bottom-bar-color);
    border-radius: var(--van-tabs-bottom-bar-height);
    left: 50%;
    bottom: -12px;
    transform: translateX(-50%);
}

</style>

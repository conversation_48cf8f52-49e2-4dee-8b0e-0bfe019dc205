<template>
    <c-table
        :columns
        :data-source="list"
        :finished
        :onRefresh
        v-model:refresh-loading="refreshLoading"
        v-model:load-loading="loadLoading"
        @filter="onFilter"
        @row-click="dispatch_checkStock"
    />
</template>

<script setup>
const { market } = defineProps({
    market: {
        type: String,
        required: true,
    },
})

const { dispatch_checkStock } = useStockStore()

const columns = useStockColumns()

// 热门股票
const [
    {
        list,
        refreshLoading,
        loadLoading,
        finished,
        onRefresh,
        onFilter,
    },
] = useMarketStocks({
    params: {
        marketType: market,
        securityType: 1,
        ...DEFAULT_STOCK_SORT,
    },
    cancellable: false,
}, {
    paginationOptions: {
        pageSize: 5,
    },
})

defineOptions({ name: 'StockHot' })
</script>

<style scoped>

</style>

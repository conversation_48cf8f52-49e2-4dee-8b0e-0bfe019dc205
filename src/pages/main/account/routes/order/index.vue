<template>
    <c-header :title="t('_title')"/>

    <div class="with-header-container__noPadding px-2.5">
        <van-tabs
            class="h-full"
            data-aos="fade-left"
            data-aos-delay="50"
        >
            <van-tab
                class="py-2.5"
                name="deal"
                :title="t('_deal')"
            >
                <DealTable class="h-full" @row-click=""/>
            </van-tab>

            <van-tab
                class="py-2.5"
                name="entrust"
                :title="t('_entrust')"
            >
                <EntrustTable class="h-full" @row-click=""/>
            </van-tab>
        </van-tabs>
    </div>
</template>

<script setup>
const { idParams } = useAccountId()

const { Table: DealTable } = useDealTable({
        params: { ...idParams, status: 2 }
    }),
    { Table: EntrustTable } = useEntrustTable({
        params: idParams
    })

const { t } = useI18n({
    useScope: 'local',
    messages: {
        [LANGUAGE.zhCN]: {
            _title: '历史订单',
            _deal: '历史成交',
            _entrust: '历史委托',
        },
        [LANGUAGE.zhHK]: {
            _title: '历史订单',
            _deal: '历史成交',
            _entrust: '历史委托',
        },
        [LANGUAGE.enUS]: {
            _title: 'Order History',
            _deal: 'Deal History',
            _entrust: 'Transaction History',
        },
    },
})

defineOptions({ name: 'account-order' })
</script>

<style scoped>

</style>

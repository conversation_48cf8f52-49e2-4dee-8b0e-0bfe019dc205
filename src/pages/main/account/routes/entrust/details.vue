<template>
    <c-header :title="t('stock.entrust.details')"/>

    <div class="with-header-container">
        <c-card>
            <c-description-group
                :items="baseInfo"
                :data-source="res"
            />
        </c-card>

        <c-card
            data-aos-delay="50"
            class="mt-2.5"
        >
            <c-description-group
                :items="details"
                :data-source="res"
            />
        </c-card>
    </div>
</template>

<script setup>
import StockBaseInfo from '@/pages/main/stock/components/StockBaseInfo.vue'

const { params: { id } } = useRoute()

const { res, initial } = useRequest({
    // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/app%E7%AB%AF-%E8%AE%A2%E5%8D%95%E7%9B%B8%E5%85%B3/getByIdUsingGET_1
    url: '/order/getById',
    params: {
        id,
    },
    initialValues: {
        cancelTime: '',
        currency: '',
        dealNum: 0,
        dealPrice: 0,
        dealTime: '',
        direction: 0,
        id: 0,
        market: '',
        priceType: 0,
        securityType: '',
        status: 0,
        stockPrice: 0,
        symbol: '',
        symbolName: '',
        tradeNum: 0,
        tradePrice: 0,
        tradeTime: '',
        tradeType: 0,
        transactionAmount: 0,
        type: 0,
        costPrice: 0,
        winAmount: 0,
        tradeFee: 0,
        contractId: 0,
        contractType: 0,
        multiple: 0,
        periodType: 0,
        tradeRate: 0,
    },
})

const baseInfo = computed(() => [
    {
        label: '',
        valueClass: 'flex items-center justify-end',
        render: () => {
            const { symbolName, symbol, market } = res.value

            return h(
                StockBaseInfo,
                {
                    inline: true,
                    name: symbolName,
                    symbol,
                    market
                }
            )
        }
    },
    {
        label: t('account.type'),
        render: () => {
            const { securityType, contractType, periodType, multiple, contractId } = res.value

            if (!initial.value) return '-'

            return contractId
                ? utils_contract_name({
                    marketType: securityType,
                    type: contractType,
                    periodType: periodType,
                    multiple,
                    id: contractId
                })
                : t('account.spot')
        }
    },
    {
        label: t('common.status'),
        value: 'status',
        render: (val) => {
            if (!val) return '-'

            const { label, color } = ENTRUST_STATUS_DICT[val]

            return h(
                'span',
                {
                    class: `text-${color}`,
                },
                label,
            )
        }
    },
    { label: t('stock.entrust.create_time'), value: 'tradeTime' },
    { label: t('stock.entrust.deal_time'), value: 'dealTime', },
])

const details = computed(() => [
    {
        label: t('stock.entrust.direction'),
        value: 'direction',
        render: val => {
            if (!val) return '-'

            const { label, color } = tradeDirectionDict(val)

            return h(
                'span',
                {
                    class: `text-${color}`,
                },
                label,
            )
        },
    },
    {
        label: t('stock.entrust.type'),
        value: 'tradeType',
        render: val => {
            if (!val) return '-'

            const { label, color } = tradeTypeDict(val)

            return h(
                'span',
                {
                    class: `text-${color}`,
                },
                label,
            )
        },
    },
    { label: t('stock.entrust.price'), value: 'tradePrice' },
    { label: t('stock.transaction.price'), value: 'dealPrice' },
    { label: t('stock.entrust.total'), value: 'tradeNum' },
    { label: t('stock.entrust.deal_quantity'), value: 'dealNum' },
    // { label: '成本价', value: 'costPrice' },
    { label: t('contract.earnings'), value: 'winAmount' },
    { label: t('stock.entrust.deal_amount'), value: 'transactionAmount' },
    { label: t('stock.transaction.fee'), value: 'tradeFee' },
])

const { t } = useI18n()

defineOptions({ name: 'entrust-details' })
</script>

<template>
    <c-header :title="t('contract.apply_record')"/>

    <c-record
        class="with-header-container"
        :finished
        :onRefresh
        :onLoadMore
        v-model:refresh-loading="refreshLoading"
        v-model:load-loading="loadLoading"
    >
        <c-card
            divider
            class="not-last:mb-3"
            v-for="({ id, type, marketType, periodType, multiple, auditStatus, totalCash, totalPower, createTime }, i) in list"
            :key="id"
            :data-aos-delay="i * 50"
            :title="
                utils_contract_name({
                    marketType,
                    type,
                    periodType,
                    multiple,
                    id,
                })
            "
        >
            <template #extra>
                <div class="text-xs ml-auto" :class="contractStatusDict[auditStatus].color">
                    {{ contractStatusDict[auditStatus].title }}
                </div>
            </template>

            <c-description :label="t('contract.principal')">
                <c-amount :amount="totalCash"/>
            </c-description>

            <c-description :label="t('contract.total_assets')">
                <c-amount :amount="totalPower"/>
            </c-description>

            <c-description :label="t('contract.apply_time')">
                {{ createTime }}
            </c-description>
        </c-card>
    </c-record>
</template>

<script setup>
import { utils_contract_name } from '@/utils'

const { contractStatusDict } = useContractType()

const { list, finished, refreshLoading, loadLoading, onRefresh, onLoadMore } = usePagination({
    // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/app%E7%AB%AF-%E5%90%88%E7%BA%A6%E4%B8%9A%E5%8A%A1/pageUsingGET_1
    url: '/contract/account/page',
})

const { t } = useI18n({
    useScope: 'global',
    messages: {
        [LANGUAGE.zhCN]: {
            contract: {
                apply_record: '申请合约记录',
                apply_time: '申请时间',
            },
        },
        [LANGUAGE.zhHK]: {
            contract: {
                apply_record: '申请合约记录',
                apply_time: '申请时间',
            },
        },
        [LANGUAGE.enUS]: {
            contract: {
                apply_record: 'Apply for contract records',
                apply_time: 'Apply time',
            },
        },
    },
})

defineOptions({ name: 'account-apply-record' })
</script>

<style scoped>

</style>

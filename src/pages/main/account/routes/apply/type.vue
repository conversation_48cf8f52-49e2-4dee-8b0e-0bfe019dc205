<template>
    <c-header :title>
        <template #right>
            <van-icon
                name="todo-list-o"
                size="20"
                @click="$router.push('/contract/apply/record')"
            />
        </template>
    </c-header>

    <div class="with-header-container">
        <CellGroup>
            <Cell
                data-aos="fade-left"
                class="bg-[#3A5BCD] rounded-xl"
                v-for="({ label, value, key }, i) in options"
                :data-aos-delay="i * 50"
                :key
                :icon="{
                    prefix: 'account',
                    name: `contract_${key}`,
                    size: 36
                }"
                arrow
                :to="`/contract/apply/${contractType}/${value}`"
            >
                <template #title>
                    <span class="text-white">{{ label }}</span>
                </template>
            </Cell>
        </CellGroup>
    </div>
</template>

<script setup>
import { CellGroup, Cell } from '@/components/Cell'

const { contractType, contractTypeOptions } = useContractType()

const options = ref([])

useRequest({
    // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/app%E7%AB%AF-%E8%8E%B7%E5%8F%96%E5%90%88%E7%BA%A6%E9%85%8D%E7%BD%AE/getOpenContractTypeUsingGET_1
    url: '/contract/config/getOpenContractType',
    initialValues: [],
    onSuccess: res => {
        options.value = contractTypeOptions.filter(e => e.visibleIndex ? res.includes(e.visibleIndex) : true)
    },
})

const { t } = useI18n()

const title = t('contract.apply', { name: +contractType === 1 ? t('contract.title') : t('futures.title') })

defineOptions({ name: 'account-apply-type' })
</script>

<style scoped>

</style>

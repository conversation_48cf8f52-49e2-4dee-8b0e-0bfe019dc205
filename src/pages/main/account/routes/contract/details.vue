<template>
    <c-header :title="title || t('contract.details')"/>

    <div class="with-header-container">
        <c-card
            data-aos="fade-left"
            data-aos-delay="50"
        >
            <c-description-group :items="baseInfo"/>
        </c-card>

        <c-card
            class="mt-4"
            data-aos="fade-left"
            data-aos-delay="100"
        >
            <c-description-group :items="details">
                <template #template="{ value }">
                    <c-amount
                        :amount="value"
                    />
                </template>
            </c-description-group>
        </c-card>

        <slot/>
    </div>
</template>

<script setup>
import _ from 'lodash'

defineProps({
    title: String,
})

const { dispatch_refreshContact } = useAccountStore(),
    contractDetails = useCurrentContract()

dispatch_refreshContact()

const baseInfo = computed(() => {
    const { periodType, openTime, expireTime, multiple } = contractDetails.value
    return [
        { label: t('contract.market'), value: utils_contract_name(contractDetails.value) },
        { label: t('contract.period'), value: t(`contract.period_${periodType}`) },
        {
            label: t('form.date'),
            value: `${utils_time(openTime, TIME_FORMAT.YMD)} - ${utils_time(expireTime, TIME_FORMAT.YMD)}`,
        },
        { label: t('contract.multiple'), value: t('contract.multiples', [ multiple ]) },
    ]
})

const details = computed(() => {
    const {
        type,
        totalPower,
        initCash,
        totalFinance,
        giveAmount,
        warnRemindAmount,
        closeRemindAmount,
        interestAmount,
        positionAmount,
        expendAmount,
        coverLossAmount,
        accountWinAmount,
        withdrawAmount,
        freezePower,
        negativeAmount,
    } = contractDetails.value

    const base = [
        // 总操盘资金
        { label: t('contract.total_assets'), value: totalPower },
        // 起始保证金
        { label: t('contract.init_principal'), value: initCash },
        // 亏损警戒线
        { label: t('contract.warning'), value: warnRemindAmount },
        // 平仓线
        { label: t('contract.close'), value: closeRemindAmount },
        // 利息金额
        { label: t('contract.rate_amount'), value: interestAmount },
        // 市值
        { label: t('stock.market_value'), value: positionAmount },
        // 扩大合约
        { label: t('contract.expand'), value: expendAmount },
        // 追加合约
        { label: t('contract.replenish'), value: coverLossAmount },
        // 浮动盈亏
        { label: t('account.earnings'), value: accountWinAmount },
        // 提盈金额
        { label: t('financial.withdrawal.amount'), value: withdrawAmount },
        // 冻结金额
        { label: t('account.freeze'), value: freezePower },
        // 穿仓金额
        { label: t('account.amount_of_loss'), value: negativeAmount },
    ]

    // 彩金合约
    if (type === 3) {
        base.splice(2, 0, {
            label: t('contract.gift_amount'),
            value: giveAmount,
        })
    }

    // 体验合约
    if (type === 2) {
        const _details = _.at(base, [ 0, 2, 3, 5, 8, 10 ])

        _details.splice(1, 0, {
            label: t('contract.gift_amount'),
            value: totalFinance,
        })

        return _details
    }

    return base
})

const { t } = useI18n()

defineOptions({ name: 'contract-details' })
</script>

<style scoped>

</style>

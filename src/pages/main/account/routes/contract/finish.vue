<template>
    <Details :title="t('_title')">
        <div
            data-aos="fade-left"
            data-aos-delay="250"
            data-aos-anchor="#app"
            class="mt-4"
        >
            <van-button
                type="danger"
                block
                :loading
                @click="onConfirm"
            >
                {{ t('_submit') }}
            </van-button>
        </div>
    </Details>
</template>

<script setup>
import Details from './details.vue'

const { id: contractId } = useAccountId(),
    { dispatch_refreshContact } = useAccountStore()

const { go } = useRouter()

const [ onSubmit, loading ] = useFetchLoading(async () => {
    await api_post({
        // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/app%E7%AB%AF-%E5%90%88%E7%BA%A6%E4%B8%9A%E5%8A%A1/endContractUsingPOST_1
        url: '/contract/account/endContract',
        params: {
            contractId,
        },
    })
    dispatch_refreshContact()
    go(-2)
})

const onConfirm = async () => {
    await showConfirmDialog({
        title: t('_confirm'),
    })

    onSubmit()
}

const { t } = useI18n({
    useScope: 'global',
    messages: {
        [LANGUAGE.zhCN]: {
            _title: '终止合约',
            _confirm: '是否确认终止当前合约',
            _submit: '终止',
        },
        [LANGUAGE.zhHK]: {
            _title: '终止合约',
            _confirm: '是否确认终止当前合约',
            _submit: '终止',
        },
        [LANGUAGE.enUS]: {
            _title: 'Terminate Contract',
            _confirm: 'Do you confirm the termination of the current contract?',
            _submit: 'Terminate',
        },
    },
})

defineOptions({ name: 'contract-finish' })
</script>

<style scoped>

</style>

<template>
    <c-header :title="t('contract.history')"/>

    <c-record
        class="with-header-container"
        :finished
        :onRefresh
        :onLoadMore
        v-model:refresh-loading="refreshLoading"
        v-model:load-loading="loadLoading"
    >
        <c-card
            class="not-last:mb-4"
            v-for="item in list"
            :key="item.id"
        >
            <template #title>
                <div class="flex justify-between w-full">
                    <span class="flex-1 w-1 truncate text-title text-sm font-semibold">{{ utils_contract_name(item) }}</span>
                    <van-space>
                        <van-button type="primary" hairline size="small" @click="$router.push(`/contract/order/${item.id}`)">
                            交易明细
                        </van-button>
                        <!--<van-button type="primary" hairline round size="small" @click="$router.push(`/contract/details/${item.id}`)">-->
                        <!--    合约详情-->
                        <!--</van-button>-->
                    </van-space>
                </div>
            </template>

            <c-description :label="t('form.date')">
                {{ utils_time(item.openTime, TIME_FORMAT.YMD) }}
                -
                {{ utils_time(item.expireTime, TIME_FORMAT.YMD) }}
            </c-description>
            <c-description-group
                :items="details"
                :data-source="item"
                :value-format="utils_currency"
            />
        </c-card>
    </c-record>
</template>

<script setup>
import { utils_contract_name, utils_currency, utils_time } from '@/utils'
import { TIME_FORMAT } from '@/config'


const [
    {
        list,
        refreshLoading,
        loadLoading,
        finished,
        onRefresh,
        onLoadMore,
    },
] = useContractList({
    params: {
        settlementStatus: 2,
    },
})

const { t } = useI18n()

const details = [
    { label: t('contract.init_principal'), value: 'initCash' },
    { label: t('contract.total_assets'), value: 'totalPower' },
    { label: t('contract.expand'), value: 'expendAmount' },
    { label: t('contract.replenish'), value: 'coverLossAmount' },
    { label: t('contract.earnings'), value: 'winAmount' },
]

defineOptions({ name: 'contract-history' })
</script>

<style scoped>

</style>

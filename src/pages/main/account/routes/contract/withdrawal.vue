<template>
    <c-header :title="t('_title')"/>

    <div class="with-header-container">
        <van-form @submit="onSubmit">
            <c-card class="mb-2.5">
                <c-description-group
                    :items
                    :data-source="res"
                    :value-format="utils_currency"
                />
            </c-card>

            <van-field
                required
                :label="t('_label')"
                label-align="top"
                type="digit"
                name="applyAmount"
                clearable
                :min="res.minProfitWithdrawalAmount"
                :max="res.maxProfitWithdrawalAmount"
                :placeholder="t('_placeholder')"
                v-model="formState.applyAmount"
            />

            <c-submit
                class="mt-4"
                :disabled="!formState.applyAmount"
                :loading
            />
        </van-form>
    </div>
</template>

<script setup>
import { utils_currency } from '@/utils'

const { params: { id } } = useRoute()

const { res } = useRequest({
    // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/app%E7%AB%AF-%E5%90%88%E7%BA%A6%E4%B8%9A%E5%8A%A1/getWithdrawAmountUsingGET_1
    url: `/contract/account/getWithdrawAmount/${id}`,
    initialValues: {
        amount: 0,
        canUse: false,
        dailyProfitWithdrawalCount: 0,
        endTime: null,
        maxProfitWithdrawalAmount: 0,
        minProfitWithdrawalAmount: 0,
        startTime: null,
    },
})

const formState = ref({
    applyAmount: '',
})

const [ onSubmit, loading ] = useFetchLoading(async ({ applyAmount }) => {
    await api_post({
        // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/app%E7%AB%AF-%E5%90%88%E7%BA%A6%E4%B8%9A%E5%8A%A1/withdrawUsingPOST_1
        url: '/contract/account/withdraw',
        params: {
            id,
            applyAmount,
        },
    })

    showSuccessToast(t('operation.successfully'))
})

const { t } = useI18n({
    useScope: 'global',
    messages: {
        [LANGUAGE.zhCN]: {
            _title: '合约资金提取',
            _label: '提取金额',
            _available: '可提取金额',
            _placeholder: '请输入要提取的金额',
        },
        [LANGUAGE.zhHK]: {
            _title: '合约资金提取',
            _label: '提取金额',
            _available: '可提取金额',
            _placeholder: '请输入要提取的金额',
        },
        [LANGUAGE.enUS]: {
            _title: 'Contract fund withdrawal',
            _label: 'Withdraw amount',
            _available: 'Withdraw amount of available',
            _placeholder: 'Please enter the amount you want to withdraw',
        },
    },
})

const items = [
    { label: t('_available'), value: 'amount' },
    { label: t('financial.min'), value: 'minProfitWithdrawalAmount' },
    { label: t('financial.max'), value: 'maxProfitWithdrawalAmount' },
]

defineOptions({ name: 'contract-withdrawal' })
</script>

<style scoped>

</style>

<template>
    <AccountDetailsLayout
        :title="t('account.spot')"
        :type="ACCOUNT_TYPE.SPOT"
        :total="$spot.assetAmount"
        :earnings="$spot.todayWinAmount"
        :balance="$spot.usableCash"
        :interest="$spot.interestCash"
        :freeze="$spot.freezeCash"
        :grid
        :grid-props="{
            columnNum: 5
        }"
    >
        <AccountTabs
            class="account-tabs-container"
            :type="ACCOUNT_TYPE.SPOT"
        />
    </AccountDetailsLayout>
</template>

<script setup>
import { ACCOUNT_TYPE } from '@/config'
import AccountDetailsLayout from '@/pages/main/account/components/AccountDetailsLayout.vue'
import AccountTabs from '@/pages/main/account/components/AccountTabs.vue'

const accountStore = useAccountStore(),
    { dispatch_refreshSpot } = accountStore,
    { $spot } = storeToRefs(accountStore)

dispatch_refreshSpot()

const { t } = useI18n()

const grid = [
    // 充值
    {
        icon: 'grid_deposit',
        title: t('account.deposit'),
        to: '/deposit',
    },
    // 提现
    {
        icon: 'grid_withdrawal',
        title: t('account.withdrawal'),
        to: '/withdrawal',
    },
    // 交易中心
    {
        icon: 'grid_transaction',
        title: t('account.transaction'),
        to: '/quotes/index',
    },
    // 资金记录
    {
        icon: 'grid_finance',
        title: t('account.finance_record'),
        to: '/spot/financial/' + $spot.value.assetId,
    },
    // 历史订单
    {
        icon: 'grid_history',
        title: t('account.history'),
        to: '/spot/order/' + $spot.value.assetId,
    },
]

// 现货账户
defineOptions({ name: 'account' })
</script>

<style scoped>
.account-tabs-container {
    /*
        MarginTop 16
        总资产 100
        资产统计 64
        快捷导航 88
    */
    height: calc(100% - 16px - 100px - 64px - 88px);
}
</style>

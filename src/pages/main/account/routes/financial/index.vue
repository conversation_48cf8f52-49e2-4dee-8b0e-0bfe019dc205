<template>
    <c-header :title="t('account.finance_record')">
        <template #right>
            <van-icon
                name="filter-o"
                @click="filterPopup = true"
            />
        </template>
    </c-header>

    <c-record
        class="with-header-container"
        :finished
        :onRefresh
        :onLoadMore
        v-model:refresh-loading="refreshLoading"
        v-model:load-loading="loadLoading"
    >
        <c-card
            class="not-last:mb-2.5"
            v-for="({ fromType, createTime, beforeNum, updateNum, afterNum, currency }, i) in list"
            :data-aos-delay="50 * i"
            data-aos-anchor="#app"
            :key="i"
            :title="typeDict.get(fromType)"
            :extra="createTime"
        >
            <c-description :label="t('_before')">
                <c-amount
                    class="text-text"
                    :amount="beforeNum"
                    :currency
                />
            </c-description>
            <c-description :label="t('_change')">
                <c-amount
                    symbol
                    colorful
                    :currency
                    :amount="updateNum"
                />
            </c-description>
            <c-description :label="t('_after')">
                <c-amount :currency :amount="afterNum"/>
            </c-description>
        </c-card>
    </c-record>

    <Filter
        v-model="filterPopup"
        @reset="onReset"
        @confirm="onRefresh"
    >
        <c-select
            class="mb-4"
            :label="t('_type')"
            popover
            :columns="options"
            :columns-field-names="{
                text: 'label',
                value: 'value'
            }"
            v-model="type"
        />

        <c-picker
            :title="t('form.query_time')"
            :options="record_range_presets"
            v-model:index="dateIndex"
        />
    </Filter>
</template>

<script setup>
import { record_range_presets } from '@/config/index.js'
import Filter from '@/components/Filter/index.vue'

const { isSpot, idParams } = useAccountId()

const filterPopup = ref(false)

const { t } = useI18n({
    useScope: 'global',
    messages: {
        [LANGUAGE.zhCN]: {
            _before: '变更前金额',
            _change: '变更金额',
            _after: '变更后金额',
            _type: '资金类型',

            _转到合约账户: '转到合约账户',
            _合约账户转出: '合约账户转出',
            _创建合约账户: '创建合约账户',
            _结算合约账户: '结算合约账户',
            _交易买入: '交易买入',
            _交易卖出: '交易卖出',
            _人工存提: '人工存提',
            _分红派息: '分红派息',
            _利息扣除: '利息扣除',
            _交易手续费: '交易手续费',
            _取消订单解冻: '取消订单解冻',
            _委托单成交金额解冻: '委托单成交金额解冻',
            _委托单手续费解冻: '委托单手续费解冻',
            _委托单成交金额冻结: '委托单成交金额冻结',
            _委托单手续费冻结: '委托单手续费冻结',
            _活动奖励: '活动奖励',
            _签到奖励: '签到奖励',
            _合约解冻: '合约解冻',
            _利息返佣: '利息返佣',
            _交易返佣: '交易返佣',
            _充值赠送: '充值赠送',
            _委托撤销: '委托撤销',
            _任务奖励: '任务奖励',
            _委托单金额解冻: '委托单金额解冻',
            _委托单买入金额冻结: '委托单买入金额冻结',
            _委托单买入手续费冻结: '委托单买入手续费冻结',

            _追加金额: '追加金额',
            _扩大保证金: '扩大保证金',
            _转出到现货账户: '转出到现货账户',
            _扩大合约: '扩大合约',
        },
        [LANGUAGE.zhHK]: {
            _before: '变更前金额',
            _change: '变更金额',
            _after: '变更后金额',
            _type: '资金类型',

            _转到合约账户: '转到合约账户',
            _合约账户转出: '合约账户转出',
            _创建合约账户: '创建合约账户',
            _结算合约账户: '结算合约账户',
            _交易买入: '交易买入',
            _交易卖出: '交易卖出',
            _人工存提: '人工存提',
            _分红派息: '分红派息',
            _利息扣除: '利息扣除',
            _交易手续费: '交易手续费',
            _取消订单解冻: '取消订单解冻',
            _委托单成交金额解冻: '委託單成交金額解凍',
            _委托单手续费解冻: '委託單手續費解凍',
            _委托单成交金额冻结: '委託單成交金額凍結',
            _委托单手续费冻结: '委託單手續費凍結',
            _活动奖励: '活动奖励',
            _签到奖励: '签到奖励',
            _合约解冻: '合约解冻',
            _利息返佣: '利息返佣',
            _交易返佣: '交易返佣',
            _充值赠送: '充值赠送',
            _委托撤销: '委托撤销',
            _任务奖励: '任务奖励',
            _委托单金额解冻: '委託單金額解凍',
            _委托单买入金额冻结: '委託單買入金額凍結',
            _委托单买入手续费冻结: '委託單買入手續費凍結',

            _追加金额: '追加金额',
            _扩大保证金: '扩大保证金',
            _转出到现货账户: '转出到现货账户',
            _扩大合约: '扩大合约',
        },
        [LANGUAGE.enUS]: {
            _before: 'Previous Amount',
            _change: 'Change Amount',
            _after: 'Post-Change Amount',
            _type: 'Fund Type',

            _转到合约账户: 'Transfer to Contract Account',
            _合约账户转出: 'Transfer Out from Contract Account',
            _创建合约账户: 'Create Contract Account',
            _结算合约账户: 'Settle Contract Account',
            _交易买入: 'Trade Buy',
            _交易卖出: 'Trade Sell',
            _人工存提: 'Manual Deposit and Withdrawal',
            _分红派息: 'Dividend Distribution',
            _利息扣除: 'Interest Deduction',
            _交易手续费: 'Transaction Fee',
            _取消订单解冻: 'Cancel Order and Unfreeze',
            _委托单成交金额解冻: 'Unfreeze Order Transaction Amount',
            _委托单手续费解冻: 'Unfreeze Order Fee',
            _委托单成交金额冻结: 'Freeze Order Transaction Amount',
            _委托单手续费冻结: 'Freeze Order Fee',
            _活动奖励: 'Increase In Interest',
            _签到奖励: 'Sign Bonus',
            _合约解冻: 'Contract unfrozen',
            _利息返佣: 'Interest rebate',
            _交易返佣: 'Trading rebate',
            _充值赠送: 'Deposit Gift',
            _委托撤销: 'Entrust Revoke',
            _任务奖励: 'Mission award',
            _委托单金额解冻: 'Order Amount Unfreeze',
            _委托单买入金额冻结: 'Order Buy Amount Freeze',
            _委托单买入手续费冻结: 'Order Buy Fee Freeze',
            
            _追加金额: 'Additional Amount',
            _扩大保证金: 'Increase Margin',
            _转出到现货账户: 'Transfer to Spot Account',
            _扩大合约: 'Expand Contract',
        },
    },
})

// 现货资金类型字典表
const SPOT_FINANCIAL_OPTIONS = [
    { label: t('dict.全部'), value: '' },
    { label: t('account.deposit'), value: 1 },
    { label: t('account.withdrawal'), value: 2 },
    { label: t('_转到合约账户'), value: 3 },
    { label: t('_合约账户转出'), value: 4 },
    { label: t('_创建合约账户'), value: 5 },
    { label: t('_结算合约账户'), value: 6 },
    { label: t('_交易买入'), value: 7 },
    { label: t('_交易卖出'), value: 8 },
    { label: t('_人工存提'), value: 9 },
    { label: t('_分红派息'), value: 10 },
    { label: t('_利息扣除'), value: 11 },
    { label: t('_交易手续费'), value: 12 },
    { label: t('_活动奖励'), value: 13 },
    { label: t('_签到奖励'), value: 14 },
    { label: t('_合约解冻'), value: 15 },
    { label: t('_利息返佣'), value: 16 },
    { label: t('_交易返佣'), value: 17 },
    { label: t('_充值赠送'), value: 18 },
    { label: t('_委托撤销'), value: 19 },
    { label: t('_任务奖励'), value: 20 },

    { label: t('_委托单金额解冻'), value: 21 },
    { label: t('_委托单手续费解冻'), value: 22 },
    { label: t('_委托单买入金额冻结'), value: 23 },
    { label: t('_委托单买入手续费冻结'), value: 24 },
]

// 合约资金类型字典表
const CONTRACT_FINANCIAL_OPTIONS = [
    { label: t('dict.全部'), value: '' },
    { label: t('_追加金额'), value: 1 },
    { label: t('_扩大保证金'), value: 2 },
    { label: t('_转出到现货账户'), value: 3 },
    { label: t('_交易买入'), value: 4 },
    { label: t('_交易卖出'), value: 5 },
    { label: t('_利息扣除'), value: 6 },
    { label: t('_分红派息'), value: 7 },
    { label: t('_人工存提'), value: 8 },
    { label: t('_交易手续费'), value: 9 },
    { label: t('_取消订单解冻'), value: 10 },

    { label: t('_委托单成交金额解冻'), value: 12 },
    { label: t('_委托单手续费解冻'), value: 13 },
    { label: t('_委托单成交金额冻结'), value: 14 },
    { label: t('_委托单手续费冻结'), value: 15 },
    // { label: t('_扩大合约'), value: 11 },
]

const useConfig = () => {
    if (isSpot) {
        // 现货
        return {
            // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/app%E7%AB%AF-%E7%8E%B0%E8%B4%A7%E8%B4%A6%E6%88%B7/pageUsingGET_3
            url: '/account/asset/record/page',
            options: SPOT_FINANCIAL_OPTIONS,
        }
    } else {
        // 合约
        return {
            // https://agent.gpnow.xyz/doc/doc.html#/%E5%95%86%E6%88%B7%E5%B9%B3%E5%8F%B0/%E5%90%88%E7%BA%A6%E8%B4%A6%E6%88%B7%E8%B5%84%E9%87%91%E8%AE%B0%E5%BD%95/pageUsingGET_7
            url: '/contract/account/record/page',
            options: CONTRACT_FINANCIAL_OPTIONS,
        }
    }
}

const { url, options } = useConfig()

const typeDict = utils_options_to_dict(options)

const type = ref(options[0].value),
    dateIndex = ref(-1)

const onReset = () => {
    type.value = options[0].value
    dateIndex.value = -1
}

const params = computed(() => {
    let startTime, endTime

    if (dateIndex.value !== -1) {
        ([ startTime, endTime ] = record_range_presets.value[dateIndex.value].value.map(e => e.format(TIME_FORMAT.FULL)))
    }

    return {
        ...idParams,
        fromType: type.value,
        startTime,
        endTime,
    }
})

const { list, refreshLoading, loadLoading, finished, onRefresh, onLoadMore } = usePagination({
    url,
    params,
})

defineOptions({ name: 'account-financial' })
</script>

<style scoped>
</style>

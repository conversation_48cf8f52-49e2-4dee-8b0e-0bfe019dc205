<template>
    <div class="px-4">
        <van-tabs
            class="h-full"
            shrink
            data-aos="fade-left"
            data-aos-delay="50"
            v-model:active="activeTab"
        >
            <!-- 当前持仓 -->
            <van-tab
                class="h-full pt-2.5 pb-4"
                :title="`${t('stock.position.position')}(${positionPagination.total})`"
                :name="TAB_TYPES.POSITION"
            >
                <c-record
                    :loading="refreshLoading"
                    :finished
                    :onRefresh
                    :onLoadMore
                    v-model:refresh-loading="refreshLoading"
                    v-model:load-loading="loadLoading"
                >
                    <Position
                        v-for="item in list"
                        :key="item.symbol"
                        :data="item"
                        :contractId="isSpot ? undefined : id"
                    />
                </c-record>
            </van-tab>
            <!-- 当前持仓 -->

            <!-- 成交明细 -->
            <van-tab
                class="h-full pt-2.5 pb-4"
                :title="`${t('stock.entrust.deal_details')}(${dealPagination.total})`"
                :name="TAB_TYPES.DEAL"
            >
                <DealTable class="h-full" @row-click="onRowClick" />
            </van-tab>
            <!-- 成交明细 -->

            <!-- 委托明细 -->
            <van-tab
                class="h-full pt-2.5 pb-4"
                :title="`${t('stock.entrust.entrust_details')}(${entrustPagination.total})`"
                :name="TAB_TYPES.ENTRUST"
            >
                <EntrustTable class="h-full" @row-click="onRowClick" />
            </van-tab>
            <!-- 委托明细 -->
        </van-tabs>

        <StockPopup
            :type="activeTab"
            :dataSource="stockPopupDetails"
            @refresh="pollingFetch"
            v-model="stockPopup"
        />
    </div>
</template>

<script setup>
import _ from 'lodash'

import Position from './Position.vue'
import StockPopup from '@/pages/main/stock/routes/transaction/components/StockPopup.vue'

const { type } = defineProps({
    // 账户类型：spot 现货、contract 合约
    type: {
        type: String,
        required: true,
    },
})

const { id, idParams, isSpot } = useAccountId(type)

const TAB_TYPES = {
    POSITION: 'position',
    DEAL: 'deal',
    ENTRUST: 'entrust',
}

// 当前选中类型
const activeTab = useSessionStorage(`${type}Tab`, TAB_TYPES.POSITION)

const stockPopup = ref(false),
    stockPopupDetails = ref({})

const onRowClick = (details) => {
    stockPopupDetails.value = details
    stockPopup.value = true
}

// 持仓数据
const {
    list,
    initial,
    refreshLoading,
    loadLoading,
    pagination: positionPagination,
    finished,
    onRefresh,
    onLoadMore,
} = usePositionFetch({
    params: idParams,
})

// 成交明细表格
const {
        Table: DealTable,
        list: dealList,
        initial: dealInitial,
        pagination: dealPagination,
    } = useDealTable({
        params: { ...idParams, status: 2 }
    }),
    // 委托明细表格
    {
        Table: EntrustTable,
        list: entrustList,
        initial: entrustInitial,
        pagination: entrustPagination,
    } = useEntrustTable({
        params: { ...idParams, status: 0 }
    })

const { t } = useI18n()

let intervalInstance

const onClearIntervalInstance = () => {
    clearInterval(intervalInstance)
    intervalInstance = null
}

onBeforeUnmount(onClearIntervalInstance)

// const marketStatusRun = (data) => {
//     const marketTypes = _.map(data, e => stockCountryDict.get(e.market))
//     return useMarketStatus(marketTypes)
// }

const pollingFetch = async () => {
    switch (activeTab.value) {
        case TAB_TYPES.POSITION:
            if (initial.value && list.value.length) {
                const res = await api_get({
                    url: API_PATH.POSITION,
                    params: {
                        ...idParams,
                        pageNumber: 1,
                        pageSize: positionPagination.value.current * positionPagination.value.pageSize,
                    },
                })
                list.value = res.records
            } else {
                onClearIntervalInstance()
            }
            break
        case TAB_TYPES.DEAL:
            if (dealInitial.value && dealList.value.length) {
                const res = await api_get({
                    url: API_PATH.ENTRUST,
                    params: {
                        ...idParams,
                        status: 2,
                        pageNumber: 1,
                        pageSize: dealPagination.value.current * dealPagination.value.pageSize,
                    },
                })
                dealList.value = res.records
            } else {
                onClearIntervalInstance()
            }
            break
        case TAB_TYPES.ENTRUST:
            if (entrustInitial.value && entrustList.value.length) {
                const res = await api_get({
                    url: API_PATH.ENTRUST,
                    params: {
                        ...idParams,
                        status: 0,
                        pageNumber: 1,
                        pageSize: entrustPagination.value.current * entrustPagination.value.pageSize,
                    },
                })
                entrustList.value = res.records
            } else {
                onClearIntervalInstance()
            }
            break
    }
}

onMounted(async () => {
    await nextTick()

    intervalInstance = setInterval(pollingFetch, 5000)
})

// 当前持仓、成交明细、委托明细 tabs 组件
defineOptions({ name: 'AccountTabs' })
</script>

<style scoped>

</style>

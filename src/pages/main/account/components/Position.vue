<template>
    <div class="position bg-bg p-2.5" @click="_onClick">
        <div class="flex-middle gap-2 mb-2.5">
            <!-- 交易类型 -->
            <div class="marketBlock" :style="{ backgroundColor: `var(--${tradeTypeConfig?.color})` }">
                {{ t(`dict.${tradeTypeConfig?.label}`) }}
            </div>
            <!-- 交易类型 -->

            <div class="marketBlock bg-raise!">
                {{ t('dict.开') }}
            </div>

            <!-- 股票市场 -->
            <div class="marketBlock">
                {{ stockMarketDict.get(data.market) }}
            </div>
            <!-- 股票市场 -->

            <!-- 股票名称 -->
            <div class="text-title text-sm font-semibold">
                {{ data.symbolName }}
            </div>
            <!-- 股票名称 -->

            <!-- 币种 -->
            <div class="text-xs">
                ({{ STOCK_CONFIG[stockCountryDict.get(data.market)]?.currency }})
            </div>
            <!-- 币种 -->

            <div class="text-xs">
                ({{ data.id }})
            </div>
        </div>

        <!-- 持仓详情 -->
        <div class="grid grid-cols-3">
            <div
                v-for="({ title, key, symbol, colorful, precision }) in renderConfig"
                :key
            >
                <div class="text-text text-xs">{{ title }}</div>
                <c-amount
                    class="text-primary"
                    :amount="data[key]"
                    :symbol
                    :colorful
                    :precision
                />
            </div>
        </div>
        <!-- 持仓详情 -->
    </div>
</template>

<script setup>
import { STOCK_CONFIG, stockMarketDict, stockCountryDict } from '@/config'

const { data, contractId, onClick } = defineProps({
    // 持仓数据
    data: {
        type: Object,
        required: true,
    },
    // 合约ID
    contractId: [ String, Number ],
    onClick: Function,
})

const { dispatch_checkStock } = useStockStore()

const _onClick = () => {
    // 默认跳转股票交易页
    onClick ? onClick(data) : dispatch_checkStock(data, { route: 'transaction', params: { contractId } })
}

const tradeTypeConfig = computed(() => tradeTypeDict(data.tradeType))

const { t } = useI18n()

// 持仓详情渲染配置
const renderConfig = [
    { title: t('stock.position.available'), key: 'restNum' },
    { title: t('stock.market_price'), key: 'stockPrice', precision: 3 },
    {
        title: t('account.earnings'),
        key: 'floatingProfitLoss',
        symbol: true,
        colorful: true,
    },
    { title: t('stock.position.total'), key: 'buyTotalNum' },
    { title: t('stock.position.average'), key: 'buyAvgPrice', precision: 3 },
    { title: t('stock.market_value'), key: 'marketValue' },
]

// 当前持仓组件
defineOptions({ name: 'Position' })
</script>

<style scoped>
.position:not(:last-of-type) {
    border-bottom: 1px solid var(--border);
}

.grid > div:nth-child(3n) {
    text-align: right;
}

.grid > div:nth-child(3n - 1) {
    text-align: center;
}
</style>

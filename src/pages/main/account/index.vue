<template>
    <div class="with-header-container__noPadding pt-4">
        <main
            class="custom-shadow rounded-bl-lg rounded-br-lg"
            data-aos="fade-left"
            data-aos-delay="50ms"
        >
            <!-- 总资产 -->
            <div class="card text-white mx-4 rounded-lg overflow-hidden custom-shadow h-16 px-4 flex-between">
                <div>{{ t('account.total_assets') }}</div>

                <c-rate-currency
                    class="text-2xl"
                    :amount="$spot.accountAmount"
                    v-model:currency="assetsCurrency"
                    v-model:rate="assetsRate"
                />
            </div>
            <!-- 总资产 -->

            <!-- 快捷导航 -->
            <van-grid
                :column-num="grid.length"
                :border="false"
                data-aos="zoom-in"
            >
                <van-grid-item
                    v-for="({ title, icon, to, handler }, i) in grid"
                    :key="i"
                    :text="title"
                    :to
                    @click="handler"
                >
                    <template #icon>
                        <c-icon
                            prefix="account"
                            size="34"
                            :name="icon"
                        />
                    </template>
                </van-grid-item>
            </van-grid>
            <!-- 快捷导航 -->
        </main>

        <div class="account-tabs-container p-4">
            <van-tabs
                class="h-full"
                data-aos="fade-left"
                data-aos-delay="150"
                shrink
                v-model:active="accountActiveTab"
            >
                <!-- 合约账户 -->
                <van-tab
                    class="pt-2.5"
                    :title="t('account.contract')"
                    :name="ACCOUNT_TYPE.CONTRACT"
                >
                    <div class="h-full overflow-y-auto overflow-x-hidden">
                        <c-record
                            :finish="$contractFinish"
                            :onRefresh="dispatch_refreshContact"
                            :onLoadMore="dispatch_loadMoreContact"
                            v-model:refresh-loading="$contractRefreshLoading"
                            v-model:load-loading="$contractLoadLoading"
                        >
                            <c-card
                                class="not-last:mb-2.5"
                                v-for="({ id, marketType, type, periodType, multiple, useAmount, freezePower, todayWinAmount, todayWinRate }, i) in $contract"
                                data-aos="fade-left"
                                data-aos-anchor="#app"
                                :data-aos-delay="i * 50"
                                :key="id"
                                arrow
                                :to="`/contract/${id}`"
                            >
                                <template #title>
                                    <div class="flex-middle gap-2 text-sm text-title flex-1 w-1">
                                        <div class="marketBlock">{{ marketType }}</div>
                                        <div class="marketBlock bg-text!">{{ STOCK_CONFIG[marketType].currency }}</div>
                                        <div class="truncate">
                                            {{ utils_contract_name({ marketType, type, periodType, multiple, id }) }}
                                        </div>
                                    </div>
                                </template>

                                <c-description-group :columns="2">
                                    <c-description :label="t('account.balance')">
                                        <c-amount :amount="useAmount"/>
                                    </c-description>
                                    <c-description :label="t('account.freeze')">
                                        <c-amount :amount="freezePower"/>
                                    </c-description>
                                    <c-description :label="t('account.today_earnings')">
                                        <c-amount :amount="todayWinAmount" colorful/>
                                    </c-description>
                                    <c-description>
                                        <c-amount
                                            percent
                                            colorful
                                            :amount="todayWinRate"
                                        />
                                    </c-description>
                                </c-description-group>
                            </c-card>
                        </c-record>
                    </div>
                </van-tab>
                <!-- 合约账户 -->

                <!-- 现货账户 -->
                <van-tab
                    class="pt-2.5"
                    :title="t('account.spot')"
                    :name="ACCOUNT_TYPE.SPOT"
                >
                    <van-pull-refresh
                        class="h-full"
                        :model-value="$spotLoading"
                        @refresh="dispatch_refreshSpot"
                    >
                        <c-card
                            class="mb-4"
                            :to="`/spot/${$spot.assetId}`"
                            arrow
                        >
                            <template #title>
                                <div class="h-6 flex-middle gap-2.5">
                                    <div class="text-title font-semibold">
                                        {{ t('account.total_assets') }}
                                    </div>

                                    <c-rate-currency
                                        class="text-primary"
                                        :amount="$spot.assetAmount"
                                        v-model:currency="spotCurrency"
                                        v-model:rate="spotRate"
                                    />
                                </div>
                            </template>

                            <c-description-group :columns="2">
                                <c-description :label="t('account.balance')">
                                    <c-amount :amount="$spot.usableCash * spotRate"/>
                                </c-description>

                                <c-description :label="t('account.freeze')">
                                    <c-amount :amount="$spot.freezeCash * spotRate"/>
                                </c-description>
                                <c-description :label="t('account.today_earnings')">
                                    <c-amount :amount="$spot.todayWinAmount * spotRate" colorful/>
                                </c-description>
                                <c-description>
                                    <c-amount
                                        percent
                                        colorful
                                        :amount="$spot.todayWinRate"
                                    />
                                </c-description>
                            </c-description-group>
                        </c-card>
                    </van-pull-refresh>
                </van-tab>
                <!-- 现货账户 -->
            </van-tabs>
        </div>
    </div>
</template>

<script setup>
import { ACCOUNT_TYPE, STOCK_CONFIG } from '@/config/index.js'
import { utils_contract_name } from '@/utils'
import { accountActiveTab } from '@/store'

const { t } = useI18n()
const accountStore = useAccountStore(),
    {
        dispatch_refreshSpot,
        dispatch_refreshContact,
        dispatch_loadMoreContact,
        dispatch_clearSpotIntervalFetch,
        dispatch_clearContractIntervalFetch,
    } = accountStore,
    {
        $spot,
        $spotLoading,
        $contract,
        $contractRefreshLoading,
        $contractLoadLoading,
        $contractFinish,
    } = storeToRefs(accountStore)

dispatch_refreshSpot()
dispatch_refreshContact()

onBeforeUnmount(() => {
    dispatch_clearSpotIntervalFetch()
    dispatch_clearContractIntervalFetch()
})

// 总资产汇率
const [ assetsCurrency, assetsRate ] = useRate(),
    [ spotCurrency, spotRate ] = useRate()

// 现货会计导航
const spotGrid = computed(() => [
    {
        icon: 'grid_deposit',
        title: t('account.deposit'),
        to: '/deposit',
    },
    {
        icon: 'grid_withdrawal',
        title: t('account.withdrawal'),
        to: '/withdrawal',
    },
])

// 合约会计导航
const contractGrid = computed(() => [
    {
        icon: 'grid_apply',
        title: t('contract.title'),
        to: '/contract/apply/type/1',
    },
    {
        icon: 'grid_apply_futures',
        title: t('futures.title'),
        // to: '/contract/apply/type/2',
        handler: () => {
            showFailToast(t('common.developing'))
        },
    },
    {
        icon: 'grid_apply_record',
        title: t('contract.apply_record'),
        to: '/contract/apply/record',
    },
    {
        icon: 'grid_contract_history',
        title: t('contract.history'),
        to: '/contract/history',
    },
])

const grid = computed(() => accountActiveTab.value === ACCOUNT_TYPE.SPOT ? spotGrid.value : contractGrid.value)

defineOptions({ name: 'account' })
</script>

<style scoped>
.card {
    background: linear-gradient(90deg, #4366DE 0%, #3150BD 100%);
}

.card-footer {
    background: rgba(255, 255, 255, .1);
}

.van-theme-dark main {
    background: rgba(255, 255, 255, .04);
}

.van-grid {
    --van-grid-item-content-padding: 16px 0;
    --van-grid-item-text-color: var(--title)
}

:deep(.van-grid-item__text) {
    margin-top: 4px;
}

.account-tabs-container {
    /*
        总资产 64
        快捷导航 88
    */
    height: calc(100% - 64px - 88px);
}
</style>

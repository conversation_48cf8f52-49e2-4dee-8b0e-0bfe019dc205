<template>
    <c-header :title="t('_title')"/>

    <c-record
        class="with-header-container"
        :finished
        :onRefresh
        :onLoadMore
        v-model:refresh-loading="refreshLoading"
        v-model:load-loading="loadLoading"
    >
        <c-card
            class="not-last:mb-4"
            v-for="item in list"
            :key="item.id"
        >
            <template #title>
                <div class="flex-middle gap-2 text-sm">
                    <div class="marketBlock">
                        {{ stockMarketDict.get(item.market) }}
                    </div>

                    <div>
                        {{ item.symbolName }}
                    </div>

                    <div>
                        {{ item.symbol }}
                    </div>
                </div>
            </template>

            <template #extra>
                <c-amount
                    colorful
                    :amount="item.winAmount"
                    :currency="item.currency"
                />
            </template>

            <c-description-group
                layout="vertical"
                :columns="3"
                :items
                :data-source="item"
                value-class="text-xs"
            />
        </c-card>
    </c-record>
</template>

<script setup>
import { stockMarketDict } from '@/config/index.js'

const {
    list,
    refreshLoading,
    loadLoading,
    finished,
    onRefresh,
    onLoadMore,
} = usePagination({
    // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/app%E7%AB%AF-%E6%8C%81%E4%BB%93%E7%9B%B8%E5%85%B3/indexPageUsingGET_1
    url: '/position/indexPage',
})

const { t } = useI18n({
    useScope: 'global',
    messages: {
        [LANGUAGE.zhCN]: {
            _title: '开奖记录',
            _buy: '买入均价',
            _buy_time: '买入时间',
            _lever: '交易杠杆',
            _sell: '卖出均价',
            _sell_time: '卖出时间',
        },
        [LANGUAGE.zhHK]: {
            _title: '开奖记录',
            _buy: '买入均价',
            _buy_time: '买入时间',
            _lever: '交易杠杆',
            _sell: '卖出均价',
            _sell_time: '卖出时间',
        },
        [LANGUAGE.enUS]: {
            _title: 'Open Records',
            _buy: 'Purchase Avg. Price',
            _buy_time: 'Purchase Time',
            _lever: 'Trading Leverage',
            _sell: 'Selling Avg. Price',
            _sell_time: 'Selling Time',
        },
    },
})

const items = [
    {
        label: t('common.type'),
        value: 'tradeType',
        render: val => {
            const { label, color } = tradeTypeDict(val)
            return h(
                'span',
                {
                    class: `text-${color}`,
                },
                t(`dict.${label}`),
            )
        },
    },
    {
        label: t('_buy'),
        value: 'buyAvgPrice',
    },
    {
        label: t('_buy_time'),
        value: 'createTime',
    },
    {
        label: t('_lever'),
        value: 'tradeUnit',
        render: val => t('contract.multiples', [ val ]),
    },
    {
        label: t('_sell'),
        value: 'sellAvgPrice',
    },
    {
        label: t('_sell_time'),
        value: 'updateTime',
    },
]

defineOptions({ name: 'index-results' })
</script>

<style scoped>
:deep(.c-description-group .c-description:nth-child(3n)) {
    text-align: right;
    flex: 1;
}

:deep(.c-description-group .c-description:nth-child(3n - 1)) {
    text-align: center;
}
</style>

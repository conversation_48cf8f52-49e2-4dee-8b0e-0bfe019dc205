<template>
    <c-select
        class="mt-4"
        popover
        :disabled
        :columns
        :model-value
    >
        <template #popoverActions="{ onClose }">
            <PositionInfo
                class="px-3"
                v-for="({ id, buyAvgPrice, buyTotalNum, restNum, tradeType }) in columns"
                :class="{ 'text-primary': +id === +modelValue }"
                :id
                :average="buyAvgPrice"
                :total="buyTotalNum"
                :available="restNum"
                :tradeType
                @click="onSelect( { id, average: buyAvgPrice, total: buyTotalNum, available: restNum, tradeType }); onClose()"
            />
        </template>

        <template #reference="{ popup, onToggle }">
            <div class="w-ful flex-middle gap-2" @click="onToggle">
                <template v-if="modelValue">
                    <PositionInfo
                        class="flex-1 w-1"
                        :id="selected.id"
                        :average="selected.buyAvgPrice"
                        :total="selected.buyTotalNum"
                        :available="selected.restNum"
                        :tradeType="selected.tradeType"
                    />
                </template>
                <template v-else>
                    <span class="flex-1 w-1" :class="{ 'text-text': disabled }">
                        {{ $t('form.select_placeholder', [ $t('stock.position.title') ]) }}
                    </span>
                </template>

                <van-icon
                    v-if="!disabled"
                    :size="20"
                    :name="popup ? 'arrow-up' : 'arrow-down'"
                />
            </div>
        </template>
    </c-select>
</template>

<script setup>
import _ from 'lodash'

const { columns } = defineProps({
    columns: {
        type: Array,
        required: true,
    },
})

const modelValue = defineModel({
    required: true,
})

const emits = defineEmits([ 'select' ])

const disabled = computed(() => !columns.length)

const selected = computed(() => _.find(columns, { id: modelValue.value }) ?? {
    id: 0,
    buyAvgPrice: 0,
    buyTotalNum: 0,
    restNum: 0,
    tradeType: 0,
})

const onSelect = (item) => {
    modelValue.value = item.id
    emits('select', item)
}

const PositionInfo = ({ id, average, total, available, tradeType }) => {
    const { label, color } = tradeTypeDict(tradeType) ?? { label: '', color: '' }

    const space = ' '

    const child = [
        id,
        t('stock.position.average') + space + average,
        t('stock.position.total') + space + total,
        t('_available') + space + available,
    ].join(' | ')

    return h(
        'div',
        {
            class: 'h-10 flex-middle gap-2 text-xs',
        },
        [
            h(
                'div',
                {
                    class: 'marketBlock',
                    style: {
                        backgroundColor: `var(--${color})`,
                    },
                },
                t(`dict.${label}`),
            ),
            h(
                'div',
                child,
            ),
        ],
    )
}

const { t } = useI18n({
    useScope: 'global',
    messages: {
        [LANGUAGE.zhCN]: {
            _available: '可平',
        },
        [LANGUAGE.zhHK]: {
            _available: '可平',
        },
        [LANGUAGE.enUS]: {
            _available: 'available',
        },
    },
})

defineOptions({ name: 'IndexPositionSelector' })
</script>

<style scoped>

</style>

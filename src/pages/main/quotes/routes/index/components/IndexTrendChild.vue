<template>
    <c-card
        data-aos=""
        no-padding
        class="text-center p-1 h-[148px] border-2"
        :class="[
                    utils_amount_color(chg),
                    ($instrument === instrument && optional) ? 'border-primary' : 'border-transparent'
                ]"
    >
        <!-- 名称 -->
        <div class="text-sm w-full truncate">
            {{ name }}
        </div>
        <!-- 名称 -->

        <!-- 走势图 -->
        <div class="w-full h-20">
            <c-echarts
                v-if="timeline.length"
                :style="{height: '80px'}"
                :option="getOption(timeline, chg)"
            />
        </div>
        <!-- 走势图 -->

        <!-- 现价 -->
        <c-amount
            :colorful
            :amount="latestPrice"
        />
        <!-- 现价 -->

        <div class="text-xs flex-middle justify-center gap-2">
            <!-- 涨跌额 -->
            <c-amount
                symbol
                :colorful
                :amount="chg"
            />
            <!-- 涨跌额 -->

            <!-- 涨跌幅 -->
            <c-amount
                percent
                :colorful
                :amount="gain * 100"
            />
            <!-- 涨跌幅 -->
        </div>
    </c-card>
</template>

<script setup>
import * as echarts from 'echarts'
import _ from 'lodash'
import { utils_amount_color } from '@/utils/index.js'
import socket from '@/socket.js'

const isMounted = ref(false)

const { instrument, isVisible, name, isShow } = defineProps({
    optional: Boolean,
    timeline: Array,
    name: String,
    latestPrice: Number,
    chg: Number,
    gain: Number,
    instrument: String,
    isVisible: Boolean, // 判断是否在视口中
    isShow: Boolean, // 判断是否在视口中
})

const emits = defineEmits([ 'select' ])

const colorful = false
const stockStore = useStockStore()
const { $instrument } = storeToRefs(stockStore)
const indexStore = useIndexStore()
const { $indexData } = storeToRefs(indexStore)

// 走势图表配置
const getOption = (timeline, chg) => {
    const [ text, raise, fall ] = useCssVariable([ 'text', 'raise', 'fall' ])

    const gradientColor = (chg) => {
        let color = text
        if (chg > 0) color = raise
        if (chg < 0) color = fall

        return new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: `${color}80` },
            { offset: 1, color: `${color}00` },
        ])
    }

    return {
        grid: {
            left: 2,
            right: 2,
        },
        xAxis: {
            type: 'category',
            show: false,
            data: MINUTES_RANGE,
        },
        yAxis: {
            type: 'value',
            show: false,
            max: _.maxBy(timeline, 'price'),
            min: _.minBy(timeline, 'price'),
        },
        series: [
            {
                type: 'line',
                symbol: 'none',
                smooth: true,
                lineStyle: {
                    color: utils_amount_color(chg, { raise, fall, flat: text }),
                    width: 1,
                },
                areaStyle: {
                    color: gradientColor(chg),
                },
                data: _.map(timeline, 'price'),
            },
        ],
    }
}

const handler = (res) => {
    const instrument2 = `${res.data?.detail.market}|${res.data?.detail.securityType}|${res.data?.detail.symbol}`

    if (res?.data?.detail && instrument === instrument2) {
        delete res.data?.detail.name
        Object.assign($indexData.value[instrument].details, res.data?.detail)
    }

    if (res?.data?.list && res?.data?.list.length && instrument === instrument2) {
        $indexData.value[instrument].timeline.push(res?.data?.list)
    }
}

watch(() => isVisible, (bool) => {
    if (bool) {
        $indexData.value[instrument].isShow = true
        socket.on2('market', handler, {
            'type': 'market',
            'action': 'timeLine',
            'params': {
                'operate': 'subscribe',
                'instrument': instrument,
                'period': 'day',
            },
        })
    } else {
        socket.on2('market', handler, {
            'type': 'market',
            'action': 'timeLine',
            'params': {
                'operate': 'unsubscribe',
                'instrument': instrument,
                'period': 'day',
            },
        })
        $indexData.value[instrument].isShow = false
    }
}, { immediate: false, flush: 'post' })

onBeforeUnmount(() => {
    const keyList = Object.keys($indexData.value)
    keyList?.forEach((key, index) => {
        if ($indexData.value[instrument].isShow && key === instrument) {
            socket.on2('market', handler, {
                'type': 'market',
                'action': 'timeLine',
                'params': {
                    'operate': 'unsubscribe',
                    'instrument': key,
                    'period': 'day',
                },
            })
        }

        $indexData.value[key].isShow = index <= 2
    })
})

defineOptions({ name: 'IndexTrendChild' })
</script>


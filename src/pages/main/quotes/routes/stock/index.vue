<template>
    <!-- 股市选择 -->
    <van-tabs
        data-aos="fade-left"
        shrink
        :active="$marketType"
        :before-change="onChangeMarket"
        style="--van-tab-font-size: 12px;"
    >
        <van-tab
            v-for="({ symbol }) in STOCK_CONFIG"
            :key="symbol"
            :title="$t(`stock.${symbol}`)"
            :name="symbol"
        />
    </van-tabs>

    <van-pull-refresh
        class="main-tab__container mt-2"
        :model-value="marketLoading"
        @refresh="onChangeMarket($marketType)"
    >
        <!-- 今日股市走势图 -->
        <c-card
            data-aos="fade-left"
            data-aos-delay="50"
            :title="t('_today_trend')"
        >
            <TodayTrend :data="trend"/>
        </c-card>
        <!-- 今日股市走势图 -->

        <!-- 行业板块 -->
        <c-card
            data-aos="fade-left"
            data-aos-delay="100"
            class="my-2.5 industry"
            :title="t('_industry')"
            to="/industry"
            arrow
        >
            <van-skeleton :loading="!industryInitial">
                <template #template>
                    <div class="w-full grid grid-cols-3 gap-2.5">
                        <div
                            v-for="n in 6"
                            :key="n"
                            data-aos="zoom-in"
                            :data-aos-delay="n * 50"
                            class="border border-border rounded-md p-2.5"
                        >
                            <van-skeleton-title/>
                            <van-skeleton-paragraph/>
                            <van-skeleton-paragraph/>
                        </div>
                    </div>
                </template>

                <div class="grid grid-cols-3 gap-2.5 text-xs">
                    <div
                        class="border border-border rounded-md p-1.5"
                        v-for="({ key, gain, name, leadUp }, i) in industry"
                        data-aos="zoom-in"
                        :data-aos-delay="i * 50"
                        :key
                        @click="dispatch_checkIndustryType({ name, key })"
                    >
                        <!-- 板块名称 -->
                        <div class="truncate text-paragraph text-sm">
                            {{ name }}
                        </div>

                        <!-- 涨跌幅 -->
                        <c-amount
                            class="my-1"
                            percent
                            :color="utils_amount_color(gain)"
                            :amount="gain"
                        />

                        <!-- 领涨股 -->
                        <div class="text-xs text-text truncate">
                            {{ leadUp.name }}
                        </div>

                        <div class="text-xs flex-between gap-2.5 mt-1" :class="utils_amount_color(leadUp.gain)">
                            <!-- 限价 -->
                            <c-amount :precision="3" :amount="leadUp.latestPrice"/>

                            <!-- 涨跌幅 -->
                            <c-amount
                                percent
                                symbol
                                :amount="leadUp.gain"
                            />
                        </div>
                    </div>
                </div>
            </van-skeleton>
        </c-card>
        <!-- 行业板块 -->

        <!-- 热门股票列表 -->
        <c-card
            class="h-full"
            no-padding
        >
            <van-tabs
                ref="hotStockRef"
                class="px-4"
                shrink
                data-aos="fade-left"
                data-aos-delay="150"
                data-aos-anchor="#app"
                :active="$plateType"
                :before-change="beforeChangePlate"
            >
                <van-tab
                    v-for="key in $plateOptions"
                    :key
                    :name="key"
                    :title="$t(`stock.${key}`)"
                    :disabled="hotStockInitial && hotStockLoading"
                />

                <template #nav-right>
                    <van-icon
                        class="ml-auto"
                        name="arrow"
                        @click="$router.push('/plate')"
                    />
                </template>
            </van-tabs>

            <c-table
                class="tab-container"
                data-aos-anchor="#app"
                :columns
                :finished="hotStockFinished"
                :data-source="hotStock"
                :onRefresh="onRefreshHotStock"
                :onLoadMore="onLoadMoreHotStock"
                :disabled="hotStockDisabled"
                v-model:refresh-loading="hotStockRefreshLoading"
                v-model:load-loading="hotStockLoadLoading"
                @filter="onFilterHotStock"
                @row-click="dispatch_checkStock($event)"
            />
        </c-card>
        <!-- 热门股票列表 -->
    </van-pull-refresh>
</template>

<script setup>
import { STOCK_CONFIG } from '@/config/index.js'
import { utils_amount_color } from '@/utils/index.js'
import TodayTrend from './components/TodayTrend.vue'

const { dispatch_checkStock } = useStockStore(),
    quotesStore = useQuotesStore(),
    { dispatch_checkIndustryType } = quotesStore,
    {
        $markets,
        $marketType,
        $securityType,
        $plateOptions,
        $plateType,
    } = storeToRefs(quotesStore)

const columns = useStockColumns()

// 股市今日走势
const [
    {
        res: trend,
        onRefresh: onRefreshTrend,
    },
    {
        clearIntervalInstance: clearTrend,
    },
] = useTodayTrend({
    params: computed(() => ({
        markets: $markets.value,
        securityType: $securityType.value,
        marketType: $marketType.value,
    })),
})

// 行业板块
const [
    {
        list: industry,
        initial: industryInitial,
        onRefresh: onRefreshIndustry,
    },
    {
        clearIntervalInstance: clearIndustry,
    },
] = useIndustry(
    {
        params: computed(() => ({
            markets: $markets.value,
            securityType: $securityType.value,
            marketType: $marketType.value,
        })),
    },
    {
        paginationOptions: {
            pageSize: 6,
        },
    },
)

// 热门股票
const [
    {
        list: hotStock,
        initial: hotStockInitial,
        loading: hotStockLoading,
        pagination: hotStockPagination,
        refreshLoading: hotStockRefreshLoading,
        loadLoading: hotStockLoadLoading,
        finished: hotStockFinished,
        onRefresh: onRefreshHotStock,
        onFilter: onFilterHotStock,
        onLoadMore: onLoadMoreHotStock,
    },
    {
        clearIntervalInstance: clearStock,
    },
] = useMarketStocks({
    params: computed(() => ({
        marketType: $marketType.value,
        securityType: $securityType.value,
        plate: $plateType.value,
        ...DEFAULT_STOCK_SORT,
    })),
})

const hotStockDisabled = ref(false),
    hotStockRef = useTemplateRef('hotStockRef')

// 切换市场
const [ onChangeMarket, marketLoading ] = useFetchLoading(async (name) => {
    clearTrend()
    clearIndustry()
    clearStock()

    $marketType.value = name
    await nextTick()
    $plateType.value = STOCK_CONFIG[name].plate[0]
    hotStockRef.value.resize()

    await Promise.all([
        onRefreshTrend(),
        onRefreshIndustry(),
        onRefreshHotStock(),
    ])
})

// 切换市场板块
const beforeChangePlate = async (name) => {
    if ($plateType.value !== name) {
        clearStock()

        $plateType.value = name
        hotStock.value = []

        hotStockPagination.value.current = 1
        hotStockPagination.value.pageSize = 20
        hotStockDisabled.value = true
        try {
            await onRefreshHotStock()
        } finally {
            hotStockDisabled.value = false
        }
    }
}

const { t } = useI18n({
    useScope: 'local',
    messages: {
        [LANGUAGE.zhCN]: {
            _today_trend: '今日股市',
            _industry: '行业板块',
        },
        [LANGUAGE.zhHK]: {
            _today_trend: '今日股市',
            _industry: '行业板块',
        },
        [LANGUAGE.enUS]: {
            _today_trend: 'Today Trend',
            _industry: 'Industry',
        },
    },
})

defineOptions({ name: 'quotes-stock' })
</script>

<style scoped>
.main-tab__container {
    /*
        MarginTop 8
        Tab
    */
    height: calc(100% - 8px - var(--van-tabs-line-height) - 10px);
    overflow-x: hidden;
    overflow-y: auto;
}

.van-empty {
    padding: 0;
}
</style>

<template>
    <div class="with-header-container__noPadding px-2.5">
        <van-tabs
            data-aos="fade-left"
            data-aos-delay="50"
            class="main-tab mt-2.5"
            type="card"
            title-inactive-color="var(--paragraph)"
            v-model:active="$quotes_route"
        >
            <van-tab
                :title="$t('stock.title')"
                name="quotes-stock"
                to="/quotes"
            />

            <van-tab
                :title="$t('stock.index')"
                name="quotes-index"
                to="/quotes/index"
            />
            <van-tab
                :title="$t('stock.collect')"
                name="quotes-collect"
                to="/quotes/collect"
            />
        </van-tabs>

        <div class="tab-container">
            <router-view/>
        </div>
    </div>
</template>

<script setup>
import { $quotes_route } from '@/store'

const { name } = useRoute()

if ($quotes_route.value !== name) {
    $quotes_route.value = name
}

defineOptions({ name: 'transaction' })
</script>

<style scoped>
:deep(.main-tab > .van-tabs__wrap) {
    background: var(--card);
    border-radius: 32px;
}

:deep(.main-tab .van-tabs__nav--card) {
    margin: 0;
    border-radius: 32px;
    border: none;
    justify-content: space-between;
}

:deep(.main-tab .van-tabs__nav--card > .van-tab) {
    width: 80px;
    flex: none;
    border: none;
    border-radius: 32px;
}
</style>

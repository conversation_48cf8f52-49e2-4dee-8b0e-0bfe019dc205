<template>
    <form @submit="onValid">
        <c-card :title="t('financial.ways')">
            <c-picker
                :loading
                :options="res"
                :index="payWaysIndex"
            >
                <template #default="{ option: { icon, payWayName, payWayCode, recommended }, index, active }">
                    <div
                        class="w-1/5 aspect-square flex-center flex-col text-xs border border-border rounded relative"
                        :class="{ 'border-primary text-primary': active }"
                        @click="payWaysIndex = index; amount = '';"
                    >
                        <img
                            class="size-8 mb-2"
                            :src="icon"
                            :alt="payWayCode"
                        >

                        <div class="w-full text-center truncate">
                            {{ payWayName }}
                        </div>

                        <div v-if="recommended" class="absolute right-0 top-0">
                            <van-icon
                                name="hot-o"
                                size="20"
                            />
                        </div>
                    </div>
                </template>
            </c-picker>
        </c-card>

        <c-card class="my-4" :title="t('financial.channel')">
            <c-picker
                :loading
                :options="channels"
                :field-names="{
                    label: 'controllerTips',
                    value: 'payTypeName'
                }"
                v-model:index="payChannelIndex"
            />
        </c-card>

        <FinancialAmount
            :title="t('financial.deposit.amount')"
            :min="currentChannel.amountMinLimit"
            :max="currentChannel.amountMaxLimit"
            v-model="amount"
        >
            <c-picker
                class="mb-4"
                :loading
                :options="presetsAmount"
                v-model="amount"
            />
        </FinancialAmount>

        <c-submit
            class="mt-10 mb-4"
            :disabled="!amount"
            :loading="submitLoading"
        />
    </form>
</template>

<script setup>
import _ from 'lodash'

import FinancialAmount from '../components/FinancialAmount.vue'

const { res, loading } = useRequest({
    url: API_PATH.THIRD_CHANNEL,
    initialValues: [],
})

const payWaysIndex = ref(0),
    payChannelIndex = ref(0),
    channels = computed(() => res.value[payWaysIndex.value]?.payTypeList ?? []),
    currentChannel = computed(() => channels.value?.[payChannelIndex.value] ?? {
        amountList: '',
        amountMaxLimit: 0,
        amountMinLimit: 0,
        controllerTips: '',
        exchangeRate: 0,
        fixedAmount: false,
        payTypeId: 0,
        payTypeName: '',
        sort: 0,
    }),
    presetsAmount = computed(() => currentChannel.value.amountList.split(',').filter(Boolean).map(e => ({
        label: e,
        value: e,
    })))

const amount = ref('')

const onValid = (e) => {
    e.preventDefault()

    const { amountMinLimit, amountMaxLimit } = currentChannel.value

    if (_.inRange(+amount.value, amountMinLimit, amountMaxLimit + 1)) {
        onSubmit()
    } else {
        showFailToast(t('financial.amount_range_error'))
    }
}

const [ onSubmit, submitLoading ] = useFetchLoading(async () => {
    const { payUrl } = await api_post({
        // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/app%20%E7%BA%BF%E4%B8%8A%E5%85%85%E5%80%BC/submitOnlinePayUsingPOST_1
        url: '/onlinePay/doPay',
        params: {
            channelId: currentChannel.value.payTypeId,
            amount: amount.value,
        },
    })

    utils_link(payUrl)

    amount.value = ''
    showSuccessToast(t('operation.successfully'))
})

const { t } = useI18n()

defineOptions({ name: 'deposit-third' })
</script>

<style scoped>
</style>

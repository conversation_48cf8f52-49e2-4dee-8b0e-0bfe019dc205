<template>
    <FinancialPageContainer
        type="deposit"
        :title="$t('account.deposit')"
        to="/deposit/record"
    >
        <template #bank>
            <Bank/>
        </template>
        <template #third>
            <Third/>
        </template>
    </FinancialPageContainer>
</template>

<script setup>
import FinancialPageContainer from '../components/PageContainer.vue'
import Bank from './bank.vue'
import Third from './third.vue'

defineOptions({ name: 'deposit' })
</script>

<style scoped>

</style>

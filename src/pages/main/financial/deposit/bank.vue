<template>
    <form @submit="onValid">
        <FinancialAmount
            :title="t('financial.deposit.amount')"
            :min="channelConfig.minAmount"
            :max="channelConfig.maxAmount"
            v-model="formState.payAmount"
        >
            <BankSelector
                data-aos="fade-left"
                data-aos-delay="50"
                class="mb-4"
                :placeholder="t('form.select_placeholder', [ t('financial.bank_card') ])"
                v-model="formState.userBankId"
            />
        </FinancialAmount>

        <c-card
            class="mt-4"
            data-aos-delay="100"
            :title="t('financial.receiver')"
        >
            <c-select
                :columns="channel"
                :columns-field-names="{
                text: 'channelName',
                value: 'id'
            }"
                v-model:index="channelIndex"
            />
            <p class="text-xs mt-4 pl-3">
                <!--该通道返现比例<span class="text-special font-bold ">{{ cashbackRate }}%</span>,-->
                该通道返现金额<span
                class="text-special font-bold">{{
                    cashbackAmount
                }}</span>元
            </p>
            <van-field
                data-aos="fade-left"
                data-aos-delay="50"
                :label="t('_holder')"
                readonly
                :model-value="receiver.owner"
            />
            <van-field
                data-aos="fade-left"
                data-aos-delay="100"
                :label="t('financial.bank_number')"
                readonly
                :model-value="receiver.bank_number"
            />
            <van-field
                data-aos="fade-left"
                data-aos-delay="150"
                :label="t('_bank')"
                readonly
                :model-value="receiver.bank"
            />
            <van-field
                data-aos="fade-left"
                data-aos-delay="200"
                :label="t('_bank_address')"
                readonly
                :model-value="receiver.bank_address"
            />
        </c-card>

        <c-card
            data-aos-delay="200"
            :title="t('_order')"
            class="mt-4"
        >
            <c-input
                data-aos-delay="200"
                :placeholder="t('form.input_placeholder', [ t('_order')]) +  t('_name') "
                v-model="formState.chargeNo"
            />
        </c-card>

        <c-submit
            class="mt-10 mb-4"
            data-aos-delay="200"
            :disabled
            :loading
        />
    </form>
</template>

<script setup>
import _ from 'lodash'

import BankSelector from '../components/BankSelector.vue'
import FinancialAmount from '../components/FinancialAmount.vue'

const formState = ref({
    userBankId: null,
    payAmount: '',
    chargeNo: '',
})

const disabled = useFormDisabled(formState)

const receiver = computed(() => {
    const service = t('_service')

    const { isOnlyOffline, ownerName, bankAccount, channelName, bankAddress } = channelConfig.value
    if (isOnlyOffline) {
        return {
            owner: ownerName,
            bank_number: bankAccount,
            bank: channelName,
            bank_address: bankAddress,
        }
    } else {
        return {
            owner: service,
            bank_number: service,
            bank: service,
            bank_address: service,
        }
    }
})

const onValid = (e) => {
    e.preventDefault()

    const { minAmount, maxAmount } = channelConfig.value

    if (_.inRange(+formState.value.payAmount, minAmount, maxAmount + 1)) {
        onSubmit()
    } else {
        showFailToast(t('financial.amount_range_error'))
    }
}

const [ onSubmit, loading ] = useFetchLoading(async () => {
    // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/app%20Payment/addPayUsingPOST_1
    await api_post({
        url: '/payments/addPay',
        params: {
            ...formState.value,
            sysBankCardId: channelConfig.value.id,
        },
    })

    formState.value.payAmount = ''
    formState.value.chargeNo = ''

    showSuccessToast(t('operation.successfully'))
})

const channelIndex = ref(0)
const channelConfig = computed(() => channel.value[channelIndex.value] ?? {
    bankAccount: '',
    bankAddress: '',
    bankCode: '',
    bankRegion: '',
    channelName: '',
    id: 0,
    isOnlyOffline: false,
    maxAmount: 0,
    minAmount: 0,
    ownerName: '',
    status: 0,
})

const { res: channel } = useRequest({
    // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/app%20%E5%85%85%E5%80%BC%E6%B8%A0%E9%81%93/listUsingGET_3
    url: '/recharge/channel/list',
    initialValues: [],
})


const cashbackRate = computed(() => {
    return channel.value[channelIndex.value]?.constantGiveRate || 0
})

const cashbackAmount = computed(() => {
    return (parseFloat(cashbackRate.value) / 100 * formState.value.payAmount).toFixed(2) || 0.00
})
const { t } = useI18n({
    useScope: 'global',
    messages: {
        [LANGUAGE.zhCN]: {
            _holder: '收款人',
            _bank: '银行',
            _bank_address: '开户行',
            _order: '转账人',
            _service: '请联系在线客服',
            _range: '金额范围：',
            _name: '姓名',
        },
        [LANGUAGE.zhHK]: {
            _holder: '收款人',
            _bank: '银行',
            _bank_address: '开户行',
            _order: '转账人',
            _service: '请联系在线客服',
            _range: '金额范围：',
            _name: '姓名',
        },
        [LANGUAGE.enUS]: {
            _holder: 'Payee',
            _bank: 'Bank',
            _bank_address: 'Bank Branch',
            _order: 'Transferor',
            _service: 'Please contact online service',
            _range: 'Amount Range:',
            _name: 'Name',
        },
    },
})

defineOptions({ name: 'deposit-bank' })
</script>

<style scoped>
.van-cell {
    padding: 16px 12px;
}
</style>

<template>
    <FinancialPageContainer
        type="withdrawal"
        :title="$t('account.withdrawal')"
        to="/withdrawal/record"
    >
        <template #bank>
            <Bank/>
        </template>
        <template #third>
            <Third/>
        </template>
    </FinancialPageContainer>
</template>

<script setup>
import FinancialPageContainer from '@/pages/main/financial/components/PageContainer.vue'
import Bank from './bank.vue'
import Third from './third.vue'

defineOptions({ name: 'withdrawal' })
</script>

<style scoped>

</style>

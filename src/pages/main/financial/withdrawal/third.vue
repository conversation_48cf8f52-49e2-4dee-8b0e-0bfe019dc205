<template>
    <form @submit="onValid">
        <c-card :title="t('financial.ways')">
            <c-picker
                :loading="$channelLoading"
                :options="$channel"
                :index="payWaysIndex"
            >
                <template #default="{ option: { icon, payWayName, payWayCode, recommended }, index, active }">
                    <div
                        class="w-1/5 aspect-square flex-center flex-col text-xs border border-border rounded relative"
                        :class="{ 'border-primary text-primary': active }"
                        @click="payWaysIndex = index; amount = ''; walletId = null;"
                    >
                        <img
                            class="size-8 mb-2"
                            :src="icon"
                            :alt="payWayCode"
                        >

                        <div class="w-full text-center truncate">
                            {{ payWayName }}
                        </div>

                        <div v-if="recommended" class="absolute right-0 top-0">
                            <van-icon
                                name="hot-o"
                                size="20"
                            />
                        </div>
                    </div>
                </template>
            </c-picker>
        </c-card>

        <c-card class="my-4" :title="t('financial.channel')">
            <c-picker
                :loading="$channelLoading"
                :options="currentWay.payTypeList"
                :field-names="{
                    label: 'controllerTips',
                    value: 'payTypeName'
                }"
                v-model:index="payChannelIndex"
            />
        </c-card>

        <c-card
            class="mb-4"
            :title="t('_to')"
        >
            <c-select
                :columns="_wallet"
                :columns-field-names="{
                    text: 'payAddress',
                    value: 'id',
                }"
                :placeholder="t('form.select_placeholder', [ t('financial.wallet.title') ])"
                v-model="walletId"
            >
                <template #option="{ bankCode, payAddress }">
                    <span class="text-text mr-2">{{ bankCode }}</span>
                    <span class="text-title">{{ payAddress }}</span>
                </template>

                <template #columns-bottom>
                    <div
                        class="h-11 flex-center gap-2 text-sm text-link border-t border-border"
                        @click="push('/wallet/add')"
                    >
                        <van-icon name="plus"/>
                        <span>{{ t('financial.wallet.add') }}</span>
                    </div>
                </template>
            </c-select>
        </c-card>

        <FinancialAmount
            :title="t('financial.withdrawal.amount')"
            :min="currentChannel.amountMinLimit"
            :max
            v-model="amount"
        >
            <c-picker
                class="mb-4"
                :loading="$channelLoading"
                :options="presetsAmount"
                v-model="amount"
            />
        </FinancialAmount>

        <c-submit
            class="mt-10 mb-4"
            data-aos-delay="150"
            :disabled="!amount || !walletId"
            :loading="submitLoading"
        />
    </form>

    <Password/>
</template>

<script setup>
import _ from 'lodash'

import FinancialAmount from '../components/FinancialAmount.vue'

const { push } = useRouter()
const { $profile } = storeToRefs(useProfileStore())
const { $withdrawalConfig } = storeToRefs(useWithdrawalStore())

const { dispatch_refreshSpot } = useAccountStore(),
    { $spot } = storeToRefs(useAccountStore()),
    walletStore = useWalletStore(),
    { dispatch_refreshChannel } = walletStore,
    { $channel, $channelLoading, $wallet } = storeToRefs(walletStore)

dispatch_refreshChannel()

const max = computed(() => Math.min(currentChannel.value.amountMaxLimit, $spot.value.usableCash))

const _wallet = computed(() => _.filter($wallet.value, { bankCode: currentWay.value.payWayCode }))

const payWaysIndex = ref(0),
    currentWay = computed(() => $channel.value[payWaysIndex.value] ?? {
        currency: '',
        icon: '',
        payTypeList: [],
        payWayCode: '',
        payWayName: '',
        recommended: false,
    }),
    payChannelIndex = ref(0),
    currentChannel = computed(() => currentWay.value.payTypeList?.[payChannelIndex.value] ?? {
        amountList: '',
        amountMaxLimit: 0,
        amountMinLimit: 0,
        controllerTips: '',
        exchangeRate: 0,
        fixedAmount: false,
        payTypeId: 0,
        payTypeName: '',
        sort: 0,
    }),
    presetsAmount = computed(() => currentChannel.value.amountList.split(',').filter(Boolean).map(e => ({
        label: e,
        value: e,
    })))

const amount = ref(''),
    walletId = ref(null)

const [ onSubmit, submitLoading ] = useFetchLoading(async (password) => {
    await api_post({
        url: API_PATH.WITHDRAWAL,
        params: {
            amount: amount.value,
            userBankCardId: walletId.value,
            channelId: currentChannel.value.payTypeId,
            password: btoa(password),
            type: 3,
        },
    })

    amount.value = ''

    showSuccessToast(t('operation.successfully'))

    dispatch_refreshSpot()
})

const { onOpenPassword, Password } = usePassword(onSubmit)

const onValid = e => {

    if (!$withdrawalConfig.value.testUsersCanWithdraw && +$profile.value.type === 2) {
        showFailToast(t('operation.test_account_cannot_withdraw'))
        return false
    }

    e.preventDefault()

    const { amountMinLimit } = currentChannel.value

    if (_.inRange(amount.value, amountMinLimit, max.value + 1)) {
        onOpenPassword()
    } else {
        showFailToast(t('financial.amount_range_error'))
    }
}

const { t } = useI18n({
    useScope: 'global',
    messages: {
        [LANGUAGE.zhCN]: {
            _to: '提现至',
        },
        [LANGUAGE.zhHK]: {
            _to: '提现至',
        },
        [LANGUAGE.enUS]: {
            _to: '提现至',
        },
    },
})

defineOptions({ name: 'withdrawal-third' })
</script>

<style scoped>

</style>

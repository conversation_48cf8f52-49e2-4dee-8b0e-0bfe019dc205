<template>
    <c-header :title="t('_title')"/>

    <c-record
        class="with-header-container"
        :finished
        :onRefresh
        :onLoadMore
        v-model:refresh-loading="refreshLoading"
        v-model:load-loading="loadLoading"
    >
        <c-card
            class="not-last:mb-2.5"
            v-for="item in list"
            :key="item.id"
        >
            <c-description
                v-for="({ label, key, render }) in columns"
                :key
                :label
                :value="render ? render(item[key], item) : item[key]"
            />
        </c-card>
    </c-record>
</template>

<script setup>
const { list, refreshLoading, loadLoading, finished, onRefresh, onLoadMore } = usePagination({
    // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/withdrawal-controller/getWithdrawListUsingGET_1
    url: '/withdrawal/list',
})

const STATUS_DICT = {
    0: {
        label: '待审核',
        color: 'gray',
    },
    1: {
        label: '出款中',
        color: 'blue',
    },
    2: {
        label: '出款成功',
        color: 'green',
    },
    3: {
        label: '出款失败',
        color: 'red',
    },
    4: {
        label: '撤销',
        color: 'black',
    },
}

const columns = computed(() => [
    { label: '提现通道', key: 'channelName' },
    { label: '提现金额', key: 'chargeAmount', render: val => utils_currency(val) },
    { label: '手续费', key: 'chargeFee', render: val => utils_currency(val) },
    { label: '实际到账金额', key: 'realAmount', render: val => utils_currency(val) },
    { label: '币种', key: 'chargeCurrency' },
    { label: '提现信息', key: 'reason' },
    {
        label: t('common.status'),
        key: 'status',
        render: val => {
            const dict = STATUS_DICT[val]

            return () => h(
                'span',
                {
                    style: {
                        color: dict.color,
                    },
                },
                t(`dict.${dict.label}`),
            )
        },
    },
    { label: t('common.create_time'), key: 'createTime' },
])

const { t } = useI18n({
    useScope: 'global',
    messages: {
        [LANGUAGE.zhCN]: {
            _title: '提现记录',
        },
        [LANGUAGE.zhHK]: {
            _title: '提现记录',
        },
        [LANGUAGE.enUS]: {
            _title: 'Withdrawal Record',
        },
    },
})

defineOptions({ name: 'withdrawal-record' })
</script>

<style scoped>

</style>

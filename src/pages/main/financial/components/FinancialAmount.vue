<template>
    <c-card
        data-aos-delay="100"
        :title
    >
        <slot/>

        <c-input
            data-aos="fade-left"
            data-aos-delay="50"
            :placeholder="placeholder ?? t('form.input_placeholder', [ title ])"
            type="number"
            inputmode="decimal"
            :min="Math.max(0, min)"
            :max="max"
            v-model="modelValue"
        />
        <div v-if="!hideBalance" class="text-sm my-4">
            {{ t('account.balance') }}
            <c-amount :amount="$spot.usableCash"/>
        </div>

        <div class="text-special text-xs">
            <sup>*</sup>{{ t('financial.min') }}
            <c-amount :amount="min"/>
            ，{{ t('financial.max') }}
            <c-amount :amount="max"/>
        </div>
    </c-card>
</template>

<script setup>
defineProps({
    title: {
        title: String,
        required: true,
    },
    min: {
        type: Number,
        required: true,
    },
    max: {
        type: Number,
        required: true,
    },
    placeholder: String,
    hideBalance: Boolean,
})

const modelValue = defineModel({
    type: [ String, Number ],
    required: false,
})

const { $spot } = storeToRefs(useAccountStore())

const { t } = useI18n()

defineOptions({ name: 'FinancialAmount' })
</script>

<style scoped>

</style>

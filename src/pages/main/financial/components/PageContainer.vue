<template>
    <c-header :title>
        <template #right>
            <van-icon
                v-if="to"
                name="todo-list-o"
                size="20"
                @click="push(to)"
            />
        </template>
    </c-header>

    <div class="with-header-container__noPadding px-2.5 pb-2.5">
        <van-tabs
            data-aos="fade-left"
            v-model:active="activeTab"
        >
            <van-tab
                class="pt-2.5"
                name="bank"
                :title="t('_bank')"
            >
                <slot name="bank"/>
            </van-tab>
            <van-tab
                class="pt-2.5"
                name="third"
                :title="t('_third')"
            >
                <slot name="third"/>
            </van-tab>
        </van-tabs>

        <c-service />
    </div>
</template>

<script setup>
const { type } = defineProps({
    type: {
        type: String,
        required: true,
    },
    title: {
        type: String,
        required: true,
    },
    to: {
        type: [ String, Object ],
    },
})

const { params: { tab } } = useRoute(),
    { push } = useRouter()

const activeTab = useSessionStorage(`${type}ActiveTab`, tab)

const { t } = useI18n({
    messages: {
        [LANGUAGE.zhCN]: {
            _bank: '银行卡',
            _third: '三方渠道',
        },
        [LANGUAGE.zhHK]: {
            _bank: '银行卡',
            _third: '三方渠道',
        },
        [LANGUAGE.enUS]: {
            _bank: 'Bank',
            _third: 'Third Channel',
        },
    },
})

defineOptions({ name: 'FinancialPageContainer' })
</script>

<style scoped>
</style>

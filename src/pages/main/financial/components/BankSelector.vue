<template>
    <c-select
        :placeholder
        :columns="$bankList"
        :columns-field-names="{
            text: 'bankFullName',
            value: 'id'
        }"
        v-model="modelValue"
    >
        <template #option="{ bankFullName, bankAccount }">
            <span class="text-title mr-2">{{ bankFullName }}</span>
            <span class="text-text">{{ bankAccount }}</span>
        </template>

        <template #empty>
            <van-empty :description="$t('financial.no_bank')"/>
        </template>

        <template #columns-bottom>
            <div
                class="h-11 flex-center gap-2 text-sm text-link border-t border-border"
                @click="$router.push('/bank/add')"
            >
                <van-icon name="plus"/>
                <span>{{ $t('financial.add_bank') }}</span>
            </div>
        </template>
    </c-select>
</template>

<script setup>
defineProps({
    placeholder: String,
})

const modelValue = defineModel({
    required: true,
})

const bankStore = useBankStore(),
    { dispatch_refreshBank } = bankStore,
    { $bankList } = storeToRefs(bankStore)

dispatch_refreshBank()

defineOptions({ name: 'BankSelector' })
</script>

<style scoped>

</style>

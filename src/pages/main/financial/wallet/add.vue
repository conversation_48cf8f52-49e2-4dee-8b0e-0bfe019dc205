<template>
    <c-header :title="t('financial.wallet.add')" />

    <form class="with-header-container" @submit="onSubmit">
        <c-card>
            <c-input
                :label="t('_address')"
                :placeholder="t('form.input_placeholder', [ t('_address') ])"
                v-model="formState.payAddress"
            />
            <c-select
                class="mt-4"
                :label="t('_channel')"
                :placeholder="t('form.select_placeholder', [ t('_channel') ])"
                :columns="$channel"
                :columns-field-names="{
                    text: 'payWayName',
                    value: 'payWayCode'
                }"
                v-model="formState.bankCode"
            />
        </c-card>

        <c-submit
            class="mt-10 mb-4"
            :disabled
            :loading="submitLoading"
        />

        <c-service />
    </form>
</template>

<script setup>
const walletStore = useWalletStore(),
    { dispatch_refreshWallet } = walletStore,
    { $channel } = storeToRefs(walletStore)

const { back } = useRouter()

const formState = ref({
    payAddress: '',
    bankCode: null
})

const disabled = useFormDisabled(formState)

const [ onSubmit, submitLoading ] = useFetchLoading(async(e) => {
    e.preventDefault()

    await api_post({
        // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E7%94%A8%E6%88%B7%E9%92%B1%E5%8C%85/bindUsingPOST_1
        url: '/user_wallet/bind',
        params: formState.value
    })

    formState.value = {
        payAddress: '',
        bankCode: null
    }

    showSuccessToast(t('operation.successfully'))

    Promise.all([
        dispatch_refreshWallet(),
        back()
    ])
})

const { t } = useI18n({
    useScope: 'global',
    messages: {
        [LANGUAGE.zhCN]: {
            _address: '钱包地址',
            _channel: '钱包渠道'
        },
        [LANGUAGE.zhHK]: {
            _address: '钱包地址',
            _channel: '钱包渠道'
        },
        [LANGUAGE.enUS]: {
            _address: 'Wallet Address',
            _channel: 'Wallet Channel'
        },
    },
})

defineOptions({ name: 'wallet-add' })
</script>

<style scoped>

</style>
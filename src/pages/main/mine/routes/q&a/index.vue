<template>
    <c-header :title="$t('header.q&a')"/>

    <div class="with-header-container text-text text-xs">
        <Search
            data-aos="fade-left"
            :placeholder="t('_placeholder')"
            readonly
            @click="$router.push('/q&a/search')"
        />

        <van-skeleton :loading="!initial">
            <template #template>
                <div class="w-full">
                    <div
                        data-aos="fade-left"
                        class="bg-bg rounded-lg mt-2.5 custom-shadow p-5"
                        v-for="n in 3"
                        :key="n"
                        :data-aos-delay="`${n * 50}`"
                    >
                        <van-skeleton-title/>
                        <van-skeleton-paragraph/>
                        <van-skeleton-paragraph/>
                        <van-skeleton-paragraph/>
                    </div>
                </div>
            </template>

            <c-card
                class="mt-2.5"
                v-for="({ typeId, name, questions }) in res"
                :key="typeId"
                :title="name"
            >
                <div
                    data-aos="fade-left"
                    data-aos-anchor="#app"
                    class="h-10 flex-between border-t border-border"
                    v-for="({ id, title }, i) in questions"
                    :data-aos-delay="i * 50"
                    :key="id"
                    @click="$router.push(`/q&a/${id}`)"
                >
                    <span>{{ title }}</span>

                    <van-icon name="arrow"/>
                </div>
            </c-card>
        </van-skeleton>
    </div>
</template>

<script setup>
import Search from '@/components/Search/index.vue'
import { useQ_AI18n } from './hooks.js'

const { res, initial } = useRequest({
    // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/App%E5%B8%AE%E5%8A%A9%E4%B8%AD%E5%BF%83/getQuestionsUsingGET_1
    url: '/help/list',
    initialValues: [],
})

const { t } = useQ_AI18n()

defineOptions({ name: 'q&a' })
</script>

<style scoped>
</style>

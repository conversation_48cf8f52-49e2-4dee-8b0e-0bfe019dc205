export const useQ_AI18n = () => useI18n({
    useScope: 'global',
    messages: {
        [LANGUAGE.zhCN]: {
            _placeholder: '搜索您遇到的问题或关键词',
            _hot: '热门问题',
            _result: '搜索到与{0}相关的结果有{1}条',
            _service: '没有解决相关问题？联系',
        },
        [LANGUAGE.zhHK]: {
            _placeholder: '搜索您遇到的问题或关键词',
            _hot: '热门问题',
            _result: '搜索到与{0}相关的结果有{1}条',
            _service: '没有解决相关问题？联系',
        },
        [LANGUAGE.enUS]: {
            _placeholder: 'Search questions or keywords',
            _hot: 'Search questions or keywords',
            _result: '搜索到与{0}相关的结果有{1}条',
            _service: '没有解决相关问题？联系',
        },
    },
})

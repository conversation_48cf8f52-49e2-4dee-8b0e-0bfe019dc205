<template>
    <c-header :title="$t('header.q&a')"/>

    <div class="with-header-container text-text">
        <Search
            data-aos="fade-left"
            :placeholder="t('_placeholder')"
            v-model="content"
            @search="onSearch"
        />

        <div class="text-title">
            <div class="flex-middle gap-2 h-10 text-base">
                <van-icon class="text-red-500" name="fire"/>

                <div>
                    {{ t('_hot') }}
                </div>
            </div>

            <template v-if="content">
                <van-highlight
                    class="text-xs mb-2.5 normal-case"
                    :keywords
                    :source-string="source"
                />

                <van-cell-group v-if="result.length" inset>
                    <van-cell
                        is-link
                        v-for="({ id, title }) in result"
                        :key="id"
                        :title
                        :to="`/q&a/${id}`"
                    />
                </van-cell-group>

                <template v-else>
                    <van-empty :description="t('common.empty')"/>

                    <c-service :tip="t('_service')"/>
                </template>
            </template>
        </div>
    </div>
</template>

<script setup>
import Search from '@/components/Search/index.vue'
import { useQ_AI18n } from './hooks.js'

const content = ref(''),
    result = ref([]),
    keywords = computed(() => [ content.value, `${result.value.length}` ]),
    source = computed(() => t('_result', keywords.value))

const [ onSearch ] = useFetchLoading(async (keyword) => {
    // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/App%E5%B8%AE%E5%8A%A9%E4%B8%AD%E5%BF%83/getQuestionListUsingGET_1
    result.value = await api_get({
        url: '/help/question/search',
        params: {
            keyword,
        },
        options: {
            headers: {
                keepLatest: true,
            },
        },
    })
}, { keepLatest: true })

const { t } = useQ_AI18n()

defineOptions({ name: 'q&a-search' })
</script>

<style scoped>
.van-cell-group--inset {
    margin: 0;
}
</style>

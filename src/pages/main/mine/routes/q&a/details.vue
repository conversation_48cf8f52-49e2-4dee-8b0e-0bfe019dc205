<template>
    <c-header :title="t('_title')"/>

    <div class="with-header-container">
        <c-card>
            <van-skeleton
                title
                :row="5"
                :loading
            >
                <div class="text-title text-lg font-semibold mb-2.5">
                    {{ res.title }}
                </div>

                <div
                    class="text-title"
                    v-html="res.content"
                />
            </van-skeleton>

            <div class="text-paragraph mt-5">
                {{ t('_tip') }}
            </div>

            <div class="flex-between mt-4">
                <van-button
                    type="primary"
                    :loading="solveLoading"
                    :disabled="unsolvedLoading"
                    @click="onSolved"
                >
                    {{ t('_solved') }}
                </van-button>

                <van-button
                    :disabled="solveLoading"
                    @click="collapse = true"
                >
                    {{ t('_unresolved') }}
                </van-button>
            </div>

            <van-form
                class="mt-5"
                v-if="collapse"
                @submit="onUnsolved"
            >
                <van-field
                    border
                    type="textarea"
                    class="border border-[var(--van-button-default-border-color)] rounded"
                    :placeholder="t('_placeholder')"
                    name="content"
                    v-model="formState.content"
                />

                <div class="flex-between mt-4">
                    <van-button
                        type="primary"
                        native-type="submit"
                        :loading="unsolvedLoading"
                        :disabled="solveLoading"
                    >
                        {{ t('operation.confirm') }}
                    </van-button>

                    <van-button
                        native-type="reset"
                        :disabled="unsolvedLoading"
                        @click="collapse = false"
                    >
                        {{ t('operation.cancel') }}
                    </van-button>
                </div>
            </van-form>
        </c-card>
    </div>
</template>

<script setup>
const { params: { id } } = useRoute()

const collapse = ref(false)

const formState = ref({
    content: '',
})

const { res, loading } = useRequest({
    // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/App%E5%B8%AE%E5%8A%A9%E4%B8%AD%E5%BF%83/getQuestionDetailUsingGET_1
    url: '/help/question/detail',
    params: {
        id,
    },
    initialValues: {
        content: '',
        id: 0,
        title: '',
    },
})

const [ onSolved, solveLoading ] = useFetchLoading(async () => {
    const msg = await api_post({
        // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E9%97%AE%E9%A2%98%E5%8F%8D%E9%A6%88/solvedUsingPOST_1
        url: '/question/feedback/solved',
        params: {
            questionId: id,
        },
    })

    showSuccessToast(msg)
})

const [ onUnsolved, unsolvedLoading ] = useFetchLoading(async () => {
    const msg = await api_post({
        // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E9%97%AE%E9%A2%98%E5%8F%8D%E9%A6%88/unsolvedUsingPOST_1
        url: '/question/feedback/unsolved',
        params: {
            questionId: id,
            content: formState.value.content,
        },
    })

    collapse.value = false
    formState.value.content = ''

    showSuccessToast(msg)
})

const { t } = useI18n({
    useScope: 'global',
    messages: {
        [LANGUAGE.zhCN]: {
            _title: '意见反馈',
            _tip: '以上内容是否解决您的问题？',
            _solved: '已解决',
            _unresolved: '未解决',
            _placeholder: '您可以在这里留下更多反馈或意见,我们将根据具体反馈进行优化...',
        },
        [LANGUAGE.zhHK]: {
            _title: '意见反馈',
            _tip: '以上内容是否解决您的问题？',
            _solved: '已解决',
            _unresolved: '未解决',
            _placeholder: '您可以在这里留下更多反馈或意见,我们将根据具体反馈进行优化...',
        },
        [LANGUAGE.enUS]: {
            _title: 'Feedback',
            _tip: 'Does the above content solve your problem?',
            _solved: 'Solved',
            _unresolved: 'Unresolved',
            _placeholder: 'You can leave more feedback or comments here, we will optimize according to the specific feedback...',
        },
    },
})

defineOptions({ name: 'q&a-details' })
</script>

<style scoped>

</style>

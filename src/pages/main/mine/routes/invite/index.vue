<template>
    <div class="h-full invite">
        <c-header :title="t('_title')" transparent/>

        <div class="with-header-container">
            <div class="text-center text-[var(--title)]">
                <div
                    data-aos="fade-left"
                    data-aos-delay="50"
                    class="text-4xl"
                >
                    {{ t('_subhead') }}
                </div>

                <div
                    data-aos="fade-left"
                    data-aos-delay="100"
                    class="mt-1 whitespace-pre-line"
                >
                    {{ t('_description') }}
                </div>

                <div
                    data-aos="fade-left"
                    data-aos-delay="150"
                    class="mx-auto w-max mt-28 rounded-full h-9 px-2.5 text-xs flex-middle gap-2.5 bg-white/30 go"
                    @click="$router.push('/vip')"
                >
                    <div>{{ t('_vip') }}</div>

                    <img
                        src="./assets/go.png"
                        alt="go"
                        class="size-6"
                    >
                </div>
            </div>

            <c-card class="my-4">
                <div class="grid grid-cols-2 gap-4 mb-4">
                    <div class="block" @click="show = true">
                        <van-icon :size="20" name="qr"/>

                        <div>{{ t('_qrcode') }}</div>
                    </div>

                    <div class="block" @click="onCopy(res.inviteCode)">
                        <div class="h-5 leading-5 flex-middle gap-2">
                            <span>{{ res.inviteCode }}</span>
                            <c-icon size="14" name="copy"/>
                        </div>

                        <div>{{ t('_referrer') }}</div>
                    </div>
                </div>

                <van-button
                    block
                    type="primary"
                    @click="onCopy()"
                >
                    <van-icon name="link-o"/>
                    {{ t('_link') }}
                </van-button>
            </c-card>

            <c-card>
                <Divider :size="[ 1, 64 ]" class="flex-middle h-24 text-text">
                    <div class="flex-1 text-center">
                        <div class="text-title">{{ t('vip.trade_rebate_ratio') }}</div>
                        <c-amount
                            class="text-[var(--number)] text-3xl"
                            :amount="res.commissionRate"
                            percent
                        />
                    </div>

                    <div class="flex-1 text-center">
                        <div class="text-title">{{ t('vip.interest_rebate_ratio') }}</div>
                        <c-amount
                            class="text-[var(--number)] text-3xl"
                            :amount="res.interestRate"
                            percent
                        />
                    </div>
                </Divider>

                <div class="pt-4 flex-between border-t border-border text-sm text-title">
                    <div>{{ t('_commission') }}</div>
                    <c-amount
                        :amount="res.totalCommission"
                        currency="CNY"
                    />
                </div>
            </c-card>
        </div>
    </div>

    <van-popup
        v-model:show="show"
    >
        <img
            :src="qrcode"
            alt="QR Code"
            class="size-[200px]"
        />
    </van-popup>
</template>

<script setup>
import { useQRCode } from '@vueuse/integrations/useQRCode'

import Divider from '@/components/Divider/index.vue'

const show = ref(false)

const href = shallowRef('')

const { res } = useRequest({
    // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E9%82%80%E8%AF%B7%E5%A5%BD%E5%8F%8B/detailUsingGET_1
    url: '/invite/detail',
    initialValues: {
        commissionRate: 0,
        interestRate: 0,
        inviteCode: '',
        totalCommission: 0,
    },
    onSuccess: (res) => {
        href.value = window.location.origin + '/#/?inviteCode=' + res.inviteCode
    },
})

const { copy } = useClipboard({ source: href })

const onCopy = (content) => {
    copy(content)
    showSuccessToast(t('operation.copy_successfully'))
}

const qrcode = useQRCode(href)

const { t } = useI18n({
    useScope: 'global',
    messages: {
        [LANGUAGE.zhCN]: {
            _title: '邀请返佣',
            _subhead: '邀请好友 赚取佣金',
            _description: '进行股票，股指交易佣金最高达90%',
            _vip: '等级越高，赚取佣金比例更高还有机会获得锦鲤奖！',
            _qrcode: '二维码',
            _referrer: '推广码',
            _link: '生成邀请链接',
            _commission: '我的佣金',
        },
        [LANGUAGE.zhHK]: {
            _title: '邀请返佣',
            _subhead: '邀请好友 赚取佣金',
            _description: '进行股票，股指交易佣金最高达90%',
            _vip: '等级越高，赚取佣金比例更高还有机会获得锦鲤奖！',
            _qrcode: '二维码',
            _referrer: '推广码',
            _link: '生成邀请链接',
            _commission: '我的佣金',
        },
        [LANGUAGE.enUS]: {
            _title: 'Referral Rewards',
            _subhead: 'Invite friends, earn commissions',
            _description: 'Commissions up to 90% for stock and index trading',
            _vip: 'Upgrade VIP level to get higher commission ratio!',
            _qrcode: 'QR Code',
            _referrer: 'Referral Code',
            _link: 'Generate Invitation Link',
            _commission: 'My Commission',
        },
    },
})

defineOptions({ name: 'invite' })
</script>

<style scoped>
.invite {
    background-position: 0 100px, center 129px, 0 0;
    background-repeat: no-repeat;
    background-size: 171px 154px, 242px 163px, 100% 100%;
}

[data-theme="light"] .invite {
    background-image: url('./assets/bg_1.png'), url('./assets/bg_2.png'), linear-gradient(180deg, #E64A41 0%, #FFF3EF 100%);
    --title: #FFF5E5;
    --text: #B7655F;
    --number: #EE7D75;
}

[data-theme="dark"] .invite {
    background-image: url('./assets/bg_1.png'), url('./assets/bg_2.png'), linear-gradient(180deg, #14183C 0%, #0E101E 100%);
    --title: #646ADC;
    --text: #8F94F5;
    --number: var(--text);
}

.block {
    height: 64px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 4px;
    font-size: 12px;
    border-radius: 8px;
    color: var(--text);
}

[data-theme="light"] .block {
    border: 1px solid #FFEDED;
    background: linear-gradient(180deg, #FFE1DE 0%, #FFF8F8 100%);
}

[data-theme="dark"] .block {
    border: 1px solid transparent;
    background: #0D1233;
    backdrop-filter: blur(10px);
}

[data-theme="light"] .go {
    color: #873F3A;
}

[data-theme="dark"] .go {
    color: #0D1233;
}
</style>

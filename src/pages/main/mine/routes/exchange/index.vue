<template>
    <c-header :title="t('_title')">
        <template #right>
            <van-icon
                name="replay"
                :class="{ 'animate-spin': loading }"
                @click="onRefresh"
            />
        </template>
    </c-header>

    <div class="with-header-container">
        <c-card
            class="not-last:mb-2.5 flex-middle gap-4 text-title"
            v-for="({ currencyBase, currencyTarget, rate }) in data"
            :key="currencyTarget"
        >
            <c-icon size="32" name="CN"/>

            <div class="flex-middle">
                <van-field
                    class="flex-1 border-b border-border"
                    clearable
                    type="number"
                    :min="0"
                    :max="100000000"
                    v-model="principal[currencyTarget]"
                />
                {{ currencyBase }} ≈ {{ utils_currency(principal[currencyTarget] * rate, { precision: 4 }) }}{{ currencyTarget }}
            </div>

            <c-icon size="32" :name="currencyTarget.slice(0, 2)"/>
        </c-card>
    </div>
</template>

<script setup>
import { utils_currency } from '@/utils/index.js'

const rateStore = useRateStore(),
    { dispatch_refreshRate } = rateStore,
    { $rateConfig } = storeToRefs(rateStore)

const principal = ref({
    USD: 1,
    HKD: 1
})

const [ onRefresh, loading ] = useFetchLoading(dispatch_refreshRate)

const data = computed(() => $rateConfig.value.filter(e => e.currencyTarget !== CURRENCY.CNY))

const { t } = useI18n({
    messages: {
        [LANGUAGE.zhCN]: {
            _title: '汇率换算',
        },
        [LANGUAGE.zhHK]: {
            _title: '汇率换算',
        },
        [LANGUAGE.enUS]: {
            _title: 'Exchange rate conversion',
        },
    },
})

defineOptions({ name: 'exchange' })
</script>

<style scoped>

</style>

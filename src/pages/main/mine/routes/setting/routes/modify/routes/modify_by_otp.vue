<template>
    <c-header :title="t('modify_password.by_otp')"/>

    <div class="with-header-container">
        <van-form
            required="auto"
            :label-width="isEn ? 140 : undefined"
            @submit="onValid"
        >
            <van-cell-group data-aos="fade-left">
                <van-field
                    type="tel"
                    disabled
                    :maxlength="11"
                    :model-value="$profile.mobile"
                    :label="t('profile.mobile')"
                />

                <van-field
                    type="digit"
                    clearable
                    :maxlength="6"
                    :label="t('auth.otp')"
                    :placeholder="t('form.input_placeholder', [ t('auth.otp') ])"
                    :rules="[ { required: true } ]"
                    v-model="formState.smsCode"
                >
                    <template #button>
                        <van-button
                            type="primary"
                            size="mini"
                            :disabled="!$profile.mobile || otpDisabled"
                            :loading="otpLoading"
                            @click="onSendOtp($profile.mobile)"
                        >
                            {{ otpText }}
                        </van-button>
                    </template>
                </van-field>

                <template v-if="passwordType.account">
                    <van-field
                        autocomplete="new-password"
                        type="password"
                        clearable
                        :label="t('_new')"
                        :placeholder="t('auth.password_regex')"
                        :rules="[ { required: true } ]"
                        v-model="formState.password"
                    />

                    <van-field
                        type="password"
                        clearable
                        :label="t('_repeat')"
                        :placeholder="t('form.input_placeholder')"
                        :rules="[ { required: true } ]"
                        v-model="formState.repeat"
                    />
                </template>
            </van-cell-group>

            <c-controller
                v-if="passwordType.financial"
                class="mt-4"
                required
                :label="t('_new')"
            >
                <van-password-input
                    class="w-full"
                    :gutter="10"
                    :value="formState.password"
                    @focus="keyboard = true"
                />
                <c-keyboard
                    v-model="formState.password"
                    v-model:visible="keyboard"
                />
            </c-controller>

            <c-submit
                class="mt-10"
                :disabled
                :loading
            />
        </van-form>
    </div>
</template>

<script setup>
import { usePasswordI18n, usePasswordType } from '../hooks.js'
import { OTP_VERIFY } from '@/hooks/index.js'

const passwordType = usePasswordType()

const { t, locale } = usePasswordI18n(),
    isEn = locale.value === LANGUAGE.enUS

const { $profile } = storeToRefs(useProfileStore())

const { otpText, otpLoading, otpDisabled, onSendOtp } = useOtp({ type: OTP_TYPES.UPDATE_PASSWORD })

const keyboard = ref(false)

const formState = ref({
    smsCode: '',
    password: '',
    repeat: '',
})

const disabled = useFormDisabled(formState, passwordType.financial ? 'repeat' : undefined)

const onValid = () => {
    const { smsCode, password, repeat } = formState.value

    if (!REGULAR.OTP.test(smsCode)) return showFailToast(t('auth.otp_error'))

    if (passwordType.account) {
        if (!REGULAR.NEW_PASSWORD.test(password)) return showFailToast(t('auth.password_error'))
        if (!REGULAR.NEW_PASSWORD.test(repeat)) return showFailToast(t('auth.password_error'))

        if (password !== repeat) return showFailToast(t('auth.repeat_error'))
    }
    if (passwordType.financial) {
        if (!REGULAR.OTP.test(password)) return showFailToast(t('auth.password_error'))
    }

    onSubmit()
}

const [ onSubmit, loading ] = useFetchLoading(async () => {
    const { smsCode, password } = formState.value

    await api_post({
        url: API_PATH.UPDATE_PASSWORD,
        params: {
            mobile: $profile.value.mobile,
            smsCode,
            newPassword: btoa(password),
            verifyType: OTP_VERIFY.MOBILE,
            type: passwordType.account ? 1 : 2,
        },
    })

    showSuccessToast(t('operation.successfully'))

    if (passwordType.account) logout()
})

defineOptions({ name: 'modify_password_by_otp' })
</script>

<style scoped>

</style>

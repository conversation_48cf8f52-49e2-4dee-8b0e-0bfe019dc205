<template>
    <c-header :title="t('modify_password.by_original')"/>

    <div class="with-header-container">
        <van-form
            required="auto"
            :label-width="isEn ? 140 : undefined"
            @submit="onValid"
        >
            <van-cell-group
                v-if="passwordType.account"
                data-aos="fade-left"
                data-aos-anchor="#app"
            >
                <van-field
                    type="password"
                    autofocus
                    clearable
                    :label="t('_original')"
                    :placeholder="t('form.input_placeholder')"
                    :rules="[ { required: true } ]"
                    v-model="formState.original"
                />

                <van-field
                    type="password"
                    clearable
                    :label="t('_new')"
                    :placeholder="t('auth.password_regex')"
                    :rules="[ { required: true } ]"
                    v-model="formState.password"
                />

                <van-field
                    type="password"
                    clearable
                    :label="t('_repeat')"
                    :placeholder="t('form.input_placeholder')"
                    :rules="[ { required: true } ]"
                    v-model="formState.repeat"
                />
            </van-cell-group>

            <template v-else>
                <c-controller
                    required
                    :label="t('_original')"
                >
                    <van-password-input
                        class="w-full"
                        :gutter="10"
                        :value="formState.original"
                        @focus="originalKeyboard = true; passwordKeyboard = false"
                    />
                    <c-keyboard
                        v-model="formState.original"
                        v-model:visible="originalKeyboard"
                    />
                </c-controller>
                <c-controller
                    class="mt-4"
                    required
                    :label="t('_new')"
                >
                    <van-password-input
                        class="w-full"
                        :gutter="10"
                        :value="formState.password"
                        @focus="passwordKeyboard = true; originalKeyboard = false"
                    />
                    <c-keyboard
                        v-model="formState.password"
                        v-model:visible="passwordKeyboard"
                    />
                </c-controller>
            </template>

            <c-submit
                class="mt-10"
                :disabled
                :loading
            />
        </van-form>
    </div>
</template>

<script setup>
import { usePasswordI18n, usePasswordType } from '../hooks.js'

const passwordType = usePasswordType()

const { t, locale } = usePasswordI18n(),
    isEn = locale.value === LANGUAGE.enUS

const originalKeyboard = ref(false),
    passwordKeyboard = ref(false)

const formState = ref({
    original: '',
    password: '',
    repeat: '',
})

const disabled = useFormDisabled(formState, passwordType.financial ? 'repeat' : undefined)

const onValid = () => {
    const { original, password, repeat } = formState.value

    if (passwordType.account) {
        if (
            !REGULAR.NEW_PASSWORD.test(original) ||
            !REGULAR.NEW_PASSWORD.test(password) ||
            !REGULAR.NEW_PASSWORD.test(repeat)
        ) return showFailToast(t('auth.password_error'))

        if (password !== repeat) return showFailToast(t('auth.repeat_error'))
    }
    if (passwordType.financial) {
        if (
            !REGULAR.OTP.test(original) ||
            !REGULAR.OTP.test(password)
        ) return showFailToast(t('auth.password_error'))
    }

    if (original === password) return showFailToast(t('auth.new_error'))

    onSubmit()
}

const [ onSubmit, loading ] = useFetchLoading(async () => {
    const { original, password } = formState.value

    await api_post({
        url: API_PATH.UPDATE_PASSWORD,
        params: {
            password: btoa(original),
            newPassword: btoa(password),
            verifyType: OTP_VERIFY.ACCOUNT,
            type: passwordType.account ? 1 : 2,
        },
    })

    showSuccessToast(t('operation.successfully'))

    if (passwordType.account) logout()
})

defineOptions({ name: 'modify_password_by_original' })
</script>

<style scoped>
</style>

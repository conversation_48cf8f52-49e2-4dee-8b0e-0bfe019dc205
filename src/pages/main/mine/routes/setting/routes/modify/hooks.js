export const usePasswordI18n = () => {
    return useI18n({
        useScope: 'global',
        messages: {
            [LANGUAGE.zhCN]: {
                _original: '原密码',
                _new: '新密码',
                _repeat: '确认密码',
            },
            [LANGUAGE.zhHK]: {
                _original: '原密码',
                _new: '新密码',
                _repeat: '确认密码',
            },
            [LANGUAGE.enUS]: {
                _original: 'Original Password',
                _new: 'New Password',
                _repeat: 'Confirm Password',
            },
        },
    })
}

export const usePasswordType = () => {
    const { params: { type } } = useRoute()

    return {
        type,
        account: type === 'account',
        financial: type === 'financial',
    }
}

<template>
    <c-header :title="t('profile.avatar')" :disabled="loading"/>

    <div class="with-header-container flex flex-col">
        <div class="grid grid-cols-5">
            <div
                class="aspect-square relative"
                v-for="n in 30"
                :key="n"
                @click="checked = n"
            >
                <c-avatar
                    class="mx-auto"
                    data-aos="zoom-in"
                    :avatar="n"
                />

                <van-icon
                    v-show="n === +$profile.avatar"
                    name="checked"
                    data-aos="zoom-in"
                    class="absolute! right-1 top-0"
                />

                <van-icon
                    v-if="n === +checked"
                    name="checked"
                    data-aos="zoom-in"
                    class="absolute! right-1 top-0 text-primary"
                />
            </div>
        </div>

        <div class="mt-auto" data-aos="fade-up">
            <van-button
                block
                type="primary"
                :disabled="+checked === +$profile.avatar"
                @click="onChange"
            >
                {{ $t('operation.modify') }}
            </van-button>
        </div>
    </div>
</template>

<script setup>
import { useProfileStore } from '@/store/index.js'
import { useFetchLoading } from '@/hooks/index.js'

const { back } = useRouter(),
    profileStore = useProfileStore(),
    { dispatch_updateProfile } = profileStore,
    { $profile } = storeToRefs(profileStore)

const checked = ref($profile.value.avatar)

const [ onChange, loading ] = useFetchLoading(async () => {
    await dispatch_updateProfile({ avatar: checked.value })
    $profile.value.avatar = checked.value
    showSuccessToast(t('operation.successfully'))
    back()
})

const { t } = useI18n()

defineOptions({ name: 'avatar' })
</script>

<style scoped>

</style>

<template>
    <c-header :title="t('profile.title')"/>

    <div class="with-header-container">
        <van-cell-group>
            <van-cell
                data-aos="fade-left"
                :title="t('profile.avatar')"
                :to="{ name: 'avatar' }"
                is-link
            >
                <template #value>
                    <c-avatar
                        class="size-6 ml-auto"
                        :avatar="$profile.avatar"
                    />
                </template>
            </van-cell>

            <van-cell
                data-aos="fade-left"
                v-for="({ title, value, to }, i) in cell"
                :data-aos-delay="`${(i + 1) * 50}`"
                :title
                :value
                :to
                :is-link="!!to"
            />
        </van-cell-group>
    </div>
</template>

<script setup>
const { $profile } = storeToRefs(useProfileStore())

const cell = computed(() => {
    const { nickname, realName, email, mobile } = $profile.value

    return [
        {
            title: t('profile.nickname'),
            value: nickname,
            to: '/profile/update/nickname',
        },
        {
            title: t('profile.name'),
            value: realName,
        },
        {
            title: t('profile.email'),
            value: email,
        },
        {
            title: t('profile.mobile'),
            value: mobile,
        },
    ]
})

const { t } = useI18n()

defineOptions({ name: 'profile' })
</script>

<style scoped>
.van-cell-group {
    text-transform: none;
}
</style>

<template>
    <c-header :title="t('profile.update', [ t(`profile.${type}`) ])" :disabled="loading"/>

    <div class="with-header-container">
        <van-form required="auto" @submit="onSubmit">
            <van-cell-group data-aos="fade-left">
                <van-field
                    name="nickname"
                    clearable
                    show-word-limit
                    :label="t('profile.nickname')"
                    :maxlength="10"
                    :rules="[
                        { required: true }
                    ]"
                    v-model="formState.nickname"
                />
            </van-cell-group>

            <div
                data-aos="fade-left"
                data-aos-delay="50"
                class="mt-10"
            >
                <van-button
                    type="primary"
                    block
                    native-type="submit"
                    :loading
                    :disabled
                >
                    {{ t('form.submit') }}
                </van-button>
            </div>
        </van-form>
    </div>
</template>

<script setup>
import { useProfileStore } from '@/store/index.js'
import { useFetchLoading } from '@/hooks/index.js'
import { showSuccessToast } from 'vant'

const { params: { type } } = useRoute(),
    { back } = useRouter()

const profileStore = useProfileStore(),
    { dispatch_updateProfile } = profileStore,
    { $profile } = storeToRefs(profileStore)

const formState = ref({
    nickname: $profile.value.nickname,
})

const disabled = computed(() => {
    switch (type) {
        case 'nickname':
            return !formState.value.nickname || formState.value.nickname === $profile.value.nickname
    }
})

const [ onSubmit, loading ] = useFetchLoading(async (params) => {
    await dispatch_updateProfile(params)

    $profile.value = {
        ...$profile.value,
        ...params,
    }

    showSuccessToast(t('operation.successfully'))
    back()
})

const { t } = useI18n()

defineOptions({ name: 'profile-update' })
</script>

<style scoped>

</style>

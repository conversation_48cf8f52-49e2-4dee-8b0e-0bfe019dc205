<template>
    <c-header :title="detail.title"/>

    <div class="with-header-container">
        <div
            data-aos="fade-zoom-in"
            class="text-title indent-8"
            v-html="detail.content"
        />
    </div>
</template>

<script setup>
import _ from 'lodash'

const { $protocol } = storeToRefs(useProtocolStore())

const { params: { id } } = useRoute()

const detail = computed(() => _.find($protocol.value, { id: +id }) ?? {
    id: 0,
    title: '',
    content: '',
})

defineOptions({ name: 'about-details' })
</script>

<style scoped>

</style>

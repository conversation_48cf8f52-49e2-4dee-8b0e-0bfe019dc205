<template>
    <c-header :title="$t('header.about')"/>

    <div class="with-header-container text-title">
        <van-cell-group inset>
            <van-cell
                data-aos="fade-left"
                is-link
                v-for="(item, i) in $protocol"
                :data-aos-delay="`${i * 50}`"
                :key="item.id"
                :title="item.title"
                :to="`/about/details/${item.id}`"
            />
        </van-cell-group>

        <!--<div class="flex-center flex-col mb-10 mt-4" data-aos="fade-left">-->
        <!--    <div class="mt-2.5 mb-1">{{ $globalConfig.title?.[locale] ?? '' }}</div>-->

        <!--    <div class="text-xs normal-case">-->
        <!--        <span>v{{ __APP_VERSION__ }}</span>-->
        <!--    </div>-->
        <!--</div>-->

    </div>
</template>

<script setup>
// const { $globalConfig } = storeToRefs(useGlobalStore()),
//     { __APP_VERSION__ } = import.meta.env

const { $protocol } = storeToRefs(useProtocolStore())

// const { locale } = useI18n()

defineOptions({ name: 'about' })
</script>

<style scoped>
.van-cell-group--inset {
    margin: 0;
}
</style>

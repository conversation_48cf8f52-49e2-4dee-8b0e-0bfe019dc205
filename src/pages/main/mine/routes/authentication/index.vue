<template>
    <div class="authentication bg-contain bg-no-repeat h-full">
        <c-header
            class="text-white"
            transparent
            :disabled="loading"
        />

        <div class="with-header-container">
            <div class="text-3xl text-white" data-aos="fade-left">
                {{ $t('header.authentication') }}
            </div>

            <c-card class="mt-4">
                <c-select
                    data-aos="fade-left"
                    data-aos-delay="50"
                    :label="t('authentication.type')"
                    :placeholder="t('form.select_placeholder', [ t('authentication.type') ])"
                    :columns
                    disabled
                    required
                    v-model="formState.certificateType"
                />

                <c-input
                    data-aos="fade-left"
                    data-aos-delay="100"
                    :label="t('authentication.name')"
                    :placeholder="t('form.input_placeholder', [ t('authentication.name') ])"
                    :disabled
                    required
                    v-model="formState.realName"
                />

                <c-input
                    data-aos="fade-left"
                    data-aos-delay="150"
                    :maxlength="18"
                    :label="t('authentication.number')"
                    :placeholder="t('form.input_placeholder', [ t('authentication.number') ])"
                    :disabled
                    required
                    v-model="formState.idCard"
                />

                <c-controller
                    v-if="!disabled"
                    class="password mt-4"
                    required
                    :label="t('auth.password_financial')"
                >
                    <van-password-input
                        class="w-full"
                        :gutter="10"
                        :value="formState.withdrawPassword"
                        @focus="keyboard = true; "
                    />
                    <c-keyboard
                        :gutter="10"
                        v-model="formState.withdrawPassword"
                        v-model:visible="keyboard"
                    />
                </c-controller>
            </c-card>

            <div
                data-aos="fade-left"
                data-aos-delay="200"
                data-aos-anchor="#app"
                class="mt-10"
                v-if="!disabled"
            >
                <van-button
                    block
                    type="primary"
                    :loading
                    :disabled="submitDisabled || disabled"
                    @click="onValid"
                >
                    {{ $t('form.submit') }}
                </van-button>
            </div>
        </div>
    </div>
</template>

<script setup>
const { dispatch_refreshProfile } = useProfileStore()

const keyboard = ref(false)

const disabled = computed(() => !!res.value?.id)

const formState = ref({
    certificateType: 1,
    idCard: '',
    realName: '',
    withdrawPassword: '',
})

const { res } = useRequest({
    // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E4%BC%9A%E5%91%98%E5%AE%9E%E5%90%8D%E8%AE%A4%E8%AF%81/getAuthInfoUsingGET_1
    url: '/member/auth/info',
    initialValues: {
        id: 1,
    },
    onSuccess: res => {
        if (res.id) {
            const { certificateType, realName, idCard, certificateFront, certificateBack } = res

            formState.value = {
                certificateType,
                idCard,
                realName,
                certificateFront,
                certificateBack,
            }
        }
    },
})

// ******************
const submitDisabled = useFormDisabled(formState, [ 'certificateFront', 'certificateBack' ])

const columns = computed(() => [
    { text: t('authentication.id'), value: 1 },
])

const { back } = useRouter()
const [ onSubmit, loading ] = useFetchLoading(async (params) => {
    // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E4%BC%9A%E5%91%98%E5%AE%9E%E5%90%8D%E8%AE%A4%E8%AF%81/applyAuthUsingPOST_1
    await api_post({
        url: '/member/auth/apply',
        params
    })

    showSuccessToast(t('form.successfully'))

    dispatch_refreshProfile()
    back()
})

const onValid = () => {
    const {
        realName,
        idCard,
        withdrawPassword,
    } = formState.value

    if (!REGULAR.ID.test(idCard)) return showFailToast(t('authentication.number_error'))
    if (!REGULAR.REAL_NAME.test(realName)) return showFailToast(t('authentication.name_error'))
    if (!REGULAR.OTP.test(withdrawPassword)) return showFailToast(t('auth.password_error'))

    onSubmit({
        ...formState.value,
        withdrawPassword: btoa(withdrawPassword)
    })
}

const { t } = useI18n({
    useScope: 'global',
    messages: {
        [LANGUAGE.zhCN]: {
            authentication: {
                id: '身份证',
                passport: '护照',
                type: '证件类型',
                name: '证件姓名',
                name_error: '请输入正确的姓名格式',
                number: '证件号码',
                number_error: '请输入正确的证件号码格式',
            },
        },
        [LANGUAGE.zhHK]: {
            authentication: {
                id: '身份证',
                passport: '护照',
                type: '证件类型',
                name: '证件姓名',
                name_error: '请输入正确的姓名格式',
                number: '证件号码',
                number_error: '请输入正确的证件号码格式',
            },
        },
        [LANGUAGE.enUS]: {
            authentication: {
                id: 'ID',
                passport: 'Passport',
                type: 'Certificate Type',
                name: 'Certificate Name',
                name_error: 'Please enter the correct name format',
                number: 'Certificate Number',
                number_error: 'Please enter the correct Certificate number format',
            },
        },
    },
})

defineOptions({ name: 'authentication' })
</script>

<style scoped>
.authentication {
    background-image: url('./assets/bg.png');
}

.c-controller:not(:last-child) {
    margin-bottom: 20px;
}

:deep(.password .c-controller__main) {
    padding: 0;
    background-color: transparent;
    --van-password-input-background: var(--controller_bg);
}
</style>

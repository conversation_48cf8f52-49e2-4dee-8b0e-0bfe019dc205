<template>
    <c-controller :label required>
        <van-uploader
            class="w-full"
            style="aspect-ratio: 159/103"
            accept="image/*"
            :max-count="1"
            :after-read="onUpload"
            :disabled="disabled || loading"
            :deletable="!disabled"
            v-model="files"
        >
            <slot/>
        </van-uploader>
    </c-controller>
</template>

<script setup>
defineProps({
    label: {
        type: String,
        required: true,
    },
    disabled: Boolean,
})

const modelValue = defineModel({
    type: String,
    required: true,
})

const files = ref([])
if (modelValue.value) {
    files.value = [
        { url: modelValue.value },
    ]
}

const { onUpload, loading } = useUpload({
    onSuccess: url => {
        modelValue.value = url
    },
    onError: (e) => {
        files.value = []
    },
})

defineOptions({ name: 'AuthenticationUpload' })
</script>

<style scoped>
:deep(.van-uploader .van-uploader__preview) {
    margin: 0;
}

:deep(.van-uploader .van-uploader__wrapper),
:deep(.van-uploader .van-image),
:deep(.van-uploader .van-uploader__preview) {
    width: 100%;
    height: 100%;
}

:deep(.c-controller__main) {
    padding: 12px;
}
</style>

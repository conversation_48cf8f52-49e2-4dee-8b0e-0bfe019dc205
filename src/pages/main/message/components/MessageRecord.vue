<template>
    <c-record
        :finished
        :onRefresh
        :onLoadMore
        v-model:refresh-loading="refreshLoading"
        v-model:load-loading="loadLoading"
    >
        <c-card
            class="not-last:mb-2.5"
            v-for="({ id, title, content, status }) in list"
            :key="id"
            :title
        >
            <template #extra>
                <van-badge v-if="!status" dot/>
            </template>

            <div class="text-xs whitespace-pre-line">
                {{ content }}
            </div>
        </c-card>
    </c-record>
</template>

<script setup>
const { type } = defineProps({
    type: String,
    required: true,
})

const {
    list,
    refreshLoading,
    loadLoading,
    finished,
    onRefresh,
    onLoadMore,
} = usePagination({
    // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E6%B6%88%E6%81%AF%E7%AE%A1%E7%90%86/pageUsingGET_11
    url: '/message/page',
    params: {
        type,
    },
})

defineOptions({ name: 'MessageRecord' })
</script>

<style scoped>

</style>

<template>
    <c-header :title="$t(`stock.${$plateType}`)"/>

    <div class="with-header-container__noPadding">
        <c-table
            class="h-full"
            :finished
            :columns
            :data-source="list"
            :onRefresh
            :onLoadMore
            v-model:refresh-loading="refreshLoading"
            v-model:load-loading="loadLoading"
            @filter="onFilter"
            @row-click="dispatch_checkStock"
        />
    </div>
</template>

<script setup>
const { dispatch_checkStock } = useStockStore(),
    { $marketType, $plateType, $securityType } = storeToRefs(useQuotesStore())

const columns = useStockColumns()

const [
    {
        list,
        refreshLoading,
        loadLoading,
        finished,
        onRefresh,
        onFilter,
        onLoadMore
    }
] = useMarketStocks({
    params: {
        marketType: $marketType.value,
        securityType: $securityType.value,
        plate: STOCK_CONFIG[$marketType.value].plate.join(','),
        ...DEFAULT_STOCK_SORT,
    },
})

// 市场全部大盘
defineOptions({ name: 'stock-plate' })
</script>

<style scoped>

</style>

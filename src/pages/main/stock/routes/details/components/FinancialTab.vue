<template>
    <c-card
        class="rounded-none"
        :title="t('stock.financial.flow_distributed')"
        :extra="`${t('common.update_time')}：${res.updateTime ? utils_time(res.updateTime * 1000) : '--'}`"
    >
        <c-echarts style="height: 400px;" :option="pieOption"/>

        <div class="flex text-center text-xs" v-show="initial">
            <div class="flex-1">
                <span>
                    {{ t('stock.financial.flow_in') }}
                </span>
                <c-amount
                    class="ml-1 text-raise"
                    big-unit
                    :amount="flowData.in"
                />
            </div>
            <div class="flex-1">
                <span>
                    {{ t('stock.financial.flow_out') }}
                </span>
                <c-amount
                    class="ml-1 text-fall"
                    big-unit
                    :amount="flowData.out"
                />
            </div>
        </div>
    </c-card>

    <c-card
        class="rounded-none mt-4"
        :title="t('stock.financial.flow_trend')"
    >
        <c-echarts
            class="border border-border"
            style="height: 240px;"
            :option="barOption"
        />
    </c-card>
</template>

<script setup>
import _ from 'lodash'

import { utils_time } from '@/utils/index.js'

const { $currentStock } = storeToRefs(useStockStore())

const pieOption = ref({}),
    flowData = ref({
        in: 0,
        out: 0,
    })

const barOption = ref({})

const colors = (colors) => {
    let colorList = [
        '#C83232',
        '#DC4646',
        '#F06464',
        '#329632',
        '#46AA46',
        '#64C864',
    ]
    return colorList[colors.dataIndex]
}

const { t } = useI18n({
    useScope: 'global',
    messages: {
        [LANGUAGE.zhCN]: {
            stock: {
                financial: {
                    big: '大单',
                    medium: '中单',
                    small: '小单',
                    flow_distributed: '资金成交分布',
                    flow_trend: '资金成交流向',
                    flow_in: '流入',
                    flow_out: '流出',
                },
            },
        },
        [LANGUAGE.zhHK]: {
            stock: {
                financial: {
                    big: '大单',
                    medium: '中单',
                    small: '小单',
                    flow_distributed: '资金成交分布',
                    flow_trend: '资金成交流向',
                    flow_in: '流入',
                    flow_out: '流出',
                },
            },
        },
        [LANGUAGE.enUS]: {
            stock: {
                financial: {
                    big: 'Large',
                    medium: 'Medium',
                    small: 'Small',
                    flow_distributed: 'Capital Transaction Distribution',
                    flow_trend: 'Capital Flow Direction',
                    flow_in: 'Inflow',
                    flow_out: 'Outflow',
                },
            },
        },
    },
})

const categories = [
    t('stock.financial.big'),
    t('stock.financial.medium'),
    t('stock.financial.small'),
    t('stock.financial.big'),
    t('stock.financial.medium'),
    t('stock.financial.small'),
]

const { res, initial } = useRequest({
    // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E8%A1%8C%E6%83%85%E6%95%B0%E6%8D%AE/getDistAndFlowUsingGET_1
    url: '/market/distFlow',
    params: {
        symbol: $currentStock.value.symbol,
        market: $currentStock.value.market,
        securityType: $currentStock.value.securityType,
    },
    initialValues: {
        flow: [],
        symbol: '',
        market: '',
        updateTime: 0,
        dist: {
            in: {
                maxAmount: 0,
                maxNum: 0,
                midAmount: 0,
                midNum: 0,
                minAmount: 0,
                minNum: 0,
            },
            out: {
                maxAmount: 0,
                maxNum: 0,
                midAmount: 0,
                midNum: 0,
                minAmount: 0,
                minNum: 0,
            },
        },
    },
    onSuccess: res => {
        const {
            dist: { in: _in, out },
            flow,
        } = res

        const inData = _.values(_.pick(_in, [ 'maxAmount', 'midAmount', 'minAmount' ])),
            outData = _.values(_.pick(out, [ 'maxAmount', 'midAmount', 'minAmount' ]))

        flowData.value = {
            in: _.sum(inData),
            out: _.sum(outData),
        }

        const pieData = [
            ...inData,
            ...outData,
        ]

        pieOption.value = {
            grid: {
                top: 240,
                left: 20,
                right: 20,
                bottom: 30,
            },
            xAxis: {
                type: 'category',
                data: categories,
                axisTick: false,
                axisLabel: {
                    interval: 0,
                },
                axisLine: {
                    show: false,
                },
            },
            yAxis: {
                type: 'value',
                show: false,
            },
            series: [
                {
                    type: 'pie',
                    radius: [ 40, 70 ],
                    center: [ '50%', 100 ],
                    data: pieData,
                    itemStyle: {
                        color: colors,
                    },
                    label: {
                        color: 'inherit',
                        formatter: '{d}%',
                    },
                },
                {
                    type: 'bar',
                    data: pieData,
                    barWidth: 24,
                    barGap: 32,
                    itemStyle: {
                        color: colors,
                    },
                    label: {
                        show: true,
                        color: 'inherit',
                        position: 'top',
                        formatter: ({ value }) => utils_big_amount_unit(value),
                    },
                },
            ],
        }

        const time = _.map(flow, e => utils_time(e.date * 1000, TIME_FORMAT.Hm))

        barOption.value = {
            grid: {
                top: 20,
                left: 10,
                right: 10,
                bottom: 24,
            },
            xAxis: {
                type: 'category',
                data: time,
                axisTick: false,
                axisLine: {
                    show: false,
                },
                axisLabel: {
                    interval: (index) => !index || index === time.length - 1 || index === 120,
                    alignMinLabel: 'left',
                    alignMaxLabel: 'right',
                },
            },
            yAxis: {
                scale: true,
                splitLine: false,
                splitNumber: 3,
                axisLabel: {
                    inside: true,
                    formatter: val => utils_big_amount_unit(val),
                    verticalAlign: 'bottom',
                },
            },
            series: [
                {
                    data: _.map(flow, 'chg'),
                    type: 'line',
                    symbol: false,
                    smooth: true,
                    lineStyle: {
                        width: 1,
                    },
                },
            ],
        }
    },
})

defineOptions({ name: 'StockFinancialTab' })
</script>

<style scoped>

</style>

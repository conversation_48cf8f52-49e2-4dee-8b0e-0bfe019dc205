<template>
    <c-card :title="t('_title')" class="rounded-none">
        <c-description-group
            value-class="text-text"
            :items="description"
            :data-source="res"
        />
    </c-card>
</template>

<script setup>
const { $currentStock } = storeToRefs(useStockStore())

const { market, securityType, symbol } = $currentStock.value

const { t } = useI18n({
    useScope: 'global',
    messages: {
        [LANGUAGE.zhCN]: {
            _title: '基本概况',
            _chairman: '董事长',
            _registered_capital: '注册资本',
            _company_name: '公司名称',
            _company_intro: '公司简介',
            _company_legal_person: '公司法人',
            _secretary: '董秘',
            _company_website: '公司网址',
            _industry: '所属行业',
            _business: '主营业务',
            _establish_date: '成立日期',
            _registered_address: '注册地址',
            _staff: '公司员工',
            _office: '办公地址',
            _contact: '联系电话',
            _email: '电子邮件',
        },
        [LANGUAGE.zhHK]: {
            _title: '基本概况',
            _chairman: '董事長',
            _registered_capital: '註冊資本',
            _company_name: '公司名稱',
            _company_intro: '公司简介',
            _company_legal_person: '公司法人',
            _secretary: '董秘',
            _company_website: '公司網址',
            _industry: '所屬行業',
            _business: '主營業務',
            _establish_date: '成立日期',
            _registered_address: '註冊地址',
            _staff: '公司員工',
            _office: '辦公地址',
            _contact: '聯繫電話',
            _email: '電子郵件',
        },
        [LANGUAGE.enUS]: {
            _title: 'Basic overview',
            _chairman: 'Chairman',
            _registered_capital: 'Registered Capital',
            _company_name: 'Company Name',
            _company_intro: 'Company Introduction',
            _company_legal_person: 'Company Legal Person',
            _secretary: 'Secretary',
            _company_website: 'Company Website',
            _industry: 'Industry',
            _business: 'Main Business',
            _establish_date: 'Establishment Date',
            _registered_address: 'Registered Address',
            _staff: 'Company Staff',
            _office: 'Office Address',
            _contact: 'Contact Number',
            _email: 'Email',
        },
    },
})

const description = [
    { label: t('stock.symbol'), value: 'symbol' },
    { label: t('_chairman'), value: 'chairman' },
    { label: t('_registered_capital'), value: 'register_capital' },
    { label: t('_company_name'), value: 'company_name' },
    { label: t('_company_intro'), value: 'company_profile' },
    { label: t('_company_legal_person'), value: 'legal_person' },
    { label: t('_secretary'), value: 'chairman_secretary' },
    {
        label: t('_company_website'),
        value: 'company_website',
        // render: val => {
        //     return h(
        //         'div',
        //         {
        //             class: 'text-link',
        //             onClick: () => {
        //                 utils_link(val)
        //             },
        //         },
        //         val,
        //     )
        // },
    },
    { label: t('_industry'), value: 'industry' },
    { label: t('_business'), value: 'main_business' },
    { label: t('_establish_date'), value: 'establish_date' },
    { label: t('_registered_address'), value: 'registered_address' },
    { label: t('_staff'), value: 'staff_number' },
    { label: t('_office'), value: 'office_address' },
    {
        label: t('_contact'),
        value: 'contact_telephone',
        render: val => {
            return h(
                'div',
                {
                    class: 'text-link',
                    onClick: () => {
                        utils_link(`tel:${val}`)
                    },
                },
                val,
            )
        },
    },
    {
        label: t('_email'),
        value: 'email',
        render: val => {
            return h(
                'div',
                {
                    class: 'text-link',
                    onClick: () => {
                        utils_link(`mailto:${val}`)
                    },
                },
                val,
            )
        },
    },
]

const { res } = useRequest({
    // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E8%A1%8C%E6%83%85%E6%95%B0%E6%8D%AE/getCompanyInfoUsingGET_1
    url: '/market/companyInfo',
    params: {
        market,
        securityType,
        symbol,
    },
    initialValues: {
        symbol: '',
        legal_person: '',
        registered_address: '',
        contact_telephone: '',
        concept: '',
        company_website: '',
        industry: '',
        chairman: '',
        staff_number: '',
        market: '',
        chairman_secretary: '',
        office_address: '',
        company_profile: '',
        company_name: '',
        register_capital: '',
        general_manager: '',
        manage_number: '',
        main_business: '',
        region: '',
        establish_date: '',
        email: '',
    },
})

defineOptions({ name: 'StockIntroductionTab' })
</script>

<style scoped>

</style>

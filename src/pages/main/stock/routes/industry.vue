<template>
    <c-header :title="t('stock.industry')"/>

    <div class="with-header-container__noPadding">
        <c-table
            class="h-full"
            :columns
            :data-source="list"
            :onRefresh
            :onLoadMore
            v-model:refresh-loading="refreshLoading"
            v-model:load-loading="loadLoading"
            @filter="onFilter"
            @row-click="dispatch_checkIndustryType"
        />
    </div>
</template>

<script setup>
import Amount from '@/components/Amount/index.vue'
import StockBaseInfo from '@/pages/main/stock/components/StockBaseInfo.vue'

const quotesStore = useQuotesStore(),
    { dispatch_checkIndustryType } = quotesStore,
    { $marketType } = storeToRefs(quotesStore)

const [
    {
        list,
        refreshLoading,
        loadLoading,
        onRefresh,
        onFilter,
        onLoadMore,
    },
] = useIndustry({
    params: {
        markets: STOCK_CONFIG[$marketType.value].markets,
        securityType: 1,
    },
})

const { t } = useI18n({
    useScope: 'global',
    messages: {
        [LANGUAGE.zhCN]: {
            _leading: '领涨股',
        },
        [LANGUAGE.zhHK]: {
            _leading: '领涨股',
        },
        [LANGUAGE.enUS]: {
            _leading: 'Leading',
        },
    },
})

const columns = [
    {
        title: t('stock.name'),
        dataIndex: 'name',
    },
    {
        title: t('stock.raise_fall'),
        dataIndex: 'gain',
        align: 'center',
        sortable: true,
        render: (val) => h(
            Amount,
            {
                amount: val * 100,
                colorful: true,
                percent: true,
                symbol: true,
            },
        ),
    },
    {
        title: t('_leading'),
        dataIndex: 'leadUp',
        align: 'right',
        render: ({ name, symbol, market }) => h(
            StockBaseInfo,
            {
                class: 'ml-auto w-full text-right',
                name,
                symbol,
                market,
            },
        ),
    },
]

// 全部行业板块
defineOptions({ name: 'stock-industry' })
</script>

<style scoped>
:deep(.marketBlock) {
    margin-left: auto;
}
</style>

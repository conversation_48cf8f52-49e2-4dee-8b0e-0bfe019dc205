<template>
    <c-header :title="t('_title')"/>

    <div class="with-header-container__noPadding px-2.5 pb-2.5">
        <van-tabs
            data-aos="fade-left"
            data-aos-delay="50"
            v-model:active="active"
            :before-change="onChangeTab"
        >
            <van-tab
                v-for="(_, symbol) in STOCK_CONFIG"
                :key="symbol"
                :title="$t(`stock.${symbol}`)"
                :name="symbol"
                :disabled="initial && refreshLoading"
            />
        </van-tabs>

        <div class="tab-container pt-2.5">
            <c-record
                :finished
                :onRefresh
                :onLoadMore
                v-model:refresh-loading="refreshLoading"
                v-model:load-loading="loadLoading"
            >
                <c-card
                    class="not-last:mb-2.5 px-4"
                    no-padding
                    v-for="({ id, market, symbol, securityType, name, targetUpPrice, targetDownPrice, targetUpGain, targetDownGain }, i) in list"
                    :data-aos-delay="i * 50"
                    :key="id"
                >
                    <template #title>
                        <StockBaseInfo
                            class="border-b border-border h-10"
                            inline
                            :symbol
                            :market
                            :name
                        />
                    </template>

                    <template #extra>
                        <div class="flex-middle gap-2 text-xs">
                            <router-link
                                class="text-link"
                                :to="{ path: '/stock/warning/add', query: { market, securityType, symbol } }"
                                replace
                            >
                                {{ t('operation.modify') }}
                            </router-link>

                            <span class="text-red" @click="onDelete(id)">
                                {{ t('operation.delete') }}
                            </span>
                        </div>
                    </template>

                    <c-collapse
                        :fold="0"
                        :unfold="56"
                        :tip="false"
                    >
                        <c-description-group
                            :columns="2"
                            :items
                            :data-source="{
                                targetUpPrice,
                                targetDownPrice,
                                targetUpGain,
                                targetDownGain
                            }"
                            :value-format
                        />
                    </c-collapse>
                </c-card>
            </c-record>
        </div>
    </div>
</template>

<script setup>
import { STOCK_CONFIG } from '@/config/index.js'
import { useWarningI18n } from './hooks.js'
import StockBaseInfo from '@/pages/main/stock/components/StockBaseInfo.vue'

const active = ref(STOCK_CONFIG.CN.symbol)

const { t } = useWarningI18n()

const {
    list,
    initial,
    finished,
    refreshLoading,
    loadLoading,
    run,
    onRefresh,
    onLoadMore,
} = usePagination({
    // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E8%AF%81%E5%88%B8%E6%8E%A7%E5%88%B6%E5%99%A8/getSymbolWarnListUsingGET_1
    url: '/symbol/warn/list',
    params: {
        market: active.value,
    },
})

const onChangeTab = async (market) => {
    await run({
        market,
    })
    active.value = market
    return true
}

const valueFormat = (val, { value }) => {
    if (!val) return '--'

    const amount = utils_currency(val)
    return value.includes('Gain') ? `${amount}%` : amount
}

const items = [
    {
        label: t('_price_raise'),
        value: 'targetUpPrice',
        valueClass: 'text-raise',
    },
    {
        label: t('_price_fall'),
        value: 'targetDownPrice',
        valueClass: 'text-fall!',
    },
    {
        label: t('_percent_raise'),
        value: 'targetUpGain',
        valueClass: 'text-raise',
    },
    {
        label: t('_price_fall'),
        value: 'targetDownGain',
        valueClass: 'text-fall!',
    },
]

const { onDeleteConfirm } = useDeleteConfirm()

const onDelete = async (id) => {
    await onDeleteConfirm()
    await api_delete({
        // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E8%AF%81%E5%88%B8%E6%8E%A7%E5%88%B6%E5%99%A8/deleteSymbolWarnUsingDELETE_1
        url: '/symbol/warn/delete',
        params: {
            id,
        },
    })
    showSuccessToast(t('operation.successfully'))
    onRefresh()
}

defineOptions({ name: 'stock-warning' })
</script>

<style scoped>

</style>

<template>
    <Transaction class="p-2.5">
        <!-- 股票基础信息 -->
        <div class="flex-between">
            <div>
                <div class="text-title">
                    {{ $stock.name }} {{ $stock.symbol }}
                </div>

                <StockPrice/>
            </div>

            <div class="text-link text-sm" @click="$router.replace({ name: 'stock-details', query })">
                {{ $t('common.details') }}
            </div>
        </div>
        <!-- 股票基础信息 -->

        <!-- 股票交易量 -->
        <TransferVolume v-if="+type === 1" class="my-2.5"/>
        <!-- 股票交易量 -->
    </Transaction>
</template>

<script setup>
import TransferVolume from '../../components/TransferVolume.vue'
import Transaction from './components/Transaction.vue'
import StockPrice from '../../components/StockPrice.vue'

const { params: { type } } = useRoute()

const { $stock } = storeToRefs(useStockStore())

defineOptions({ name: 'stock-transaction' })
</script>

<style scoped>
</style>

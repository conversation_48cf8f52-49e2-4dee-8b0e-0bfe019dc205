<template>
    <c-header :title="$plateParams.title"/>

    <div class="with-header-container__noPadding">
        <c-table
            class="h-full"
            :finished
            :data-source="list"
            :columns
            :onRefresh
            :onLoadMore
            v-model:refresh-loading="refreshLoading"
            v-model:load-loading="loadLoading"
            @filter="onFilter"
            @row-click="dispatch_checkStock"
        />
    </div>
</template>

<script setup>
const { dispatch_checkStock } = useStockStore(),
    { $plateParams } = storeToRefs(useQuotesStore())

const columns = useStockColumns()

const { list, refreshLoading, loadLoading, finished, onRefresh, onFilter, onLoadMore } = usePagination({
    // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E8%A1%8C%E6%83%85%E6%95%B0%E6%8D%AE/plateInfoUsingGET_1
    url: '/market/plateInfo',
    params: $plateParams.value,
}, {
    responseKeys: {
        list: 'list',
        total: 'totalNum',
    },
})

// 根据板块类型查看全部股票
defineOptions({ name: 'stock-industry-type' })
</script>

<style scoped>

</style>

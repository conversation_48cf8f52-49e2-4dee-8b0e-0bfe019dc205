export const useTransferRenderConfig = (type) => {
    let itemBg = '',
        firstItemBg = '',
        serialBg = '',
        textColor = ''

    if (type === 'buy') {
        firstItemBg = 'bg-raise/30'
        itemBg = 'bg-raise/10'
        serialBg = 'bg-raise'
        textColor = 'text-raise'
    } else {
        firstItemBg = 'bg-fall/30'
        itemBg = 'bg-fall/10'
        serialBg = 'bg-fall'
        textColor = 'text-fall'
    }

    return {
        firstItemBg,
        itemBg,
        serialBg,
        textColor,
    }
}

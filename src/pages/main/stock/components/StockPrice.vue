<template>
    <div :class="$raise_fall.color">
        <!-- 最新价 -->
        <c-amount
            :precision="3"
            :currency="$stock.currency"
            :amount="$stock.latestPrice"
            :animate="false"
        />
        <!-- 最新价 -->

        <van-icon
            v-if="$stock.gain"
            name="play"
            class="ml-2"
            :size="12"
            :class="[ $raise_fall.raise ? '-rotate-90' : 'rotate-90' ]"
        />

        <div class="text-xs">
            <c-amount
                symbol
                :precision="3"
                :amount="$stock.chg"
                :animate="false"
            />
            <c-amount
                symbol
                percent
                :precision="3"
                :amount="$stock.gain * 100"
                :animate="false"
                class="ml-2"
            />
        </div>
    </div>
</template>

<script setup>
const { $stock, $raise_fall } = storeToRefs(useStockStore())

defineOptions({ name: 'StockPrice' })
</script>

<style scoped>

</style>

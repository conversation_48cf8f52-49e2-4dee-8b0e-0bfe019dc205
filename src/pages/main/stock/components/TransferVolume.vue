<template>
    <div v-if="$volume.bid.length && $volume.ask.length">
        <div class="text-sm mb-2.5">
            <div class="flex-middle">
                <span class="text-fall flex-1">{{ t('_sell') }}</span>

                <div class="flex-between flex-1">
                    <span class="text-raise">{{ t('_buy') }}</span>

                    <van-popover
                        v-if="$currentStock.market !== STOCK_CONFIG.US.symbol"
                        v-model:show="countPopover"
                        placement="left-start"
                    >
                        <div class="bg-bg p-2.5 flex flex-col gap-2.5">
                            <div
                                class="block"
                                v-for="n in counts"
                                :key="n"
                                @click="count = n; countPopover = false;"
                            >
                                {{ n }}
                            </div>
                        </div>

                        <template #reference>
                            <div class="block">
                                {{ count }}
                            </div>
                        </template>
                    </van-popover>
                </div>
            </div>

            <div class="flex-middle gap-2">
                <c-amount
                    :amount="100 - buyPercent"
                    color="text-fall"
                    percent
                />

                <div class="flex-1 flex w-full h-2.5 rounded-full overflow-hidden">
                    <div
                        class="relative h-full rounded-tl-full rounded-bl-full flex-1"
                        :class="{ 'sell bg-fall': buyPercent }"
                    />
                    <div
                        class="h-full rounded-tr-full rounded-br-full"
                        :class="{ 'bg-raise': buyPercent }"
                        :style="{ width: `${buyPercent}%` }"
                    />
                </div>

                <c-amount
                    :amount="buyPercent"
                    color="text-raise"
                    percent
                />
            </div>
        </div>

        <c-collapse
            :tip="false"
            :arrow="false"
            :fold="count * 24"
            :unfold="count * 24"
            disabled
        >
            <div class="grid grid-cols-2">
                <Render type="sell"/>
                <Render type="buy"/>
            </div>
        </c-collapse>
    </div>
</template>

<script setup>
import currencyJs from 'currency.js'

import { STOCK_CONFIG } from '@/config'
import Amount from '@/components/Amount/index.vue'
import TransferItem from './TransferItem.vue'
import { useTransferRenderConfig } from '@/pages/main/stock/components/hooks.js'

const { $currentStock, $volume } = storeToRefs(useStockStore())

const buyPercent = computed(() => {
    const _buy = $volume.value.bid[0]?.vol ?? 0,
        _sell = $volume.value.ask[0]?.vol ?? 0

    return currencyJs(_buy).divide(_buy + _sell).multiply(100)
})

const counts = [ 1, 5 ],
    count = ref(1),
    countPopover = ref(false)

const Render = ({ type }) => {
    const { itemBg, firstItemBg, serialBg, textColor } = useTransferRenderConfig(type)

    const data = type === 'buy' ? $volume.value.bid : $volume.value.ask

    return h(
        'div',
        data.map((e, i) => {
            const { depthNo, price, vol, no } = e

            return h(
                TransferItem,
                {
                    class: [ textColor, i ? itemBg : firstItemBg ],
                    serialBg,
                    serial: depthNo,
                    price: +price,
                },
                () => h(
                    Amount,
                    {
                        class: 'flex-1 justify-end text-title',
                        amount: vol / 100,
                        bigUnit: true,
                        suffix: `(${no || '-'})`,
                        precision: 0,
                    },
                ),
            )
        }),
    )
}

const { t } = useI18n({
    useScope: 'local',
    messages: {
        [LANGUAGE.zhCN]: {
            _buy: '买盘',
            _sell: '卖盘',
        },
        [LANGUAGE.zhHK]: {
            _buy: '买盘',
            _sell: '卖盘',
        },
        [LANGUAGE.enUS]: {
            _buy: 'Bid',
            _sell: 'Ask',
        },
    },
})

// 股票交易百分比和体量
defineOptions({ name: 'TransferVolume' })
</script>

<style scoped>
.sell:after {
    content: "";
    position: absolute;
    right: 0;
    width: 6px;
    height: 100%;
    background-color: #fff;
    transform: translate(50%) skew(40deg);
}

.block {
    color: var(--title);
    font-size: 12px;
    border: 1px solid currentColor;
    width: 20px;
    height: 20px;
    line-height: 18px;
    text-align: center;
    border-radius: 6px;
}
</style>

<template>
    <c-header class="font-semibold" :title="$activityDetails.title"/>

    <div class="with-header-container">
        <img
            data-aos="fade-left"
            class="w-full h-[126px] object-center mb-5"
            :src="$activityDetails.imageUrl"
            :alt="$activityDetails.title"
        >

        <div
            data-aos="fade-left"
            data-aos-delay="100"
            v-html="$activityDetails.content"
        />
    </div>
</template>

<script setup>
import { $activityDetails } from './store.js'

defineOptions({ name: 'activity-details' })
</script>

<style scoped>

</style>

<template>
    <div class="with-header-container">
        <van-tabs
            class="h-full"
            shrink
            v-model:active="$activityActiveTab"
        >
            <van-tab
                :title="t('header.news')"
                name="news"
                class="pt-2.5"
            >
                <NewsList/>
            </van-tab>

            <van-tab
                :title="t('header.activity')"
                name="activity"
                class="pt-2.5"
            >
                <NoticeList :type="NOTICE_TYPE.ACTIVITY"/>
            </van-tab>
        </van-tabs>
    </div>
</template>

<script setup>
import { $activityActiveTab, NOTICE_TYPE } from './store.js'
import NewsList from '@/pages/main/news/components/NewsList.vue'
import NoticeList from '@/pages/main/activity/components/NoticeList.vue'

const { t } = useI18n()

defineOptions({ name: 'activity' })
</script>

<style scoped>
</style>

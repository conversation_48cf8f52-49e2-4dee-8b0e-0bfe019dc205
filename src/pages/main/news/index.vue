<template>
    <c-header :title="t('_title')"/>

    <div class="with-header-container text-sm">
        <div class="text-title text-xl">
            {{ $newsDetails.title }}
        </div>

        <div class="flex-between gap-2.5 text-text text-xs py-3 mb-2.5 border-b border-border">
            <span>{{ $newsDetails.comefrom }}</span>
            <span>{{ $newsDetails.publishTime }}</span>
        </div>

        <div>
            {{ $newsDetails.digest }}
        </div>

        <img
            class="w-full my-4"
            :src="$newsDetails.url"
            alt="cover"
        >

        <div class="text-paragraph whitespace-pre-line">
            {{ $newsDetails.content }}
        </div>
    </div>
</template>

<script setup>
import { $newsDetails } from './store.js'

const { t } = useI18n({
    messages: {
        [LANGUAGE.zhCN]: {
            _title: '新闻详情',
        },
        [LANGUAGE.zhHK]: {
            _title: '新闻详情',
        },
        [LANGUAGE.enUS]: {
            _title: 'News Details',
        },
    },
})

defineOptions({ name: 'news' })
</script>

<style scoped>

</style>

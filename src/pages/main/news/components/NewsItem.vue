<template>
    <div class="flex-middle gap-2.5 py-2.5">
        <img
            v-if="cover"
            :src="cover"
            alt="news-cover"
            class="w-[100px] h-16 rounded"
        >

        <div class="flex-1">
            <div class="van-multi-ellipsis--l2 text-title text-sm">{{ title }}</div>

            <div class="flex-between text-xs mt-2">
                <span>{{ author }}</span>
                <span>{{ time }}</span>
            </div>
        </div>
    </div>
</template>

<script setup>
defineProps({
    cover: String,
    title: {
        type: String,
        required: true,
    },
    author: {
        type: String,
        required: true,
    },
    time: {
        type: String,
        required: true,
    },
})

defineOptions({ name: 'NewsItem' })
</script>

<style scoped>

</style>

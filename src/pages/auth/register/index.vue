<template>
    <AuthPopup class="pb-6">
        <div class="text-primary mb-8 text-lg font-semibold">
            {{ t('_register_welcome') }}
        </div>

        <van-form @submit="onValid">
            <c-input
                required
                inputmode="numeric"
                :maxlength="11"
                :placeholder="t('form.input_placeholder', [ t('profile.mobile') ])"
                v-model="formState.mobile"
            >
                <template #prefix>
                    <c-icon prefix="auth" name="mobile"/>
                </template>
            </c-input>

            <c-input
                class="flex-1"
                inputmode="numeric"
                :maxlength="6"
                :placeholder="t('form.input_placeholder', [ t('auth.otp') ])"
                v-model="formState.smsCode"
            >
                <template #prefix>
                    <c-icon prefix="auth" name="captcha"/>
                </template>

                <template #addOnAfter>
                    <van-button
                        type="primary"
                        :loading="otpLoading"
                        :disabled="!formState.mobile || otpDisabled"
                        @click="onSendOtp(formState.mobile)"
                    >
                        {{ otpText }}
                    </van-button>
                </template>
            </c-input>

            <c-input
                autocomplete="new-password"
                type="password"
                required
                :maxlength="16"
                :placeholder="t('auth.password_regex')"
                v-model="formState.password"
            >
                <template #prefix>
                    <c-icon prefix="auth" name="lock"/>
                </template>
            </c-input>

            <c-input
                type="password"
                required
                :maxlength="16"
                :placeholder="t('auth.repeat')"
                v-model="formState.repeat"
            >
                <template #prefix>
                    <c-icon prefix="auth" name="lock"/>
                </template>
            </c-input>

            <c-input
                :placeholder="t('form.input_placeholder', [ t('profile.referrer') ])"
                v-model="formState.inviteCode"
            >
                <template #prefix>
                    <c-icon prefix="auth" name="email"/>
                </template>
            </c-input>

            <div class="mt-8">
                <van-button
                    type="primary"
                    native-type="submit"
                    block
                    round
                    :loading
                    :disabled
                >
                    {{ t('_register') }}
                </van-button>
            </div>
        </van-form>

        <div class="text-center mt-5 text-xs text-text">
            {{ t('_has') }}
            <router-link
                class="text-link underline"
                :to="{ name: 'login' }"
                replace
            >
                {{ t('_login') }}
            </router-link>
        </div>
    </AuthPopup>
</template>

<script setup>
import AuthPopup from '@/layout/auth/components/AuthPopup.vue'

const { replace } = useRouter()

const { $inviteCode } = storeToRefs(useGlobalStore())

const { otpText, otpLoading, otpDisabled, onSendOtp } = useOtp({ type: OTP_TYPES.REGISTER })

const formState = ref({
    mobile: '',
    smsCode: '',
    password: '',
    repeat: '',
    inviteCode: $inviteCode.value,
})

const disabled = useFormDisabled(formState, [ 'inviteCode' ])

const onValid = () => {
    const { mobile, password, repeat, smsCode } = formState.value

    if (!REGULAR.MOBILE.test(mobile)) return showFailToast(t('auth.account_error'))

    if (!REGULAR.NEW_PASSWORD.test(password)) return showFailToast(t('auth.password_error'))
    if (!REGULAR.NEW_PASSWORD.test(repeat)) return showFailToast(t('auth.password_error'))

    if (password !== repeat) return showFailToast(t('auth.repeat_error'))

    if (!REGULAR.OTP.test(smsCode)) return showFailToast(t('auth.otp_error'))

    onSubmit()
}

const [ onSubmit, loading ] = useFetchLoading(async () => {
    const { mobile, password, smsCode, inviteCode } = formState.value

    await api_post({
        // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E4%BC%9A%E5%91%98%E7%94%A8%E6%88%B7%E6%9D%83%E9%99%90/registerUsingPOST_1
        url: '/auth/register',
        params: {
            mobile,
            inviteCode,
            password: btoa(password),
            smsCode: smsCode,
        },
    })

    localStorage.setItem('loginInfo', JSON.stringify({ mobile, password, smsCode: '' }))

    showSuccessToast(t('_successfully'))
    replace({ name: 'login' })
})

const { t } = useI18n({
    useScope: 'global',
    messages: {
        [LANGUAGE.zhCN]: {
            _register: '立即注册',
            _register_welcome: '欢迎注册',
            _successfully: '注册成功',
            _has: '已有账号？',
            _login: '立即登录',
        },
        [LANGUAGE.zhHK]: {
            _register: '立即注册',
            _register_welcome: '欢迎注册',
            _successfully: '注册成功',
            _has: '已有账号？',
            _login: '立即登录',
        },
        [LANGUAGE.enUS]: {
            _register: 'Register',
            _register_welcome: 'Welcome to Register',
            _successfully: 'Register Successfully',
            _has: 'Had an account?',
            _login: 'To Login',
        },
    },
})

defineOptions({ name: 'register' })
</script>

<style scoped>
.c-controller:not(:last-child) {
    margin-bottom: 20px;
}
</style>

<template>
    <AuthPopup>
        <van-form @submit="onValid">
            <van-tabs
                shrink
                v-model:active="loginMode"
            >
                <!-- 密码登录 -->
                <van-tab
                    :title="t('_account_login')"
                    :name="OTP_VERIFY.ACCOUNT"
                >
                    <div class="my-5">
                        <mobileFormItem/>

                        <c-input
                            type="password"
                            required
                            :maxlength="16"
                            :placeholder="t('form.input_placeholder', [ t('auth.password_account') ])"
                            v-model="formState.password"
                        >
                            <template #prefix>
                                <c-icon prefix="auth" name="lock"/>
                            </template>
                        </c-input>
                    </div>
                </van-tab>
                <!-- 密码登录 -->

                <!-- 短信验证码登录 -->
                <van-tab :title="t('_mobile_login')" :name="OTP_VERIFY.MOBILE">
                    <div class="my-5">
                        <mobileFormItem/>

                        <c-input
                            class="flex-1"
                            inputmode="numeric"
                            required
                            :maxlength="6"
                            :placeholder="t('form.input_placeholder', [ t('auth.otp') ])"
                            v-model="formState.smsCode"
                        >
                            <template #prefix>
                                <c-icon prefix="auth" name="captcha"/>
                            </template>

                            <template #addOnAfter>
                                <van-button
                                    type="primary"
                                    :loading="otpLoading"
                                    :disabled="!formState.mobile || otpDisabled"
                                    @click="onSendOtp(formState.mobile)"
                                >
                                    {{ otpText }}
                                </van-button>
                            </template>
                        </c-input>
                    </div>
                </van-tab>
                <!-- 短信验证码登录 -->
            </van-tabs>
            <div>
                <!-- 记住密码 -->
                <div class="flex-middle mb-5 text-sm">
                    <van-checkbox
                        v-if="isPasswordMode"
                        shape="square"
                        icon-size="14"
                        v-model="remember"
                    >
                        {{ t('_remember') }}
                    </van-checkbox>

                    <router-link
                        class="ml-auto text-link"
                        :to="{ name: 'forget' }"
                        replace
                    >
                        {{ t('auth.forget') }}?
                    </router-link>
                </div>
                <!-- 记住密码 -->
                <!-- 提交按钮 -->
                <van-button
                    type="primary"
                    native-type="submit"
                    block
                    round
                    :loading
                    :disabled="disabled || !protocol"
                >
                    {{ t('auth.login') }}
                </van-button>
                <!-- 提交按钮 -->

                <!-- 协议 -->
                <div
                    data-aos="fade-left"
                    data-aos-delay="150"
                    data-aos-anchor="#app"
                    class="text-xs mt-3 flex-middle"
                >
                    <van-checkbox
                        class="mr-2"
                        :icon-size="14"
                        v-model="protocol"
                    />

                    <i18n-t scope="global" keypath="_protocol">
                        <span class="text-link" @click="onShowServiceProtocol">
                            {{ t('_protocol_service') }}
                        </span>
                        <span class="text-link" @click="onShowPrivacyProtocol">
                            {{ t('_protocol_privacy') }}
                        </span>
                    </i18n-t>
                </div>
                <!-- 协议 -->

                <!-- 跳转注册 -->
                <i18n-t
                    class="text-center mt-5 text-text text-xs"
                    tag="div"
                    keypath="_register_tip"
                    scope="global"
                >
                    <router-link
                        replace
                        to="/auth/register"
                    >
                        <span class="ml-1 text-link underline">{{ t('_register_now') }}</span>
                    </router-link>
                </i18n-t>
            </div>
        </van-form>
    </AuthPopup>

    <ServiceProtocol/>
    <PrivacyProtocol/>
</template>

<script setup>
import { OTP_VERIFY } from '@/hooks'
import socket from '@/socket.js'
import AuthPopup from '@/layout/auth/components/AuthPopup.vue'
import Input from '@/components/Controller/Input.vue'
import Icon from '@/components/Icon/index.vue'
import _ from 'lodash'

const { replace } = useRouter(),
    { dispatch_refreshProfile } = useProfileStore(),
    { dispatch_refreshAccount } = useAccountStore(),
    { dispatch_refreshUnreadCount } = useMessageStore()

const { otpText, otpLoading, otpDisabled, onSendOtp } = useOtp({ type: OTP_TYPES.LOGIN })

const loginMode = ref(OTP_VERIFY.ACCOUNT),
    isPasswordMode = computed(() => loginMode.value === OTP_VERIFY.ACCOUNT),
    remember = ref(true),
    protocol = useSessionStorage('loginProtocol', true)

const { onShowProtocol: onShowServiceProtocol, ProtocolPopup: ServiceProtocol } = useProtocolPopup(2),
    { onShowProtocol: onShowPrivacyProtocol, ProtocolPopup: PrivacyProtocol } = useProtocolPopup(3)

const formState = useLocalStorage('loginInfo', {
    mobile: '',
    password: '',
    smsCode: '',
})

const disabled = computed(() => {
    const { mobile, password, smsCode } = formState.value

    return !mobile || !(isPasswordMode.value ? password : smsCode)
})

const mobileFormItem = () => h(
    Input,
    {
        required: true,
        maxlength: 11,
        placeholder: t('form.input_placeholder', [ t('profile.mobile') ]),
        inputmode: 'numeric',
        modelValue: formState.value.mobile,
        'onUpdate:modelValue': val => {
            formState.value.mobile = val
        },
    },
    {
        prefix: () => h(
            Icon,
            {
                prefix: 'auth',
                name: 'mobile',
            },
        ),
    },
)

// 获取是否开启网易验证开关
const { res: isEnableAuth, onRefresh: onRefreshAuth } = useRequest({
    // http://111.119.201.212:9002/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E7%BD%91%E6%98%93%E6%98%93%E7%9B%BE/getEnableUsingGET_1
    url: '/yidun/getEnable',
    initialValues: false,
})

const { onAwaitCaptcha, validate } = useCaptcha()

const onValid = () => {
    const { mobile, password, smsCode } = formState.value
    if (!REGULAR.MOBILE.test(mobile)) return showFailToast(t('auth.account_error'))

    if (isPasswordMode.value && !REGULAR.NEW_PASSWORD.test(password)) return showFailToast(t('auth.password_error'))

    if (!isPasswordMode.value && !REGULAR.OTP.test(smsCode)) return showFailToast(t('auth.otp_error'))

    onSubmit()
}

const [ onSubmit, loading ] = useFetchLoading(async () => {
    // 开启网易验证
    if (isEnableAuth.value) await onAwaitCaptcha()

    console.log('onClose')
    const els = document.querySelectorAll('.yidun_popup--light.yidun_popup.yidun_popup--size-small.yidun_popup--append')
    console.log(els, 'els')
    if (els?.length) {
        els?.forEach(el => {
            el.style.display = 'none'
        })
    }
    
    const params = {
        mobile: formState.value.mobile,
        password: btoa(formState.value.password),
        smsCode: formState.value.smsCode,
        verifyType: loginMode.value,
        validate: isEnableAuth.value ? validate.value : undefined,
    }

    try {
        await api_post({
            // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E4%BC%9A%E5%91%98%E7%94%A8%E6%88%B7%E6%9D%83%E9%99%90/loginUsingPOST_1
            url: '/auth/login',
            params,
        })

        if (!remember.value) {
            formState.value = {
                mobile: '',
                password: '',
                smsCode: '',
            }
        } else {
            formState.value.smsCode = ''
        }

        showSuccessToast(t('_successfully'))

        socket.emit({
            type: 'auth',
            data: $token.value,
        })

        await Promise.all([
            replace('/'),
            dispatch_refreshProfile(),
            dispatch_refreshAccount(),
            dispatch_refreshUnreadCount(),
        ])
    } catch (e) {
        console.log(e)
        onRefreshAuth()
    }
})

const { t } = useI18n({
    useScope: 'global',
    messages: {
        [LANGUAGE.zhCN]: {
            _account_login: '密码登录',
            _mobile_login: '验证码登录',
            _remember: '记住密码',
            _protocol: '点击登录即代表同意{0}和{1}',
            _protocol_service: '《服务条款》',
            _protocol_privacy: '《隐私政策》',
            _register_tip: '还没有账号?{0}',
            _register_now: '马上开户',
            _successfully: '登录成功',
        },
        [LANGUAGE.zhHK]: {
            _account_login: '密码登录',
            _mobile_login: '验证码登录',
            _remember: '记住密码',
            _protocol: '点击登录即代表同意{0}和{1}',
            _protocol_service: '《服务条款》',
            _protocol_privacy: '《隐私政策》',
            _register_tip: '还没有账号?{0}',
            _register_now: '马上开户',
            _successfully: '登录成功',
        },
        [LANGUAGE.enUS]: {
            _account_login: 'Password login',
            _mobile_login: 'OPT login',
            _remember: 'Remember Password',
            _protocol: 'Protocol {0} & {1}',
            _protocol_service: '《Terms of Service》',
            _protocol_privacy: '《Privacy Policy》',
            _register_tip: 'Don\'t have an account? {0}',
            _register_now: 'Register Now',
            _successfully: 'Login Successfull',
        },
    },
})

defineOptions({ name: 'login' })
</script>

<style scoped>
.c-controller:not(:last-child) {
    margin-bottom: 20px;
}
</style>

<template>
    <AuthPopup class="pb-6">
        <div class="text-primary mb-8 text-lg font-semibold">
            {{ t('_forget') }}
        </div>

        <van-form @submit="onValid">
            <c-input
                required
                type="tel"
                inputmode="numeric"
                :maxlength="11"
                :placeholder="t('form.input_placeholder', [ t('profile.mobile') ])"
                v-model="formState.mobile"
            >
                <template #prefix>
                    <c-icon prefix="auth" name="mobile"/>
                </template>
            </c-input>

            <c-input
                class="flex-1"
                inputmode="numeric"
                :maxlength="6"
                :placeholder="t('form.input_placeholder', [ t('auth.otp') ])"
                v-model="formState.smsCode"
            >
                <template #prefix>
                    <c-icon prefix="auth" name="captcha"/>
                </template>

                <template #addOnAfter>
                    <van-button
                        type="primary"
                        :loading="otpLoading"
                        :disabled="!formState.mobile || otpDisabled"
                        @click="onSendOtp(formState.mobile)"
                    >
                        {{ otpText }}
                    </van-button>
                </template>
            </c-input>

            <c-input
                autocomplete="new-password"
                type="password"
                required
                :maxlength="16"
                :placeholder="t('auth.password_regex')"
                v-model="formState.password"
            >
                <template #prefix>
                    <c-icon prefix="auth" name="lock"/>
                </template>
            </c-input>

            <c-input
                type="password"
                required
                :maxlength="16"
                :placeholder="t('auth.repeat')"
                v-model="formState.repeat"
            >
                <template #prefix>
                    <c-icon prefix="auth" name="lock"/>
                </template>
            </c-input>

            <div class="mt-8">
                <van-button
                    type="primary"
                    native-type="submit"
                    block
                    round
                    :loading
                    :disabled
                >
                    {{ t('form.reset') }}
                </van-button>
            </div>
        </van-form>

        <div class="text-center mt-5 text-xs text-paragraph">
            <router-link
                class="text-link underline"
                :to="{ name: 'login' }"
                replace
            >
                {{ t('_login') }}
            </router-link>
        </div>
    </AuthPopup>
</template>

<script setup>
import AuthPopup from '@/layout/auth/components/AuthPopup.vue'
import { API_PATH } from '@/apis/index.js'

const { replace } = useRouter()

const { otpText, otpLoading, otpDisabled, onSendOtp } = useOtp({ type: OTP_TYPES.UPDATE_PASSWORD })

const formState = ref({
    mobile: '',
    smsCode: '',
    password: '',
    repeat: '',
})

const disabled = useFormDisabled(formState)

const onValid = () => {
    const { mobile, password, smsCode, repeat } = formState.value

    if (!REGULAR.MOBILE.test(mobile)) return showFailToast(t('auth.account_error'))
    if (!REGULAR.OTP.test(smsCode)) return showFailToast(t('auth.otp_error'))

    if (!REGULAR.NEW_PASSWORD.test(password)) return showFailToast(t('auth.password_error'))
    if (!REGULAR.NEW_PASSWORD.test(repeat)) return showFailToast(t('auth.password_error'))

    if (password !== repeat) return showFailToast(t('auth.repeat_error'))

    onSubmit()
}

const [ onSubmit, loading ] = useFetchLoading(async () => {
    const { mobile, password, smsCode } = formState.value

    await api_post({
        url: API_PATH.UPDATE_PASSWORD,
        params: {
            mobile,
            newPassword: btoa(password),
            smsCode,
            verifyType: 'mobile',
            type: 1,
        },
    })

    sessionStorage.setItem('loginInfo', JSON.stringify({ mobile, password, smsCode: '' }))

    showSuccessToast(t('_successfully'))
    replace({ name: 'login' })
})

const { t } = useI18n({
    useScope: 'global',
    messages: {
        [LANGUAGE.zhCN]: {
            _forget: '忘记密码',
            _login: '返回登录',
            _successfully: '修改密码成功',
        },
        [LANGUAGE.zhHK]: {
            _forget: '忘记密码',
            _login: '返回登录',
            _successfully: '修改密码成功',
        },
        [LANGUAGE.enUS]: {
            _forget: 'Forgot Password',
            _login: 'To Login',
            _successfully: 'Update Password Successfully',
        },
    },
})

defineOptions({ name: 'forget' })
</script>

<style scoped>
.c-controller:not(:last-child) {
    margin-bottom: 20px;
}
</style>

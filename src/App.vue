<template>
    <van-config-provider
        class="h-full"
        :theme="$theme"
    >
        <router-view/>

        <van-floating-bubble
            v-if="bubbleVisible"
            icon="chat"
            axis="xy"
            magnetic="x"
            :gap="{ x: 20, y: 60 }"
            @click="utils_link($globalConfig?.service)"
        />
    </van-config-provider>
</template>

<script setup>
import { $theme } from '@/store'
import socket from '@/socket.js'
import { utils_link } from '@/utils/index.js'

const { currentRoute, push } = useRouter()

useMarketStatusStore()

const { $globalConfig } = storeToRefs(useGlobalStore()),
    { $isLogin } = storeToRefs(useProfileStore()),
    bubbleVisible = computed(() => $isLogin.value && currentRoute.value.name !== 'service' && $globalConfig.value.service)

const { $inviteCode } = storeToRefs(useGlobalStore())
const urlParams = new URLSearchParams(window.location.hash.split('?')[1]),
    inviteCode = urlParams.get('inviteCode')

if (inviteCode) $inviteCode.value = inviteCode

document.documentElement.dataset.theme = $theme.value

// 系统消息
socket.on(SOCKET_EVENTS.SYSTEM, ({ data }) => {
    const systemNotifyInstance = showNotify({
        type: 'primary',
        message: data.content,
        onClick: () => {
            systemNotifyInstance.close()
        },
    })
})

let warningNotifyInstance

const { t } = useI18n({
    messages: {
        [LANGUAGE.zhCN]: {
            _warning: '股票提醒',
        },
        [LANGUAGE.zhHK]: {
            _warning: '股票提醒',
        },
        [LANGUAGE.enUS]: {
            _warning: 'Stock Alert',
        },
    },
})

const warningToast = ({ data }) => {
    utils_speech_synthesis(t('_warning'))
    warningNotifyInstance = showNotify({
        type: 'warning',
        message: data.content,
        onClick: () => {
            warningNotifyInstance.close()
        },
    })
}
// 股票预警
socket.on(SOCKET_EVENTS.WARNING, warningToast)
// 强平
socket.on(SOCKET_EVENTS.CLOSE, warningToast)
</script>

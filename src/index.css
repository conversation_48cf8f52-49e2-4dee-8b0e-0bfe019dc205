@import 'tailwindcss';

:root {
    --special: #E06822;
}

/*@custom-variant data-dark (&[data-theme~="dark"]);*/

@theme {
    --color-primary: var(--primary);
    --color-raise: var(--raise);
    --color-fall: var(--fall);
    --color-red: var(--red);
    --color-green: var(--green);
    --color-page: var(--page);
    --color-bg: var(--bg);
    --color-border: var(--border);
    --color-title: var(--title);
    --color-paragraph: var(--paragraph);
    --color-text: var(--text);
    --color-active: var(--active);
    --color-link: var(--link);
    --color-controller_bg: var(--controller_bg);
    --color-input: var(--input);
    --color-card: var(--card);
    --color-special: var(--special);
}

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
    *,
    ::after,
    ::before,
    ::backdrop,
    ::file-selector-button {
        border-color: var(--color-gray-200, currentColor);
    }
}

@utility flex-center {
    @apply flex items-center justify-center;
}

@utility flex-middle {
    @apply flex items-center;
}

@utility flex-between {
    @apply flex items-center justify-between;
}

@utility bg-active {
    background-color: var(--active);
    border-color: var(--primary);
    color: var(--van-white);
}

/* 页面容器 */
.with-header-container {
    /*
        Header --header-height
    */
    height: calc(100% - var(--header-height));
    overflow-x: hidden;
    overflow-y: auto;
    padding: 10px;
}

.with-header-container__noPadding {
    /*
        Header --header-height
    */
    height: calc(100% - var(--header-height));
    overflow-x: hidden;
    overflow-y: auto;
}

.tab-container {
    height: calc(100% - var(--van-tabs-line-height));
    overflow-x: hidden;
    overflow-y: auto;
}

/* 数字字体 */
@font-face {
    font-family: 'digit'; /* 自定义字体名称 */
    src: url('/fonts/AkzidenzGrotesk-ExtraBoldCond.otf') format('opentype'); /* 路径和格式 */
    font-weight: normal; /* 字体粗细 */
    font-style: normal;  /* 字体样式 */
}

.font-digit {
    font-family: 'digit', sans-serif;
}

html,
body,
#app {
    @apply size-full overflow-hidden;
}

#app {
    @apply max-w-[640px] mx-auto text-text capitalize;
}

::-webkit-scrollbar {
    display: none;
}

:root {
    --red: #C92C31;
    --green: #2cb274;
    /*--red: 201, 44, 49;*/
    /*--green: 44, 178, 116;*/
}

/* 涨跌红绿 */
.red_green_trend {
    --raise: var(--red);
    --fall: var(--green);
}

.green_red_trend {
    --raise: var(--green);
    --fall: var(--red);
}

/* 浅色主题 */
.van-theme-light {
    --primary: #3A5BCD; /* 主色调 */
    --page: #F4F7FE; /* 页面背景色 */
    --bg: var(--van-white); /* 背景色 */
    --border: #F7F8F8; /* 边框 */
    --active: var(--primary); /* 激活状态 */
    --title: #525A79; /* 标题文本 */
    --paragraph: #373737; /* 段落文本 */
    --text: #AFB8CB; /* 普通文本 */
    --link: #3B5CCF; /* 链接 */
    --controller_bg: #F1F4FF; /* 输入控件背景色 */
    --input: #A0A4B9;
    --shadow: 0 4px 8px 0 rgba(53, 70, 119, .1); /* 阴影 */
    --card: var(--bg); /* Card */
}

.van-theme-light #app {
    background: var(--page);
}

/* 阴影效果 */
.van-theme-light .custom-shadow {
    box-shadow: var(--shadow);
    backdrop-filter: blur(4px)
}

/* 浅色主题 */

/* 深色主题 */
.van-theme-dark {
    --primary: #3A5BCD; /* 主色调 */
    --page: #13161B; /* 页面背景色 */
    --bg: #1C1740; /* 背景色 */
    --border: #212837; /* 边框 */
    --active: #4163DF; /* 激活状态 */
    --title: #AEB6C6; /* 标题文本 */
    --paragraph: #fff; /* 段落文本 */
    --text: #5F687C; /* 普通文本 */
    --link: #3B5CCF; /* 链接 */
    --controller_bg: #13161B; /* 输入控件背景色 */
    --input: #525A79; /* 输入控件背景色 */
    --shadow: 0 4px 8px 0 rgba(11, 18, 36, .1); /* 阴影 */
    --card: rgba(31, 35, 41, .4); /* Card */
}

.van-theme-dark #app {
    background: url('/dark_bg.png') no-repeat;
    background-size: cover;

    /* Vant */
    --van-cell-group-background: var(--card);
    --van-cell-background: var(--card);

    /* Picker */
    --van-picker-mask-color: linear-gradient(180deg, rgba(0, 0, 0, .3), rgba(0, 0, 0, .1)),
    linear-gradient(0deg, rgba(0, 0, 0, .3), rgba(0, 0, 0, .1));

    /* Cell */
    --van-cell-border-color: #253559;

    /* Skeleton */
    --van-active-color: var(--input);
    --van-skeleton-paragraph-background: var(--input);
}

/* 阴影效果 */
.van-theme-dark .custom-shadow {
    backdrop-filter: blur(4px)
}

/* 深色主题 */

/* 主色背景 */
.primary-bg {
    background-image: var(--primary-bg);
}

#app {
    --header-height: 44px;

    /* Vant 配置 */
    /* 主色调 */
    --van-primary-color: var(--primary);

    --primary-bg: linear-gradient(270deg, #5959ED 0%, #4686FC 100%),
    linear-gradient(270deg, #4686FC 0%, #2BB1FF 100%),
    linear-gradient(90deg, #4366DE 0%, #3150BD 100%);

    /* Button */
    --van-button-primary-background: var(--primary-bg);
    --van-button-radius: 8px;
    --van-button-mini-height: 26px;
    --van-button-default-height: 40px;

    /* Tabs */
    --van-tabs-line-height: 40px;
    --van-tab-active-text-color: var(--active);
    --van-tabs-bottom-bar-color: var(--active);
    --van-tabs-bottom-bar-height: 2px;
    --van-tab-text-color: var(--title);
    --van-tabs-bottom-bar-width: 20px;
    --van-tabs-nav-background: transparent;
    --van-tabs-default-color: var(--primary);

    /* Popover */
    --van-popover-action-height: 40px;

    /* Cell */
    --van-cell-text-color: var(--title);
    --van-cell-value-color: var(--text);
    --van-cell-value-font-size: 12px;
    --van-cell-large-title-font-size: 14px;
    --van-cell-large-vertical-padding: 14px;

    /* Grid */
    --van-grid-item-content-background: transprent;

    /* Picker */
    --van-picker-background: var(--bg);

    /* Field */
    --van-field-label-color: var(--title);

    /* Radio */
    --van-radio-checked-icon-color: var(--primary);

    /* Checkbox */
    --van-checkbox-checked-icon-color: var(--primary);

    /* PasswordInput */
    --van-password-input-margin: 0
}

/* 浮动按钮 */
.download.van-floating-bubble {
    --van-floating-bubble-background: linear-gradient(180deg, #FFFFFF 0%, #EEEEEE 100%);
    --van-floating-bubble-border-radius: 16px;
    box-shadow: 0 4px 8px 0 #35467714;
    color: var(--link);
    text-align: center;
    font-size: 12px;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

/* 英文文本大驼峰 */
.van-button,
.van-popup {
    text-transform: capitalize;
}

/* Button mini */
.van-button--mini {
    --van-button-radius: 4px;
    --van-button-mini-padding: 0 var(--van-padding-xs);
}

.van-button--primary {
    --van-button-border-width: 0;
}

/* Tab 改造 */
.van-tabs .van-tabs__wrap .van-tabs__nav--shrink {
    padding-left: 0;
    padding-right: 0;
    align-items: center;
}

.van-tabs .van-tabs__content {
    overflow-x: hidden;
    overflow-y: auto;
}

.van-tabs--line > .van-tabs__content {
    height: calc(100% - var(--van-tabs-line-height));
}

.van-tabs--card > .van-tabs__content {
    height: calc(100% - var(--van-tabs-card-height));
}

.van-tabs .van-tab__panel {
    height: 100%;
}

.van-tabs__nav--shrink.van-tabs__nav {
    gap: 20px;
}

.van-tab--shrink.van-tab {
    padding: 0;
}

/* 骨架屏 */
#app .van-skeleton {
    padding: 0;
}

/* Cell */
.van-cell-group {
    border-radius: 8px;
    overflow: hidden;
}

/* Popover */
.van-popover {
    --van-popover-action-width: 100%;
    box-shadow: var(--shadow);
}

/* Notify */
.van-notify--primary {
    color: var(--van-white);
    /*--van-notify-primary-background: var(--primary);*/
}

/* 小块 */
.marketBlock {
    color: var(--van-white);
    height: 16px;
    line-height: 16px;
    font-size: 10px;
    padding: 0 4px;
    border-radius: 2px;
}

:where(.marketBlock) {
    background: var(--primary);
}

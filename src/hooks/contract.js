import _ from 'lodash'

/**
 * @function useAccountId
 * @description 根据账号类型返回对应的 ID 配置
 * @param [defaultType] {string} 默认类型
 * */
export const useAccountId = (defaultType) => {
    // 路由参数
    const { params: { type, id } } = useRoute(),
        // 判断是否现货类型
        isSpot = (defaultType ?? type) === ACCOUNT_TYPE.SPOT

    let idKey
    if (isSpot) {
        idKey = 'commentAssetId'
    } else {
        idKey = 'contractId'
    }

    return {
        isSpot,
        id,
        idKey,
        idParams: { [idKey]: id },
    }
}

/**
 * @function useCurrentContract
 * @description 获取当前合约
 * */
export const useCurrentContract = () => {
    const { params: { id } } = useRoute(),
        { $contract } = storeToRefs(useAccountStore())

    return computed({
        get: () => _.find($contract.value, { id: +id }) ?? {
            // 总资产
            allAsset: 0,
            // 合约盈亏
            accountWinAmount: 0,
            // 平仓金额
            closeRemindAmount: 0,
            //
            coverLossAmount: 0,
            // 扩大保证金
            expendAmount: 0,
            // 冻结资金
            freezePower: 0,
            id: 0,
            // 初始保证金
            initCash: 0,
            // 利息券
            interestAmount: 0,
            // 资金利率
            interestRate: 0,
            // 倍数
            multiple: 0,
            // 周期类型
            periodType: 0,
            // 持仓金额
            positionAmount: 0,
            // 今日盈亏
            todayWinAmount: 0,
            //
            totalAccountAmount: 0,
            totalCash: 0,
            // 杠杆倍数金额
            totalFinance: 0,
            // 总操盘资金
            totalPower: 0,
            // 合约类型
            type: 0,
            // 可用余额
            useAmount: 0,
            // 预警线金额
            warnRemindAmount: 0,
            // 提盈金额
            withdrawAmount: 0,
            marketType: '',
            currency: CURRENCY.CNY,
            // 到期时间
            expireTime: '',
            // 申请时间
            openTime: '',
            // 结算状态
            settlementStatus: 0,
            // 今日盈亏
            todayWinRate: 0,
            // 昨日资产？
            yesterdayAsset: 0,
        },
        set(val) {
            const updateIndex = _.findIndex($contract.value, { id: +id })
            $contract.value[updateIndex] = val
        },
    })

    // onBeforeUnmount(clearIntervalInstance)
    //
    // onMounted(async () => {
    //     await nextTick()
    //     intervalInstance = setInterval(async () => {
    //         const marketStatus = useMarketStatus(details.value.marketType)
    //
    //         if (marketStatus) {
    //             details.value = await api_get({
    //                 // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/app%E7%AB%AF-%E5%90%88%E7%BA%A6%E4%B8%9A%E5%8A%A1/getContractSummaryUsingGET_1
    //                 url: `/contract/account/getContractSummary/${id}`,
    //             })
    //         } else {
    //             clearIntervalInstance()
    //         }
    //     }, 5000)
    // })

    // return details
}

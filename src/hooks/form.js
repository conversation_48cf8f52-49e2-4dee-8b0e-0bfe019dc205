import _ from 'lodash'

/**
 * @function useFormDisabled
 * @param formState {Ref} 需要校验的表单数据
 * @param [omit] {string | string[]} 需要校验的表单数据
 * @param [isEvery] {boolean} 是否校验每个值 | 否则一个不为空便通过
 * @return boolean
 * */
export const useFormDisabled = (formState, omit, isEvery = true) => computed(() => {
    const validData = _.values(_.omit(unref(formState), unref(omit)))

    return isEvery ? !_.every(validData, Boolean) : !_.some(validData, <PERSON><PERSON>an)
})

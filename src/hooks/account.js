import Amount from '@/components/Amount/index.vue'
import Table from '@/components/Table/index.vue'
import Password from '@/components/Password/index.vue'
import StockBaseInfo from '@/pages/main/stock/components/StockBaseInfo.vue'
import { API_PATH, usePagination } from '@/apis/index.js'
import { useEntrustRevoke } from '@/hooks/stock.js'

// 持仓请求函数
export const usePositionFetch = (fetchOption) => usePagination({
    url: API_PATH.POSITION,
    ...fetchOption,
    needLogin: true,
})

// 合约列表请求函数
export const useContractList = (fetchOption) => {
    const { $isLogin } = storeToRefs(useProfileStore())

    const { currentRoute } = useRouter()

    return useIntervalFetch({
        // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/app%E7%AB%AF-%E5%90%88%E7%BA%A6%E4%B8%9A%E5%8A%A1/getContractSummaryUsingGET_1
        url: '/contract/account/getContractSummaryPage',
        ...fetchOption,
        needLogin: true,
    }, {
        config: {
            method: FETCH_INTERVAL_TYPE.PAGINATION,
            callback: () => {
                // return useMarketStatus(_.keys(STOCK_CONFIG)) && $isLogin.value
                return $isLogin.value && ACCOUNT_ROUTES.includes(currentRoute.value.name)
            },
        },
    })
}

// 委托类型基础表格
const useEntrustBaseTable = (options, columns, type) => {
    const { t } = useI18n()

    const {
        list,
        initial,
        finished,
        pagination,
        refreshLoading,
        loadLoading,
        onRefresh,
        onLoadMore,
    } = usePagination({
        needLogin: true,
        ...options,
        url: API_PATH.ENTRUST,
    })

    const { dispatch_checkStock } = useStockStore()

    const onRevoke = useEntrustRevoke(onRefresh)

    const operation = {
        title: t('operation.title'),
        align: 'right',
        dataIndex: 'id',
        render: (id) => h(
            'div',
            {
                class: 'text-red',
                onClick: (e) => {
                    e.stopPropagation()

                    onRevoke(id)
                }
            },
            t('stock.entrust.revoke')
        )
    }

    const _columns = unref(columns)

    if (type === SOCKET_EVENTS.ENTRUST && 'status' in options.params) {
        _columns.push(operation)
    }

    const EntrustTable = (props) => {
        return h(
            Table,
            {
                dataSource: list.value,
                columns: _columns,
                finished: finished.value,
                refreshLoading: refreshLoading.value,
                'onUpdate:refreshLoading': val => {
                    refreshLoading.value = val
                },
                loadLoading: loadLoading.value,
                'onUpdate:loadLoading': val => {
                    loadLoading.value = val
                },
                onRefresh,
                onLoadMore,
                onRowClick: record => {
                    if (props.onRowClick) props.onRowClick(record)
                    else dispatch_checkStock(record, { params: { contractId: params.contractId } })
                },
                ...props,
            },
        )
    }

    return {
        Table: EntrustTable,
        list,
        initial,
        finished,
        pagination,
        refreshLoading,
        loadLoading,
        onRefresh,
        onLoadMore,
    }
}

// 委托明细表格
export const useEntrustTable = (options) => {
    const { t } = useI18n()

    const columns = computed(() => [
        {
            title: [ t('stock.name'), t('stock.symbol') ].join('|'),
            render: ({ symbolName, symbol, market }) => h(
                StockBaseInfo,
                {
                    name: symbolName,
                    symbol,
                    market,
                },
            ),
        },
        {
            title: t('stock.entrust.price'),
            dataIndex: 'tradePrice',
            align: 'center',
            render: val => h(Amount, { amount: val, color: 'text-primary', precision: 3 }),
        },
        {
            title: [ t('stock.entrust.deal_quantity'), t('stock.entrust.total') ].join('|'),
            align: 'right',
            render: ({ dealNum, tradeNum }) => h(
                'div',
                {
                    class: 'text-right',
                },
                [
                    h(
                        'div',
                        dealNum,
                    ),
                    h(
                        'div',
                        tradeNum,
                    ),
                ],
            ),
        },
        {
            title: [ t('stock.entrust.direction'), t('common.status') ].join('|'),
            align: 'right',
            render: ({ direction, tradeType, status }) => {
                const { label: directionLabel, color: directionColor } = tradeDirectionDict(direction),
                    { label: statusLabel, color: statusColor } = ENTRUST_STATUS_DICT[status]

                return h(
                    'div',
                    {
                        class: 'text-right text-xs',
                    },
                    [
                        h(
                            'div',
                            {
                                class: `text-${directionColor}`,
                            },
                            [
                                t(`dict.${directionLabel}`),
                                tradeTypeDict(tradeType)?.label,
                            ],
                        ),
                        h(
                            'div',
                            {
                                class: `text-${statusColor}`,
                            },
                            t(`dict.${statusLabel}`),
                        ),
                    ],
                )
            },
        }
    ])

    return useEntrustBaseTable(options, columns, SOCKET_EVENTS.ENTRUST)
}

// 成交明细表格
export const useDealTable = (options) => {
    const { t, locale } = useI18n()

    const columns = [
        {
            title: [ t('stock.name'), t('stock.symbol') ].join('|'),
            render: ({ symbolName, symbol, market }) => h(
                StockBaseInfo,
                {
                    name: symbolName,
                    symbol,
                    market,
                },
            ),
        },
        {
            title: [ t('stock.transaction.price'), t('stock.transaction.quantity') ].join('|'),
            align: 'center',
            render: ({ dealPrice, dealNum }) => h(
                h(
                    'div',
                    {
                        class: 'text-center text-xs',
                    },
                    [
                        h(
                            'div',
                            utils_currency(dealPrice, { precision: 3 }),
                        ),
                        h(
                            'div',
                            dealNum,
                        ),
                    ],
                ),
            ),
        },
        {
            title: t('stock.entrust.deal_amount'),
            dataIndex: 'transactionAmount',
            align: 'right',
            render: val => h(Amount, { amount: val, color: 'text-primary', class: 'text-xs', precision: 3 }),
        },
        {
            title: [ t('stock.entrust.direction'), t('common.time') ].join('|'),
            align: 'right',
            width: '90px',
            render: ({ direction, tradeType, dealTime }) => {
                const { label: directionLabel, color: directionColor } = tradeDirectionDict(direction)

                return h(
                    'div',
                    {
                        class: 'text-right text-xs',
                    },
                    [
                        h(
                            'div',
                            {
                                class: `text-${directionColor}`,
                            },
                            [
                                t(`dict.${directionLabel}`),
                                tradeTypeDict(tradeType)?.label,
                            ],
                        ),
                        h(
                            'div',
                            {
                                class: 'text-[10px]',
                            },
                            utils_time(dealTime, 'YYYY/MM/DD HH:mm'),
                        ),
                    ],
                )
            },
        },
    ]

    return useEntrustBaseTable(options, columns, SOCKET_EVENTS.ENTRUST_DEAL)
}

/**
 * @function useRate
 * @description RateCurrency 获取当前币种汇率
 * @param [option] {object} 配置项
 * @param [option.defaultCurrency] {object} 默认币种
 * @param [option.disabled] {boolean} 是否禁用切换
 * */
export const useRate = (option) => {
    const { defaultCurrency = CURRENCY.CNY, disabled } = option ?? {}

    const { dispatch_getCurrencyRateConfig } = useRateStore()

    // 当前币种
    const currency = ref(defaultCurrency),
        // 当前币种对应汇率、禁用状态则默认不返回
        rate = ref(disabled ? 1 : dispatch_getCurrencyRateConfig(currency.value.rate).rate)

    return [ currency, rate ]
}

export const usePassword = (onFinish) => {
    const visible = ref(false),
        password = ref('')

    const PasswordComp = () => h(Password, {
        modelValue: password.value,
        'onUpdate:modelValue': val => {
            password.value = val
        },
        visible: visible.value,
        'onUpdate:visible': val => {
            visible.value = val
        },
        onFinish: async (val) => {
            try {
                await onFinish?.(val)
            } finally {
                password.value = ''
            }
        },
    })

    const onOpenPassword = () => {
        visible.value = true
    }

    return {
        visible,
        onOpenPassword,
        Password: PasswordComp,
    }
}

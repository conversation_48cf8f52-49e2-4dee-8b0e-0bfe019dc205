import _ from 'lodash'

import StockBaseInfo from '@/pages/main/stock/components/StockBaseInfo.vue'
import Amount from '@/components/Amount/index.vue'
import { API_PATH } from '@/apis/index.js'
import { useIntervalFetch } from './common.js'
import { useI18n } from 'vue-i18n'

// 今日股市行情
export const useTodayTrend = (fetchOption) => useIntervalFetch({
    // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E8%A1%8C%E6%83%85%E6%95%B0%E6%8D%AE/gainDistributionUsingGET_1
    url: '/market/gain/distribution',
    initialValues: [],
    ...fetchOption,
}, {
    config: {
        method: FETCH_INTERVAL_TYPE.REQUEST,
        // callback: (_res, _params) => {
        //     return useMarketStatus(_params.marketType)
        // },
    },
})

// 行业板块
export const useIndustry = (fetchOption, paginationOption) => useIntervalFetch({
    // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E8%A1%8C%E6%83%85%E6%95%B0%E6%8D%AE/plateListUsingGET_1
    url: '/market/plate/list',
    ...fetchOption,
}, {
    config: {
        method: FETCH_INTERVAL_TYPE.PAGINATION,
        // callback: (_res, _params) => {
        //     return useMarketStatus(_params.marketType)
        // },
    },
    paginationOption,
})

/**
 * @function useMarketStatus
 * @description 获取股票市场当前状态
 * @param marketType {string|string[]} 需要查询的股票市场
 * */
export const useMarketStatus = (marketType) => {
    const marketStatusStore = useMarketStatusStore(),
        { dispatch_refreshMarketStatus } = marketStatusStore,
        { $marketStatus } = storeToRefs(marketStatusStore)

    if (!marketType) return false

    dispatch_refreshMarketStatus()

    if (_.isString(marketType)) {
        const marketStatus = $marketStatus.value.list.find(e => e.marketType === marketType)
        if (marketStatus) {
            return marketStatus.statusInfo.status === 2
        } else {
            return false
        }
    } else {
        return $marketStatus.value.list.some(e => {
            return marketType.includes(e.marketType) && e.statusInfo.status === 2
        })
    }
}

export const stockInfoInitial = {
    symbol: '',
    industryPlate: '',
    precision: 0,
    high52w: 0,
    peStatic: 0,
    peLyr: 0,
    securityStatus: 0,
    gain: 0,
    amplitude: 0,
    high: 0,
    low: 0,
    floatShare: 0,
    VWAP: 0,
    dividend: 0,
    currency: '',
    tag: '',
    close: 0,
    turnover: 0,
    latestPrice: 0,
    peTtm: 0,
    amount: 0,
    chg: 0,
    lotSize: 1,
    marketValue: 0,
    dividendRate: 0,
    priceUpLimited: 0,
    priceDownLimited: 0,
    totalShares: 0,
    market: '',
    volume: 0,
    pb: 0,
    securityType: '',
    low52w: 0,
    name: '',
    latestTime: 0,
    open: 0,
}

export const onGetStockInfo = async (params) => await api_get({
    url: API_PATH.STOCK_INFO, params, options: {
        headers: {
            cancellable: false,
        },
    },
})

// 股票列表
export const useMarketStocks = (fetchOption, paginationOption) => useIntervalFetch({
    url: API_PATH.STOCK,
    ...fetchOption,
}, {
    // config: {
    //     callback: (_res, _params) => {
    //         return useMarketStatus(_params.marketType)
    //     },
    // },
    paginationOption: {
        responseKeys: {
            list: 'list',
            total: 'totalNum'
        },
        ...paginationOption,
    },
})

/**
 * @function useStockRedirect
 * @description 股票页面跳转
 * */
export const useStockRedirect = () => {
    // 当前所在股票页参数
    const { name, query, params: { type } } = useRoute(), { push, replace } = useRouter()

    // 判断是否股指
    const isIndex = +type === 2

    const onRedirect = (name, isReplace = true) => {
        const redirect = isReplace ? replace : push

        let _name = name
        // 跳转交易页需要判断股指跳转不同页面
        if (name === STOCK_ROUTE.TRANSACTION) {
            _name = isIndex ? STOCK_ROUTE.TRANSACTION_INDEX : STOCK_ROUTE.TRANSACTION_STOCK
        }

        redirect({ name: _name, query })
    }

    return {
        routeName: name,
        isIndex,
        onRedirect,
    }
}

// 自选股票
export const useStockCollect = (fetchOptions, paginationOption) => usePagination({
    // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E8%AF%81%E5%88%B8%E6%8E%A7%E5%88%B6%E5%99%A8/getUserOptionalListUsingGET_1
    url: '/symbol/watchlist/list', needLogin: true, ...fetchOptions,
}, paginationOption)

export const useStockColumns = () => {
    const { t } = useI18n()

    return computed(() => [
        {
            title: t('stock.name'), render: ({ name, symbol, market }) => h(StockBaseInfo, {
                name, symbol, market,
            }),
        },
        {
            title: t('stock.current_price'),
            dataIndex: 'latestPrice',
            align: 'right',
            sortable: true,
            render: (val, { gain }) => h(Amount, {
                color: utils_amount_color(gain), amount: val, precision: 3,
            }),
        },
        {
            title: t('stock.raise_fall'), dataIndex: 'gain', align: 'right', sortable: true, render: (val) => {
                const color = utils_amount_color(val, { raise: 'bg-raise', fall: 'bg-fall', flat: 'bg-text' })

                return h('div', {
                    class: [ 'min-w-[68px] text-center h-7 leading-7 px-2 rounded-sm text-white', color ],
                }, h(Amount, {
                    symbol: true, percent: true, amount: val * 100,
                }))
            },
        },
    ])
}

// 委托单撤单
export const useEntrustRevoke = (callback) => {
    const { t } = useI18n()

    const { dispatch_refreshAccount } = useAccountStore()

    const [ onFetch ] = useFetchLoading(async (id) => {
        await api_post({
            // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/app%E7%AB%AF-%E8%AE%A2%E5%8D%95%E7%9B%B8%E5%85%B3/cancelOrderUsingPOST_1
            url: '/order/cancelOrder',
            params: {
                id
            },
        })

        showSuccessToast(t('operation.successfully'))

        dispatch_refreshAccount()

        callback()
    })

    return async(id) => {
        await showConfirmDialog({
            title: t('stock.entrust.revoke_confirm'),
        })
        await onFetch(id)
    }
}
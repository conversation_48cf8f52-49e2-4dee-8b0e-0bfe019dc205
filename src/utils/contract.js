import i18n from '@/i18n/index.js'

/**
 * @function utils_contract_name
 * @description 合约名称
 * @param type {string} 合约类型
 * @param marketType {string} 合约市场
 * @param periodType {number} 周期类型
 * @param multiple {number} 杠杆倍数
 * @param id {number} id
 * @returns string
 * */
export const utils_contract_name = ({ marketType, type, periodType, multiple, id }) => {
    const t = i18n.global.t

    const market = STOCK_CONFIG[marketType] ? t(`stock.${STOCK_CONFIG[marketType].symbol ?? ''}`) : ''

    return t(`contract.type_${type}`) + t(`contract.period_${periodType}`) + t('contract.multiples', [ multiple ]) + market + `[${id}]`
}

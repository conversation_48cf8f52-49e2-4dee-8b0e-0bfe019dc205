// 合约类型
export const useContractType = () => {
    const { params: { contractType } } = useRoute()

    const { t } = useI18n({
        useScope: 'global',
        messages: {
            [LANGUAGE.zhCN]: {
                _contract: '股票合约',
                _futures: '期货',

                _normal: '普通{0}',
                _experience: '体验{0}',
                _bonus: '彩金{0}',
            },
            [LANGUAGE.zhHK]: {
                _contract: '股票合約',
                _futures: '期货',

                _normal: '普通{0}',
                _bonus: '彩金{0}',
                _experience: '体验{0}',
            },
            [LANGUAGE.enUS]: {
                _contract: 'stock contract',
                _futures: 'Futures',
                _normal: 'Normal {0}',
                _bonus: 'Bonus {0}',
                _experience: 'Experience {0}',
            },
        },
    })

    // 合约周期类型选项配置
    const contractPeriodOptions = [
        { label: t('contract.period_1'), value: 1, key: 'daily' },
        { label: t('contract.period_2'), value: 2, key: 'weekly' },
        { label: t('contract.period_3'), value: 3, key: 'monthly' },
    ]

    const contractPeriodDict = utils_options_to_dict(contractPeriodOptions)

    const typeTitle = +contractType === 1 ? t('_contract') : t('_futures')
    // 合约类型选项配置
    const contractTypeOptions = [
        { label: t('_normal', [ typeTitle ]), value: 1, key: 'normal' },
        { label: t('_experience', [ typeTitle ]), value: 2, key: 'experience', visibleIndex: 2 },
        { label: t('_bonus', [ typeTitle ]), value: 3, key: 'bonus', visibleIndex: 1 },
    ]
    // 合约类型字典表
    const contractTypeDict = utils_options_to_dict([
        ...contractTypeOptions,
        { label: t('futures.title'), value: 4, key: 'futures' },
    ])

    // 合约状态字典表
    const contractStatusDict = {
        0: {
            title: t('dict.待审核'),
        },
        1: {
            title: t('dict.审核成功'),
            color: 'text-green',
        },
        2: {
            title: t('dict.审核失败'),
            color: 'text-red',
        },
    }

    return {
        contractType,
        contractTypeOptions,
        contractTypeDict,
        contractStatusDict,
        contractPeriodOptions,
        contractPeriodDict,
    }
}

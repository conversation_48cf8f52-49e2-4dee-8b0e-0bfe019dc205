import dayjs from 'dayjs'

import i18n from '@/i18n'

const today = dayjs().endOf('days')

const t = i18n.global.t

// 记录时间范围预设
export const record_range_presets = computed(() => [
    { label: t('form.range_今日'), value: [ dayjs().startOf('days'), today ] },
    {
        label: t('form.range_昨日'),
        value: [ dayjs().subtract(1, 'days').startOf('days'), dayjs().subtract(1, 'days').endOf('days') ],
    },
    { label: t('form.range_近3日'), value: [ dayjs().subtract(3, 'days').startOf('days'), today ] },
    { label: t('form.range_近7日'), value: [ dayjs().subtract(7, 'days').startOf('days'), today ] },
    { label: t('form.range_近15日'), value: [ dayjs().subtract(15, 'days').startOf('days'), today ] },
    { label: t('form.range_本月'), value: [ dayjs().startOf('months'), today ] },
    {
        label: t('form.range_上月'),
        value: [ dayjs().subtract(1, 'months').startOf('months'), dayjs().subtract(1, 'months').endOf('months') ],
    },
])

// 排序类型配置
export const SORT_CONFIG = {
    ASCENDING: 'ASC',
    DESCENDING: 'DESC',
    NORMAL: 'normal',
}

// 多语言配置
export const LOCALE_CONFIG = {
    [LANGUAGE.zhCN]: '简体中文',
    [LANGUAGE.zhHK]: '繁体中文',
    [LANGUAGE.enUS]: 'English',
}

export const DEFAULT_LANGUAGE = {
    [LANGUAGE.zhCN]: '',
    [LANGUAGE.zhHK]: '',
    [LANGUAGE.enUS]: '',
}

// 主题配置
export const THEME_CONFIG = {
    LIGHT: 'light',
    DARK: 'dark',
}

// 涨跌红绿颜色
export const RAISE_FALL_COLOR_CONFIG = {
    RED_GREEN: 'red_green_trend',
    GREEN_RED: 'green_red_trend',
}

// 正则
export const REGULAR = {
    // 手机号
    MOBILE: /^1[3-9]\d{9}$/,
    // 邮箱
    EMAIL: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
    // 密码
    PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{8,16}$/,
    // 新密码 6 - 16 位任意字符
    NEW_PASSWORD: /^.{6,16}$/,
    // 兼容 15 - 18 位身份证号码
    // ID: /(^[1-9]\d{7}(0[1-9]|1[0-2])(0[1-9]|[1-2]\d|3[0-1])\d{3}$)|(^[1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[1-2]\d|3[0-1])\d{3}[\dXx]$)/,
    // 身份证号
    ID: /^[1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[1-2]\d|3[0-1])\d{3}[\dXx]$/,
    // 真实姓名
    REAL_NAME: /^[\u4e00-\u9fa5]+(?:·[\u4e00-\u9fa5]+)*$/,
    // 护照
    PASSPORT: /^([DEGHMPS]{1,2})\d{7,8}$/,
    // 短信验证码
    OTP: /^\d{6}$/,
    // 银行卡号
    BANK: /^\d{10,19}$/,
}

export const SOCKET_ACTIONS = {
    SUBSCRIBE: 'SUBSCRIBE',
    DELSUBSCRIBE: 'DELSUBSCRIBE',
}

export const SOCKET_EVENTS = {
    // 系统消息
    SYSTEM: 'system',
    // 预警
    WARNING: 'warning',
    // 强平
    CLOSE: 'WARN_CLOSE_MESSAGE',
    // 现货账户变动
    SPOT: 'getAccountInfo',
    // 合约订阅
    CONTRACT: 'contractSummaryPage',
    // 持仓订阅
    POSITION: 'position',
    // 委托订阅
    ENTRUST: 'delegation',
    // 成交明细订阅
    ENTRUST_DEAL: 'transaction',
    // 更新用户信息
    UPDATE_PROFILE: 'accountUpdate',
    // 股指更新
    UPDATE_INDEX: 'stockIndexUpdate'
}

export const FETCH_INTERVAL_TYPE = {
    REQUEST: 'request',
    PAGINATION: 'pagination',
}

export const JUMP_DICT = new Map([
    [ '1', '/deposit' ],
    [ '2', '/invite' ],
    [ '3', '/quotes/index' ],
    [ '4', '/contract/apply/type/1' ],
    [ '5', '/mission' ],
])

export const TIME_FORMAT = {
    FULL: 'YYYY-MM-DD HH:mm:ss',
    YMD: 'YYYY-MM-DD',
    Hm: 'HH:mm',
    MD: 'MM-DD',
    YM: 'YYYY-MM',
    Y: 'YYYY',
}
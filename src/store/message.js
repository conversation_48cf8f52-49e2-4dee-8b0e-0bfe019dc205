export const useMessageStore = defineStore('message', () => {
    const { res: $unreadCount, onRefresh: dispatch_refreshUnreadCount } = useRequest({
        // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E6%B6%88%E6%81%AF%E7%AE%A1%E7%90%86/getUnreadMessageCountUsingGET_1
        url: '/message/unreadCount',
        needLogin: true,
        cancellable: false,
        initialValues: 0,
    })

    return {
        $unreadCount,
        dispatch_refreshUnreadCount,
    }
})

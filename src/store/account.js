import _ from 'lodash'

import socket from '@/socket.js'
import { useIntervalFetch } from '@/hooks/index.js'

const initialSpot = {
    accountAmount: 0,
    assetAmount: 0,
    assetId: 0,
    todayWinAmount: 0,
    todayWinRate: 0,
    currency: '',
    freezeCash: 0,
    usableCash: 0,
    interestCash: 0,
}

// 当前选中类型
export const accountActiveTab = useSessionStorage('accountActive', ACCOUNT_TYPE.CONTRACT)

const refreshContractRoutes = [ 'account', ACCOUNT_TYPE.CONTRACT ]

// https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/app%E7%AB%AF-%E7%8E%B0%E8%B4%A7%E8%B4%A6%E6%88%B7/getAllAccountInfoUsingGET_1
const SPOT_API = '/account/getAccountInfo'

// 账户状态管理
export const useAccountStore = defineStore('account', () => {
        const { currentRoute } = useRouter()

        const { $isLogin } = storeToRefs(useProfileStore())

        // 现货账户
        const [
            {
                res: $spot,
                loading: $spotLoading,
                onRefresh: dispatch_refreshSpot,
            },
            {
                clearIntervalInstance: dispatch_clearSpotIntervalFetch,
            },
        ] = useIntervalFetch({
            url: SPOT_API,
            initialValues: {
                ...initialSpot,
            },
            cancellable: false,
            needLogin: true,
            sessionKey: 'spot',
        }, {
            config: {
                method: FETCH_INTERVAL_TYPE.REQUEST,
                callback: () => {
                    // return useMarketStatus(_.keys(STOCK_CONFIG)) && $isLogin.value
                    return $isLogin.value && ACCOUNT_ROUTES.includes(currentRoute.value.name)
                },
            },
        })

        // 合约账户列表
        const [
            {
                list: $contract,
                loading: $contractLoading,
                refreshLoading: $contractRefreshLoading,
                loadLoading: $contractLoadLoading,
                finished: $contractFinished,
                onRefresh: dispatch_refreshContact,
                onLoadMore: dispatch_loadMoreContact,
            },
            {
                clearIntervalInstance: dispatch_clearContractIntervalFetch,
            },
        ] = useContractList({
            params: {
                settlementStatus: 1,
            },
            cancellable: false,
            needLogin: true,
        })

        const $accountLoading = computed(() => $spotLoading.value || $contractLoading.value)

        const dispatch_refreshAccount = async () => {
            // 同时刷新现货和合约账户
            await Promise.all([
                dispatch_refreshSpot(),
                dispatch_refreshContact(),
            ])
        }

        if ($isLogin.value) {
            // 现货监听
            socket.on(SOCKET_EVENTS.SPOT, ({ data }) => {
                console.warn('可用余额', 'getAccountInfo')
                $spot.value = _.omit(data, '$contractAccountList')

                if (refreshContractRoutes.includes(currentRoute.value.name)) dispatch_refreshContact()
            })
        }

        const dispatch_resetAccount = () => {
            $spot.value = { ...initialSpot }
            $contract.value = []
        }

        return {
            $spot,
            $spotLoading,
            dispatch_refreshSpot,
            dispatch_clearSpotIntervalFetch,

            $contract,
            $contractLoading,
            $contractRefreshLoading,
            $contractLoadLoading,
            $contractFinished,
            dispatch_refreshContact,
            dispatch_loadMoreContact,
            dispatch_clearContractIntervalFetch,

            $accountLoading,
            dispatch_refreshAccount,
            dispatch_resetAccount,
        }
    },
    {
        key: 'use-account-store',
        persist: true,
        storage: sessionStorage,
    },
)

// 汇率状态管理
export const useRateStore = defineStore('rate', () => {
    // 汇率数据
    const { res, onRefresh: dispatch_refreshRate } = useRequest({
        // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E5%B8%82%E5%9C%BA%E6%B1%87%E7%8E%87/getRateInfoUsingGET_1
        url: '/rate/info',
        initialValues: [],
    })

    // 汇率选项配置
    const $rateConfig = computed(() => res.value?.map(e => ({
        ...e,
        text: e.currencyTarget,
        value: e.rate,
    })))

    // 根据传递币种返回对应汇率
    const dispatch_getCurrencyRateConfig = (currencyTarget) => {
        return _.find($rateConfig.value, { currencyTarget }) ?? { rate: 1 }
    }

    return {
        $rateConfig,
        dispatch_refreshRate,
        dispatch_getCurrencyRateConfig,
    }
})

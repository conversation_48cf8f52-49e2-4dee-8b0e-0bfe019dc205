export const useNewsStore = defineStore('news', () => {
    const {
        list: $news,
        initial: $newsInitial,
        loading: $newsLoading,
        refreshLoading: $newsRefreshLoading,
        loadLoading: $newsLoadLoading,
        finished: $newsFinished,
        onRefresh: dispatch_refreshNews,
        onLoadMore: dispatch_loadMoreNews,
    } = usePagination({
        // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/%E6%96%B0%E9%97%BB%E6%A8%A1%E5%9D%97/pageUsingGET_15
        url: '/news/page',
        cancellable: false,
    })

    return {
        $news,
        $newsInitial,
        $newsLoading,
        $newsRefreshLoading,
        $newsLoadLoading,
        $newsFinished,
        dispatch_refreshNews,
        dispatch_loadMoreNews,
    }
})

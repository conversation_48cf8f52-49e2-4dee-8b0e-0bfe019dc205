import socket from '@/socket.js'

const initialProfile = {
    auth: false,
    avatar: '',
    countryCode: '',
    email: '',
    fromType: 0,
    id: 0,
    inviteCode: '',
    isPayment: false,
    level: 0,
    mobile: '',
    nickname: '',
    pid: 0,
    profiles: '',
    score: 0,
    sex: 0,
    status: true,
    tradeStatus: 0,
    type: 0,
    realName: '',
}

// 个人资料状态管理
export const useProfileStore = defineStore('profile', () => {
    const $profile = useSessionStorage('profile', { ...initialProfile })

    const $isLogin = computed(() => !!$token.value)

    const dispatch_resetProfile = () => {
        sessionStorage.removeItem('token')
        $profile.value = { ...initialProfile }
    }

    const dispatch_updateProfile = async (newInfo) => {
        await api_put({
            // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/app%E7%AB%AF%E4%BC%9A%E5%91%98/updateUserInfoUsingPUT_1
            url: '/member/updateUserInfo',
            params: newInfo,
        })
    }

    const [ dispatch_refreshProfile, $profileLoading ] = useFetchLoading(async () => {
        console.warn('refreshProfile - oOo')
        $profile.value = await api_get({
            // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/app%E7%AB%AF%E4%BC%9A%E5%91%98/getUserInfoUsingGET_1
            url: '/member/getUserInfo',
        })
    })

    if ($isLogin.value) dispatch_refreshProfile()

    socket.on(SOCKET_EVENTS.UPDATE_PROFILE, dispatch_refreshProfile)

    return {
        $isLogin,
        $profile,
        $profileLoading,
        dispatch_resetProfile,
        dispatch_updateProfile,
        dispatch_refreshProfile,
    }
})

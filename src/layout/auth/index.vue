<template>
    <div class="auth-layout h-full">
        <div class="h-12 px-4 flex-between" data-aos="fade-up">
            <div class="w-5">
                <van-icon
                    class="text-xl"
                    name="arrow-left"
                    @click="onBack"
                />
            </div>

            <img
                class="w-[100px] h-8"
                alt="logo"
                :src="$logo"
            />

            <van-popover
                placement="bottom-end"
                :show-arrow="false"
            >
                <template #reference>
                    <c-icon prefix="auth" name="global"/>
                </template>

                <div
                    class="flex-middle gap-2 p-2 text-sm"
                    v-for="({ title, key, active }) in langActions"
                    @click="onSelect(key)"
                >
                    <c-icon :name="key"/>

                    <div :class="{ 'text-active': active }">
                        {{ title }}
                    </div>
                </div>
            </van-popover>
        </div>

        <div class="auth-container flex flex-col">
            <div class="h-[200px] flex-between pl-6 mb-auto">
                <div
                    class="text-2xl text-title leading-10 whitespace-pre-line"
                    data-aos="fade-up"
                    data-aos-delay="50"
                >
                    {{ $globalConfig.slogan?.[$i18n.locale] ?? '' }}
                </div>

                <img
                    src="./assets/cover.png"
                    alt="cover"
                    class="h-[200px]"
                    data-aos="fade-up"
                    data-aos-delay="100"
                >
            </div>

            <router-view/>
        </div>
    </div>
</template>

<script setup>
const { $globalConfig, $logo } = storeToRefs(useGlobalStore())

const { langActions, optionConfig } = useSystemSetting(SETTING_TYPES.LANGUAGE)

const { currentRoute, replace, back } = useRouter()

const onBack = () => {
    if (currentRoute.value.name === 'login') {
        replace('/home')
    } else {
        back()
    }
}

const onSelect = (lang) => {
    unref(optionConfig).onSelect(lang)
}

defineOptions({ name: 'authLayout' })
</script>

<style scoped>
.van-theme-light .auth-layout {
    background-image: url('assets/cover_light.png'),
    linear-gradient(270deg, #E1E8FF 0%, #E6E8F7 49.5%, #FFFFFF 100%);
    background-position: -48px -58px, 0 0;
    background-size: 280px, 100%;
    background-repeat: no-repeat;
}

.van-theme-dark .auth-layout {
    background-image: url('assets/cover_dark.png'),
    linear-gradient(270deg, #13161B 0%, #1B2028 49.5%, #20252E 100%);
    background-position: -48px -58px, 0 0;
    background-size: 280px, 100%;
    background-repeat: no-repeat;
}

.auth-container {
    /*
        Header 48
    */
    height: calc(100% - 48px);
}
</style>

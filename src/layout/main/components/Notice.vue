<template>
    <van-popup
        style="width: min(90%, 640px * .9); height: 75%; background-color: transparent;"
        :close-on-click-overlay="false"
        v-model:show="$noticePopup"
    >
        <div class="popup-main bg-bg rounded w-full flex">
            <div class="leading-10 w-1/4 text-center">
                <div
                    class="px-3 w-full truncate border-border not-last:border-b"
                    v-for="({ id, title }, index) in $notice.popup"
                    :key="id"
                    :class="{ 'text-primary': selected === index }"
                    @click="selected = index;"
                >
                    {{ title }}
                </div>
            </div>

            <div class="w-3/4 border-l border-border h-full overflow-auto">
                <div class="leading-10 text-title text-center font-semibold border-b border-border">
                    {{ current.title }}
                </div>

                <div class="p-2.5">
                    <img
                        class="w-full mb-4 block"
                        :src="current.imageUrl"
                        :alt="current.title"
                    >

                    <div class="text-paragraph" v-html="current.content"/>
                </div>
            </div>
        </div>

        <div class="mt-4 text-center" @click="$noticePopup = false">
            <van-icon
                class="block!"
                name="close"
                size="32"
            />
        </div>
    </van-popup>
</template>

<script setup>
const { $noticePopup, $notice } = storeToRefs(useNoticeStore())

const selected = ref(0),
    current = computed(() => $notice.value.popup[selected.value] ?? {
        content: '',
        endTime: '',
        id: 0,
        imageUrl: '',
        siteId: 0,
        startTime: '',
        status: false,
        title: '',
        type: 0,
    })

// 弹窗公告
defineOptions({ name: 'Notice' })
</script>

<style scoped>
.popup-main {
    height: calc(100% - 32px - 16px);
}
</style>

<template>
    <img
        class="max-w-14 max-h-14"
        alt="avatar"
        :src="source ?? src"
    >
</template>

<script setup>
const { avatar } = defineProps({
    avatar: {
        type: [ String, Number ],
        required: true,
        validator(value) {
            return !isNaN(+value)
        },
    },
    source: String,
})

const src = ref()

const onLoadAssets = async () => {
    src.value = new URL(`./assets/${avatar}.png`, import.meta.url).href
}

onLoadAssets()

// 头像组件
defineOptions({ name: 'C-Avatar' })
</script>

<style scoped>

</style>

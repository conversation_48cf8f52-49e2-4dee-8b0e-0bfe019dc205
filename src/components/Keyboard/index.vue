<template>
    <van-number-keyboard
        teleport="body"
        z-index="9999"
        maxlength="6"
        random-key-order
        :show="visibleValue"
        v-bind="$attrs"
        @blur="visibleValue = false"
        @close="visibleValue = false"
        v-model="modelValue"
    />
</template>

<script setup>
const emits = defineEmits([ 'finish' ])

const modelValue = defineModel({
    type: String,
    required: true,
})

const visibleValue = defineModel('visible', {
    type: Boolean,
})

watch(modelValue, (newVal) => {
    if (newVal.length === 6) {
        visibleValue.value = false

        emits('finish', newVal)
    }
})

defineOptions({ name: 'C-Keyboard' })
</script>

<style scoped>
</style>

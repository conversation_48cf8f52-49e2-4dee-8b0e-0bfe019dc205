<template>
    <div
        data-aos="fade-left"
        data-aos-delay="250"
        data-aos-anchor="#app"
        class="text-text text-xs text-center mt-2.5"
    >
        {{ tip || t('_tip') }}

        <span class="text-link" @click="onClick">{{ t('header.service') }}</span>
    </div>
</template>

<script setup>
defineProps({
    tip: String,
})

const { $globalConfig } = storeToRefs(useGlobalStore())

const onClick = () => {
    utils_link($globalConfig.value.service)
}

const { t } = useI18n({
    useScope: 'global',
    messages: {
        [LANGUAGE.zhCN]: {
            _tip: '遇到任何问题，请联系',
        },
        [LANGUAGE.zhHK]: {
            _tip: '遇到任何问题，请联系',
        },
        [LANGUAGE.enUS]: {
            _tip: 'If you encounter any problems, please contact',
        },
    },
})

defineOptions({ name: 'C-Service' })
</script>

<style scoped>

</style>

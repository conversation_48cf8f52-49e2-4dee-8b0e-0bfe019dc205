<template>
    <div class="c-divider">
        <render/>
    </div>
</template>

<script setup>
import { isVNode } from 'vue'
import _ from 'lodash'

import divider_light from './assets/divider_light.png'
import divider_dark from './assets/divider_dark.png'
import { THEME_CONFIG } from '@/config/index.js'

const isDark = useDark()

const { index, size, theme } = defineProps({
    index: Number,
    size: {
        type: Array,
        default: [ 1, 24 ],
    },
    theme: String,
})

const slots = useSlots()

const render = () => {
    const slotsArr = _.filter(slots.default(), isVNode),
        renderArr = []

    const insertDivider = (item, i, a) => {
        if (_.isSymbol(item.type) && item.children.length) {
            item.children.forEach(insertDivider)
        } else {
            renderArr.push(item)

            if (i !== a.length - 1 && i !== index) {
                const src = theme === THEME_CONFIG.DARK || isDark.value ? divider_dark : divider_light

                renderArr.push(
                    h(
                        'img',
                        {
                            src,
                            alt: `divider-${i}`,
                            style: {
                                width: `${size[0]}px`,
                                height: `${size[1]}px`,
                            },
                        },
                    ),
                )
            }
        }
    }

    slotsArr.forEach(insertDivider)

    return renderArr
}

defineOptions({ name: 'C-Divider' })
</script>

<style scoped>

</style>

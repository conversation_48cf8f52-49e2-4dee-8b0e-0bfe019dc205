<template>
    <svg
        class="c-icon"
        aria-hidden="true"
        :style="{
            color,
            width: `${size}px`,
            height: `${size}px`
        }"
    >
        <use :xlink:href="symbol"/>
    </svg>
</template>

<script setup>
import { $theme } from '@/store/index.js'

const { prefix, name, theme } = defineProps({
    name: {
        type: String,
        required: true,
    },
    prefix: {
        type: String,
        default: 'common',
    },
    color: String,
    size: {
        type: [ Number, String ],
        default: 20,
    },
    theme: Boolean,
})

const symbol = computed(() => `#icon-${prefix}-${name}${theme ? `_${unref($theme)}` : ''}`)

defineOptions({ name: 'C-Icon' })
</script>

<style scoped lang="scss">

</style>

<svg width="91" height="90" viewBox="0 0 91 90" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_ii_767_13097)">
<path d="M36.0952 52.1346L17.6878 54.7413C17.1431 54.8184 16.6484 55.0888 16.3112 55.4937L3.09244 71.3673C2.08267 72.5798 2.904 74.3045 4.54159 74.4103L17.3296 75.2362C18.1682 75.2903 18.8766 75.8028 19.1251 76.535L22.5131 86.5202C23.0104 87.9861 25.1157 88.2919 26.151 87.0487L39.2784 71.2849C39.5946 70.9052 39.7491 70.4351 39.7121 69.9651L38.4439 53.84C38.3579 52.7462 37.2839 51.9663 36.0952 52.1346Z" fill="url(#paint0_linear_767_13097)"/>
</g>
<g filter="url(#filter1_ii_767_13097)">
<path d="M53.852 50.1688L72.7998 53.4276C73.3188 53.5168 73.7866 53.7819 74.1094 54.1695L88.3466 71.266C89.3736 72.4992 88.505 74.2502 86.8355 74.3121L73.8917 74.7922C73.005 74.8251 72.2521 75.3703 72.0163 76.1503L68.9333 86.35C68.478 87.8561 66.3307 88.1995 65.2773 86.9346L51.1429 69.9615C50.8409 69.5988 50.6859 69.1531 50.7054 68.703L51.4326 51.925C51.4823 50.7793 52.6161 49.9563 53.852 50.1688Z" fill="url(#paint1_linear_767_13097)"/>
</g>
<path d="M44.4496 0L61.9453 9.45552L79.6863 18.5244L79.4411 37.0488L79.6863 55.5731L61.9453 64.642L44.4496 74.0975L26.9538 64.642L9.21286 55.5731L9.45802 37.0488L9.21286 18.5244L26.9538 9.45552L44.4496 0Z" fill="url(#paint2_linear_767_13097)"/>
<g filter="url(#filter2_ii_767_13097)">
<path d="M44.4496 4.51807L59.8117 12.8205L75.3891 20.7834L75.1738 37.0487L75.3891 53.314L59.8117 61.2769L44.4496 69.5793L29.0874 61.2769L13.51 53.314L13.7253 37.0487L13.51 20.7834L29.0874 12.8205L44.4496 4.51807Z" fill="url(#paint3_linear_767_13097)"/>
</g>
<g filter="url(#filter3_ii_767_13097)">
<path d="M44.5984 9.03613L57.8909 16.22L71.3697 23.1101L71.1834 37.1842L71.3697 51.2582L57.8909 58.1483L44.5984 65.3322L31.3059 58.1483L17.8271 51.2582L18.0133 37.1842L17.8271 23.1101L31.3059 16.22L44.5984 9.03613Z" fill="url(#paint4_linear_767_13097)"/>
</g>
<g filter="url(#filter4_d_767_13097)">
<path d="M34.112 37.0489L29.5615 22.5908H33.0295L36.1446 34.4995L39.1489 22.5908H42.4625L38.0665 37.0489H34.112Z" fill="url(#paint5_linear_767_13097)"/>
</g>
<g filter="url(#filter5_d_767_13097)">
<path d="M44.4473 37.0483V22.5908H47.4244V37.0489L44.4473 37.0483Z" fill="url(#paint6_linear_767_13097)"/>
</g>
<g filter="url(#filter6_d_767_13097)">
<path d="M55.5193 22.5908C59.7278 22.5908 62.31 24.095 62.31 27.25C62.31 30.5929 59.584 32.118 55.8055 32.118H53.9162V37.0489H50.4014V22.5908H55.5193ZM55.5193 29.8617C57.4565 29.8617 58.6753 29.2137 58.6753 27.25C58.6753 25.558 57.5277 24.8052 55.4713 24.8052H53.9169V29.8617H55.5193Z" fill="url(#paint7_linear_767_13097)"/>
</g>
<g filter="url(#filter7_d_767_13097)">
<path d="M42.7035 55.1213V54.6847L43.6566 54.4664C44.0927 54.3606 44.3668 54.2217 44.4789 54.0497C44.6035 53.8777 44.6658 53.5866 44.6658 53.1765V44.6231L45.0396 44.8017L40.5542 50.7355L40.5168 50.279H47.6934C47.9177 50.279 48.0984 50.2261 48.2354 50.1203C48.3849 50.0012 48.5033 49.8226 48.5905 49.5844L48.8895 48.7906H49.3194L48.5905 52.0056H39.3955V51.0332L46.4973 41.5669H47.1141V53.1765C47.1141 53.5866 47.1764 53.8777 47.301 54.0497C47.4256 54.2217 47.6997 54.3606 48.1233 54.4664L49.0764 54.6847V55.1213H42.7035Z" fill="url(#paint8_linear_767_13097)"/>
</g>
<defs>
<filter id="filter0_ii_767_13097" x="2.65039" y="48.1118" width="37.0674" height="43.7124" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-4"/>
<feGaussianBlur stdDeviation="5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.835294 0 0 0 0 0.643137 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_767_13097"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.835294 0 0 0 0 0.643137 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_767_13097" result="effect2_innerShadow_767_13097"/>
</filter>
<filter id="filter1_ii_767_13097" x="50.7041" y="46.1353" width="38.085" height="45.5752" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-4"/>
<feGaussianBlur stdDeviation="5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.835294 0 0 0 0 0.643137 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_767_13097"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.835294 0 0 0 0 0.643137 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_767_13097" result="effect2_innerShadow_767_13097"/>
</filter>
<filter id="filter2_ii_767_13097" x="13.5098" y="0.518066" width="61.8789" height="73.061" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-4"/>
<feGaussianBlur stdDeviation="5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.835294 0 0 0 0 0.643137 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_767_13097"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.835294 0 0 0 0 0.643137 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_767_13097" result="effect2_innerShadow_767_13097"/>
</filter>
<filter id="filter3_ii_767_13097" x="17.8271" y="4.03613" width="53.543" height="66.2959" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="5"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.24 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_767_13097"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-5"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.9 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_767_13097" result="effect2_innerShadow_767_13097"/>
</filter>
<filter id="filter4_d_767_13097" x="9.56152" y="12.5908" width="52.9014" height="54.458" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="10"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.7125 0 0 0 0 0 0 0 0 0 0 0 0 0 0.55 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_767_13097"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_767_13097" result="shape"/>
</filter>
<filter id="filter5_d_767_13097" x="24.4473" y="12.5908" width="42.9775" height="54.458" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="10"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.7125 0 0 0 0 0 0 0 0 0 0 0 0 0 0.55 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_767_13097"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_767_13097" result="shape"/>
</filter>
<filter id="filter6_d_767_13097" x="30.4014" y="12.5908" width="51.9082" height="54.458" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="10"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.7125 0 0 0 0 0 0 0 0 0 0 0 0 0 0.55 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_767_13097"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_767_13097" result="shape"/>
</filter>
<filter id="filter7_d_767_13097" x="19.3955" y="31.5669" width="49.9238" height="53.5542" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="10"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.7125 0 0 0 0 0 0 0 0 0 0 0 0 0 0.55 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_767_13097"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_767_13097" result="shape"/>
</filter>
<linearGradient id="paint0_linear_767_13097" x1="37.7394" y1="51.4475" x2="15.1797" y2="84.1212" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFBD14"/>
<stop offset="0.25" stop-color="#FAC95C"/>
<stop offset="0.5" stop-color="#F5D5A4"/>
<stop offset="0.625" stop-color="#FAC95C"/>
<stop offset="0.75" stop-color="#FFBD14"/>
</linearGradient>
<linearGradient id="paint1_linear_767_13097" x1="52.0721" y1="49.3918" x2="76.1458" y2="84.2583" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFC700"/>
<stop offset="0.25" stop-color="#FAC95C"/>
<stop offset="0.335" stop-color="#F5D5A4"/>
<stop offset="0.625" stop-color="#FAC95C"/>
<stop offset="0.75" stop-color="#FFC700"/>
</linearGradient>
<linearGradient id="paint2_linear_767_13097" x1="44.4496" y1="-5.83862e-07" x2="44.4496" y2="74.2088" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFE17A"/>
<stop offset="1" stop-color="#FE9902"/>
</linearGradient>
<linearGradient id="paint3_linear_767_13097" x1="44.4496" y1="4.51807" x2="44.4496" y2="69.5793" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFC700"/>
<stop offset="0.25" stop-color="#FAC95C"/>
<stop offset="0.335" stop-color="#F5D5A4"/>
<stop offset="0.625" stop-color="#FAC95C"/>
<stop offset="0.75" stop-color="#FFC700"/>
</linearGradient>
<linearGradient id="paint4_linear_767_13097" x1="44.5984" y1="9.03613" x2="44.5984" y2="65.3322" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF866B"/>
<stop offset="1" stop-color="#F66848"/>
</linearGradient>
<linearGradient id="paint5_linear_767_13097" x1="36.012" y1="22.5908" x2="36.012" y2="37.0489" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFEF64"/>
<stop offset="0.5" stop-color="#FFA215"/>
<stop offset="1" stop-color="#FFE456"/>
</linearGradient>
<linearGradient id="paint6_linear_767_13097" x1="45.9358" y1="22.5908" x2="45.9358" y2="37.0489" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFEF64"/>
<stop offset="0.5" stop-color="#FFA215"/>
<stop offset="1" stop-color="#FFE456"/>
</linearGradient>
<linearGradient id="paint7_linear_767_13097" x1="56.3557" y1="22.5908" x2="56.3557" y2="37.0489" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFEF64"/>
<stop offset="0.5" stop-color="#FFA215"/>
<stop offset="1" stop-color="#FFE456"/>
</linearGradient>
<linearGradient id="paint8_linear_767_13097" x1="44.3574" y1="41.5669" x2="44.3574" y2="55.1213" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFEF64"/>
<stop offset="0.5" stop-color="#FFA215"/>
<stop offset="1" stop-color="#FFE456"/>
</linearGradient>
</defs>
</svg>

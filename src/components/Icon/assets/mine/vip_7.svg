<svg width="91" height="91" viewBox="0 0 91 91" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_ii_767_13134)">
<path d="M36.0239 52.7328L17.6165 55.3394C17.0718 55.4165 16.5771 55.6869 16.2399 56.0919L3.02116 71.9654C2.01138 73.178 2.83271 74.9027 4.4703 75.0084L17.2583 75.8343C18.0969 75.8885 18.8053 76.4009 19.0538 77.1331L22.4418 87.1184C22.9391 88.5842 25.0444 88.8901 26.0797 87.6469L39.2072 71.883C39.5233 71.5034 39.6778 71.0332 39.6408 70.5633L38.3727 54.4382C38.2866 53.3443 37.2126 52.5645 36.0239 52.7328Z" fill="url(#paint0_linear_767_13134)"/>
</g>
<g filter="url(#filter1_ii_767_13134)">
<path d="M53.7807 50.767L72.7285 54.0257C73.2475 54.115 73.7153 54.38 74.0381 54.7677L88.2753 71.8641C89.3023 73.0974 88.4337 74.8484 86.7642 74.9103L73.8205 75.3904C72.9337 75.4232 72.1808 75.9685 71.945 76.7485L68.862 86.9481C68.4068 88.4543 66.2594 88.7977 65.206 87.5328L51.0716 70.5596C50.7696 70.197 50.6146 69.7512 50.6342 69.3011L51.3614 52.5232C51.411 51.3774 52.5448 50.5544 53.7807 50.767Z" fill="url(#paint1_linear_767_13134)"/>
</g>
<path d="M44.3783 0.598145L61.8741 10.0537L79.615 19.1225L79.3698 37.6469L79.615 56.1713L61.8741 65.2401L44.3783 74.6957L26.8825 65.2401L9.14157 56.1713L9.38673 37.6469L9.14157 19.1225L26.8825 10.0537L44.3783 0.598145Z" fill="url(#paint2_linear_767_13134)"/>
<g filter="url(#filter2_ii_767_13134)">
<path d="M44.3783 5.11621L59.7404 13.4186L75.3178 21.3815L75.1025 37.6468L75.3178 53.9121L59.7404 61.875L44.3783 70.1774L29.0161 61.875L13.4387 53.9121L13.654 37.6468L13.4387 21.3815L29.0161 13.4186L44.3783 5.11621Z" fill="url(#paint3_linear_767_13134)"/>
</g>
<g filter="url(#filter3_ii_767_13134)">
<path d="M44.5271 9.63428L57.8196 16.8182L71.2984 23.7083L71.1121 37.7823L71.2984 51.8563L57.8196 58.7464L44.5271 65.9303L31.2346 58.7464L17.7558 51.8563L17.9421 37.7823L17.7558 23.7083L31.2346 16.8182L44.5271 9.63428Z" fill="url(#paint4_linear_767_13134)"/>
</g>
<g filter="url(#filter4_d_767_13134)">
<path d="M34.0407 37.647L29.4902 23.189H32.9583L36.0733 35.0976L39.0776 23.189H42.3913L37.9952 37.647H34.0407Z" fill="url(#paint5_linear_767_13134)"/>
</g>
<g filter="url(#filter5_d_767_13134)">
<path d="M44.376 37.6464V23.189H47.3531V37.647L44.376 37.6464Z" fill="url(#paint6_linear_767_13134)"/>
</g>
<g filter="url(#filter6_d_767_13134)">
<path d="M55.448 23.189C59.6565 23.189 62.2387 24.6932 62.2387 27.8482C62.2387 31.191 59.5127 32.7162 55.7342 32.7162H53.8449V37.647H50.3301V23.189H55.448ZM55.448 30.4599C57.3852 30.4599 58.604 29.8119 58.604 27.8482C58.604 26.1561 57.4564 25.4034 55.4 25.4034H53.8456V30.4599H55.448Z" fill="url(#paint7_linear_767_13134)"/>
</g>
<g filter="url(#filter7_d_767_13134)">
<path d="M42.2406 55.7195L48.7184 44.5381H42.2842C42.0807 44.5381 41.8844 44.5984 41.6954 44.7191C41.5063 44.8263 41.3537 45.0006 41.2373 45.2419L40.8884 46.0463H40.3867L41.3028 42.165H50.3106V44.1962L45.4904 55.7195H42.2406Z" fill="url(#paint8_linear_767_13134)"/>
</g>
<defs>
<filter id="filter0_ii_767_13134" x="2.5791" y="48.71" width="37.0674" height="43.7124" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-4"/>
<feGaussianBlur stdDeviation="5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.835294 0 0 0 0 0.643137 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_767_13134"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.835294 0 0 0 0 0.643137 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_767_13134" result="effect2_innerShadow_767_13134"/>
</filter>
<filter id="filter1_ii_767_13134" x="50.6328" y="46.7334" width="38.085" height="45.5752" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-4"/>
<feGaussianBlur stdDeviation="5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.835294 0 0 0 0 0.643137 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_767_13134"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.835294 0 0 0 0 0.643137 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_767_13134" result="effect2_innerShadow_767_13134"/>
</filter>
<filter id="filter2_ii_767_13134" x="13.4385" y="1.11621" width="61.8789" height="73.061" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-4"/>
<feGaussianBlur stdDeviation="5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.835294 0 0 0 0 0.643137 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_767_13134"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.835294 0 0 0 0 0.643137 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_767_13134" result="effect2_innerShadow_767_13134"/>
</filter>
<filter id="filter3_ii_767_13134" x="17.7559" y="4.63428" width="53.543" height="66.2959" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="5"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.24 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_767_13134"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-5"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.9 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_767_13134" result="effect2_innerShadow_767_13134"/>
</filter>
<filter id="filter4_d_767_13134" x="9.49023" y="13.189" width="52.9014" height="54.458" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="10"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.7125 0 0 0 0 0 0 0 0 0 0 0 0 0 0.55 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_767_13134"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_767_13134" result="shape"/>
</filter>
<filter id="filter5_d_767_13134" x="24.376" y="13.189" width="42.9775" height="54.458" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="10"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.7125 0 0 0 0 0 0 0 0 0 0 0 0 0 0.55 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_767_13134"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_767_13134" result="shape"/>
</filter>
<filter id="filter6_d_767_13134" x="30.3301" y="13.189" width="51.9082" height="54.458" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="10"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.7125 0 0 0 0 0 0 0 0 0 0 0 0 0 0.55 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_767_13134"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_767_13134" result="shape"/>
</filter>
<filter id="filter7_d_767_13134" x="20.3867" y="32.165" width="49.9238" height="53.5542" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="10"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.7125 0 0 0 0 0 0 0 0 0 0 0 0 0 0.55 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_767_13134"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_767_13134" result="shape"/>
</filter>
<linearGradient id="paint0_linear_767_13134" x1="37.6681" y1="52.0456" x2="15.1084" y2="84.7193" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFBD14"/>
<stop offset="0.25" stop-color="#FAC95C"/>
<stop offset="0.5" stop-color="#F5D5A4"/>
<stop offset="0.625" stop-color="#FAC95C"/>
<stop offset="0.75" stop-color="#FFBD14"/>
</linearGradient>
<linearGradient id="paint1_linear_767_13134" x1="52.0008" y1="49.99" x2="76.0745" y2="84.8565" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFC700"/>
<stop offset="0.25" stop-color="#FAC95C"/>
<stop offset="0.335" stop-color="#F5D5A4"/>
<stop offset="0.625" stop-color="#FAC95C"/>
<stop offset="0.75" stop-color="#FFC700"/>
</linearGradient>
<linearGradient id="paint2_linear_767_13134" x1="44.3783" y1="0.598144" x2="44.3783" y2="74.807" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFE17A"/>
<stop offset="1" stop-color="#FE9902"/>
</linearGradient>
<linearGradient id="paint3_linear_767_13134" x1="44.3783" y1="5.11621" x2="44.3783" y2="70.1774" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFC700"/>
<stop offset="0.25" stop-color="#FAC95C"/>
<stop offset="0.335" stop-color="#F5D5A4"/>
<stop offset="0.625" stop-color="#FAC95C"/>
<stop offset="0.75" stop-color="#FFC700"/>
</linearGradient>
<linearGradient id="paint4_linear_767_13134" x1="44.5271" y1="9.63428" x2="44.5271" y2="65.9303" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF866B"/>
<stop offset="1" stop-color="#F66848"/>
</linearGradient>
<linearGradient id="paint5_linear_767_13134" x1="35.9407" y1="23.189" x2="35.9407" y2="37.647" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFEF64"/>
<stop offset="0.5" stop-color="#FFA215"/>
<stop offset="1" stop-color="#FFE456"/>
</linearGradient>
<linearGradient id="paint6_linear_767_13134" x1="45.8646" y1="23.189" x2="45.8646" y2="37.647" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFEF64"/>
<stop offset="0.5" stop-color="#FFA215"/>
<stop offset="1" stop-color="#FFE456"/>
</linearGradient>
<linearGradient id="paint7_linear_767_13134" x1="56.2844" y1="23.189" x2="56.2844" y2="37.647" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFEF64"/>
<stop offset="0.5" stop-color="#FFA215"/>
<stop offset="1" stop-color="#FFE456"/>
</linearGradient>
<linearGradient id="paint8_linear_767_13134" x1="45.3487" y1="42.165" x2="45.3487" y2="55.7195" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFEF64"/>
<stop offset="0.5" stop-color="#FFA215"/>
<stop offset="1" stop-color="#FFE456"/>
</linearGradient>
</defs>
</svg>

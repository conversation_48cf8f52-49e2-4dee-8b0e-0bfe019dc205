<svg width="91" height="90" viewBox="0 0 91 90" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_ii_767_13109)">
<path d="M35.7856 52.1346L17.3782 54.7413C16.8335 54.8184 16.3389 55.0888 16.0017 55.4937L2.78287 71.3673C1.7731 72.5798 2.59443 74.3045 4.23202 74.4103L17.02 75.2362C17.8586 75.2903 18.5671 75.8028 18.8155 76.535L22.2035 86.5202C22.7009 87.9861 24.8062 88.2919 25.8414 87.0487L38.9689 71.2849C39.285 70.9052 39.4395 70.4351 39.4026 69.9651L38.1344 53.84C38.0483 52.7462 36.9743 51.9663 35.7856 52.1346Z" fill="url(#paint0_linear_767_13109)"/>
</g>
<g filter="url(#filter1_ii_767_13109)">
<path d="M53.5425 50.1688L72.4902 53.4276C73.0092 53.5168 73.477 53.7819 73.7999 54.1695L88.037 71.266C89.064 72.4992 88.1954 74.2502 86.5259 74.3121L73.5822 74.7922C72.6954 74.8251 71.9425 75.3703 71.7067 76.1503L68.6237 86.35C68.1685 87.8561 66.0211 88.1995 64.9678 86.9346L50.8333 69.9615C50.5313 69.5988 50.3764 69.1531 50.3959 68.703L51.1231 51.925C51.1727 50.7793 52.3065 49.9563 53.5425 50.1688Z" fill="url(#paint1_linear_767_13109)"/>
</g>
<path d="M44.14 0L61.6358 9.45552L79.3767 18.5244L79.1315 37.0488L79.3767 55.5731L61.6358 64.642L44.14 74.0975L26.6442 64.642L8.90329 55.5731L9.14845 37.0488L8.90329 18.5244L26.6442 9.45552L44.14 0Z" fill="url(#paint2_linear_767_13109)"/>
<g filter="url(#filter2_ii_767_13109)">
<path d="M44.14 4.51807L59.5021 12.8205L75.0795 20.7834L74.8643 37.0487L75.0795 53.314L59.5021 61.2769L44.14 69.5793L28.7778 61.2769L13.2004 53.314L13.4157 37.0487L13.2004 20.7834L28.7778 12.8205L44.14 4.51807Z" fill="url(#paint3_linear_767_13109)"/>
</g>
<g filter="url(#filter3_ii_767_13109)">
<path d="M44.2888 9.03613L57.5813 16.22L71.0601 23.1101L70.8739 37.1842L71.0601 51.2582L57.5813 58.1483L44.2888 65.3322L30.9963 58.1483L17.5175 51.2582L17.7038 37.1842L17.5175 23.1101L30.9963 16.22L44.2888 9.03613Z" fill="url(#paint4_linear_767_13109)"/>
</g>
<g filter="url(#filter4_d_767_13109)">
<path d="M33.8024 37.0489L29.252 22.5908H32.72L35.835 34.4995L38.8393 22.5908H42.153L37.7569 37.0489H33.8024Z" fill="url(#paint5_linear_767_13109)"/>
</g>
<g filter="url(#filter5_d_767_13109)">
<path d="M44.1377 37.0483V22.5908H47.1149V37.0489L44.1377 37.0483Z" fill="url(#paint6_linear_767_13109)"/>
</g>
<g filter="url(#filter6_d_767_13109)">
<path d="M55.2097 22.5908C59.4182 22.5908 62.0004 24.095 62.0004 27.25C62.0004 30.5929 59.2744 32.118 55.4959 32.118H53.6067V37.0489H50.0918V22.5908H55.2097ZM55.2097 29.8617C57.1469 29.8617 58.3657 29.2137 58.3657 27.25C58.3657 25.558 57.2181 24.8052 55.1618 24.8052H53.6074V29.8617H55.2097Z" fill="url(#paint7_linear_767_13109)"/>
</g>
<g filter="url(#filter7_d_767_13109)">
<path d="M43.5628 55.1213C42.8195 55.1213 42.1281 55.0259 41.4889 54.8352C40.8496 54.6445 40.3366 54.3703 39.9501 54.0126C39.5784 53.655 39.3926 53.2318 39.3926 52.743C39.3926 52.3616 39.5264 52.0814 39.794 51.9026C40.0765 51.7118 40.3812 51.6165 40.7083 51.6165C41.1395 51.6165 41.4666 51.7059 41.6896 51.8847C41.9274 52.0516 42.0464 52.2543 42.0464 52.4927C42.0464 52.7073 41.9943 52.8742 41.8903 52.9934C41.8011 53.1126 41.697 53.2139 41.5781 53.2974C41.4889 53.3689 41.4071 53.4345 41.3327 53.4941C41.2733 53.5537 41.2435 53.6312 41.2435 53.7265C41.2435 53.9411 41.4294 54.1199 41.8011 54.263C42.1876 54.3941 42.6485 54.4597 43.1837 54.4597C43.7041 54.4597 44.1873 54.3464 44.6333 54.1199C45.0942 53.8815 45.4584 53.5298 45.726 53.0649C46.0085 52.5881 46.1497 51.998 46.1497 51.2946C46.1497 50.7224 45.9713 50.2455 45.6145 49.8641C45.2726 49.4707 44.8042 49.1786 44.2096 48.9879C43.6149 48.7852 42.9533 48.6839 42.2248 48.6839C42.0612 48.6839 41.9126 48.6898 41.7788 48.7017C41.6598 48.7017 41.5037 48.7137 41.3104 48.7375L41.0428 48.6123L41.868 42.6756H47.1979C47.406 42.6756 47.5918 42.6279 47.7554 42.5325C47.9338 42.4371 48.075 42.2941 48.1791 42.1033L48.4244 41.5669H49.0265L48.3129 44.571H42.5593L42.2025 47.0566C42.3809 47.0328 42.5519 47.0209 42.7154 47.0209C42.8938 47.0209 43.0648 47.0209 43.2283 47.0209C44.3731 47.0209 45.3989 47.1818 46.3058 47.5037C47.2276 47.8136 47.9561 48.2726 48.4913 48.8806C49.0414 49.4766 49.3164 50.1979 49.3164 51.0443C49.3164 51.7357 49.1083 52.3973 48.692 53.0291C48.2757 53.649 47.6439 54.1557 46.7965 54.5491C45.949 54.9306 44.8711 55.1213 43.5628 55.1213Z" fill="url(#paint8_linear_767_13109)"/>
</g>
<defs>
<filter id="filter0_ii_767_13109" x="2.34082" y="48.1118" width="37.0674" height="43.7124" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-4"/>
<feGaussianBlur stdDeviation="5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.835294 0 0 0 0 0.643137 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_767_13109"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.835294 0 0 0 0 0.643137 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_767_13109" result="effect2_innerShadow_767_13109"/>
</filter>
<filter id="filter1_ii_767_13109" x="50.3945" y="46.1353" width="38.085" height="45.5752" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-4"/>
<feGaussianBlur stdDeviation="5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.835294 0 0 0 0 0.643137 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_767_13109"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.835294 0 0 0 0 0.643137 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_767_13109" result="effect2_innerShadow_767_13109"/>
</filter>
<filter id="filter2_ii_767_13109" x="13.2002" y="0.518066" width="61.8789" height="73.061" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-4"/>
<feGaussianBlur stdDeviation="5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.835294 0 0 0 0 0.643137 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_767_13109"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.835294 0 0 0 0 0.643137 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_767_13109" result="effect2_innerShadow_767_13109"/>
</filter>
<filter id="filter3_ii_767_13109" x="17.5176" y="4.03613" width="53.543" height="66.2959" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="5"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.24 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_767_13109"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-5"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.9 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_767_13109" result="effect2_innerShadow_767_13109"/>
</filter>
<filter id="filter4_d_767_13109" x="9.25195" y="12.5908" width="52.9014" height="54.458" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="10"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.7125 0 0 0 0 0 0 0 0 0 0 0 0 0 0.55 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_767_13109"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_767_13109" result="shape"/>
</filter>
<filter id="filter5_d_767_13109" x="24.1377" y="12.5908" width="42.9775" height="54.458" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="10"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.7125 0 0 0 0 0 0 0 0 0 0 0 0 0 0.55 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_767_13109"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_767_13109" result="shape"/>
</filter>
<filter id="filter6_d_767_13109" x="30.0918" y="12.5908" width="51.9082" height="54.458" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="10"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.7125 0 0 0 0 0 0 0 0 0 0 0 0 0 0.55 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_767_13109"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_767_13109" result="shape"/>
</filter>
<filter id="filter7_d_767_13109" x="19.3926" y="31.5669" width="49.9238" height="53.5542" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="10"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.7125 0 0 0 0 0 0 0 0 0 0 0 0 0 0.55 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_767_13109"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_767_13109" result="shape"/>
</filter>
<linearGradient id="paint0_linear_767_13109" x1="37.4299" y1="51.4475" x2="14.8702" y2="84.1212" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFBD14"/>
<stop offset="0.25" stop-color="#FAC95C"/>
<stop offset="0.5" stop-color="#F5D5A4"/>
<stop offset="0.625" stop-color="#FAC95C"/>
<stop offset="0.75" stop-color="#FFBD14"/>
</linearGradient>
<linearGradient id="paint1_linear_767_13109" x1="51.7625" y1="49.3918" x2="75.8362" y2="84.2583" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFC700"/>
<stop offset="0.25" stop-color="#FAC95C"/>
<stop offset="0.335" stop-color="#F5D5A4"/>
<stop offset="0.625" stop-color="#FAC95C"/>
<stop offset="0.75" stop-color="#FFC700"/>
</linearGradient>
<linearGradient id="paint2_linear_767_13109" x1="44.14" y1="-5.83862e-07" x2="44.14" y2="74.2088" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFE17A"/>
<stop offset="1" stop-color="#FE9902"/>
</linearGradient>
<linearGradient id="paint3_linear_767_13109" x1="44.14" y1="4.51807" x2="44.14" y2="69.5793" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFC700"/>
<stop offset="0.25" stop-color="#FAC95C"/>
<stop offset="0.335" stop-color="#F5D5A4"/>
<stop offset="0.625" stop-color="#FAC95C"/>
<stop offset="0.75" stop-color="#FFC700"/>
</linearGradient>
<linearGradient id="paint4_linear_767_13109" x1="44.2888" y1="9.03613" x2="44.2888" y2="65.3322" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF866B"/>
<stop offset="1" stop-color="#F66848"/>
</linearGradient>
<linearGradient id="paint5_linear_767_13109" x1="35.7025" y1="22.5908" x2="35.7025" y2="37.0489" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFEF64"/>
<stop offset="0.5" stop-color="#FFA215"/>
<stop offset="1" stop-color="#FFE456"/>
</linearGradient>
<linearGradient id="paint6_linear_767_13109" x1="45.6263" y1="22.5908" x2="45.6263" y2="37.0489" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFEF64"/>
<stop offset="0.5" stop-color="#FFA215"/>
<stop offset="1" stop-color="#FFE456"/>
</linearGradient>
<linearGradient id="paint7_linear_767_13109" x1="56.0461" y1="22.5908" x2="56.0461" y2="37.0489" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFEF64"/>
<stop offset="0.5" stop-color="#FFA215"/>
<stop offset="1" stop-color="#FFE456"/>
</linearGradient>
<linearGradient id="paint8_linear_767_13109" x1="44.3545" y1="41.5669" x2="44.3545" y2="55.1213" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFEF64"/>
<stop offset="0.5" stop-color="#FFA215"/>
<stop offset="1" stop-color="#FFE456"/>
</linearGradient>
</defs>
</svg>

<svg width="91" height="90" viewBox="0 0 91 90" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_ii_767_13061)">
<path d="M36.0239 52.1346L17.6165 54.7413C17.0718 54.8184 16.5771 55.0888 16.2399 55.4937L3.02116 71.3673C2.01138 72.5798 2.83271 74.3045 4.4703 74.4103L17.2583 75.2362C18.0969 75.2903 18.8053 75.8028 19.0538 76.535L22.4418 86.5202C22.9391 87.9861 25.0444 88.2919 26.0797 87.0487L39.2072 71.2849C39.5233 70.9052 39.6778 70.4351 39.6408 69.9651L38.3727 53.84C38.2866 52.7462 37.2126 51.9663 36.0239 52.1346Z" fill="url(#paint0_linear_767_13061)"/>
</g>
<g filter="url(#filter1_ii_767_13061)">
<path d="M53.7807 50.1688L72.7285 53.4276C73.2475 53.5168 73.7153 53.7819 74.0381 54.1695L88.2753 71.266C89.3023 72.4992 88.4337 74.2502 86.7642 74.3121L73.8205 74.7922C72.9337 74.8251 72.1808 75.3703 71.945 76.1503L68.862 86.35C68.4068 87.8561 66.2594 88.1995 65.206 86.9346L51.0716 69.9615C50.7696 69.5988 50.6146 69.1531 50.6342 68.703L51.3614 51.925C51.411 50.7793 52.5448 49.9563 53.7807 50.1688Z" fill="url(#paint1_linear_767_13061)"/>
</g>
<path d="M44.3783 0L61.8741 9.45552L79.615 18.5244L79.3698 37.0488L79.615 55.5731L61.8741 64.642L44.3783 74.0975L26.8825 64.642L9.14157 55.5731L9.38673 37.0488L9.14157 18.5244L26.8825 9.45552L44.3783 0Z" fill="url(#paint2_linear_767_13061)"/>
<g filter="url(#filter2_ii_767_13061)">
<path d="M44.3783 4.51807L59.7404 12.8205L75.3178 20.7834L75.1025 37.0487L75.3178 53.314L59.7404 61.2769L44.3783 69.5793L29.0161 61.2769L13.4387 53.314L13.654 37.0487L13.4387 20.7834L29.0161 12.8205L44.3783 4.51807Z" fill="url(#paint3_linear_767_13061)"/>
</g>
<g filter="url(#filter3_ii_767_13061)">
<path d="M44.5271 9.03613L57.8196 16.22L71.2984 23.1101L71.1121 37.1842L71.2984 51.2582L57.8196 58.1483L44.5271 65.3322L31.2346 58.1483L17.7558 51.2582L17.9421 37.1842L17.7558 23.1101L31.2346 16.22L44.5271 9.03613Z" fill="url(#paint4_linear_767_13061)"/>
</g>
<g filter="url(#filter4_d_767_13061)">
<path d="M34.0407 37.0489L29.4902 22.5908H32.9583L36.0733 34.4995L39.0776 22.5908H42.3913L37.9952 37.0489H34.0407Z" fill="url(#paint5_linear_767_13061)"/>
</g>
<g filter="url(#filter5_d_767_13061)">
<path d="M44.376 37.0483V22.5908H47.3531V37.0489L44.376 37.0483Z" fill="url(#paint6_linear_767_13061)"/>
</g>
<g filter="url(#filter6_d_767_13061)">
<path d="M55.448 22.5908C59.6565 22.5908 62.2387 24.095 62.2387 27.25C62.2387 30.5929 59.5127 32.118 55.7342 32.118H53.8449V37.0489H50.3301V22.5908H55.448ZM55.448 29.8617C57.3852 29.8617 58.604 29.2137 58.604 27.25C58.604 25.558 57.4564 24.8052 55.4 24.8052H53.8456V29.8617H55.448Z" fill="url(#paint7_linear_767_13061)"/>
</g>
<g filter="url(#filter7_d_767_13061)">
<path d="M39.3936 55.1213V54.6828L40.8396 54.4635C41.2744 54.3971 41.5957 54.3174 41.8036 54.2243C42.0305 54.1313 42.1817 54.0051 42.2573 53.8456C42.3329 53.6729 42.3707 53.4469 42.3707 53.1679V43.4207C42.3707 43.2346 42.3046 43.0951 42.1722 43.0021C42.0399 42.909 41.7942 42.8692 41.435 42.8825L39.3936 42.9423V42.3642L46.3403 41.5669V53.1679C46.3403 53.4469 46.3781 53.6729 46.4537 53.8456C46.5293 54.0051 46.6711 54.1313 46.879 54.2243C47.1058 54.3174 47.4366 54.3971 47.8714 54.4635L49.3174 54.6828V55.1213H39.3936Z" fill="url(#paint8_linear_767_13061)"/>
</g>
<defs>
<filter id="filter0_ii_767_13061" x="2.5791" y="48.1118" width="37.0674" height="43.7124" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-4"/>
<feGaussianBlur stdDeviation="5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.835294 0 0 0 0 0.643137 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_767_13061"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.835294 0 0 0 0 0.643137 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_767_13061" result="effect2_innerShadow_767_13061"/>
</filter>
<filter id="filter1_ii_767_13061" x="50.6328" y="46.1353" width="38.085" height="45.5752" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-4"/>
<feGaussianBlur stdDeviation="5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.835294 0 0 0 0 0.643137 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_767_13061"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.835294 0 0 0 0 0.643137 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_767_13061" result="effect2_innerShadow_767_13061"/>
</filter>
<filter id="filter2_ii_767_13061" x="13.4385" y="0.518066" width="61.8789" height="73.061" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-4"/>
<feGaussianBlur stdDeviation="5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.835294 0 0 0 0 0.643137 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_767_13061"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.835294 0 0 0 0 0.643137 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_767_13061" result="effect2_innerShadow_767_13061"/>
</filter>
<filter id="filter3_ii_767_13061" x="17.7559" y="4.03613" width="53.543" height="66.2959" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="5"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.24 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_767_13061"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-5"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.9 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_767_13061" result="effect2_innerShadow_767_13061"/>
</filter>
<filter id="filter4_d_767_13061" x="9.49023" y="12.5908" width="52.9014" height="54.458" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="10"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.7125 0 0 0 0 0 0 0 0 0 0 0 0 0 0.55 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_767_13061"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_767_13061" result="shape"/>
</filter>
<filter id="filter5_d_767_13061" x="24.376" y="12.5908" width="42.9775" height="54.458" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="10"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.7125 0 0 0 0 0 0 0 0 0 0 0 0 0 0.55 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_767_13061"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_767_13061" result="shape"/>
</filter>
<filter id="filter6_d_767_13061" x="30.3301" y="12.5908" width="51.9082" height="54.458" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="10"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.7125 0 0 0 0 0 0 0 0 0 0 0 0 0 0.55 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_767_13061"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_767_13061" result="shape"/>
</filter>
<filter id="filter7_d_767_13061" x="19.3936" y="31.5669" width="49.9238" height="53.5542" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="10"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.7125 0 0 0 0 0 0 0 0 0 0 0 0 0 0.55 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_767_13061"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_767_13061" result="shape"/>
</filter>
<linearGradient id="paint0_linear_767_13061" x1="37.6681" y1="51.4475" x2="15.1084" y2="84.1212" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFBD14"/>
<stop offset="0.25" stop-color="#FAC95C"/>
<stop offset="0.5" stop-color="#F5D5A4"/>
<stop offset="0.625" stop-color="#FAC95C"/>
<stop offset="0.75" stop-color="#FFBD14"/>
</linearGradient>
<linearGradient id="paint1_linear_767_13061" x1="52.0008" y1="49.3918" x2="76.0745" y2="84.2583" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFC700"/>
<stop offset="0.25" stop-color="#FAC95C"/>
<stop offset="0.335" stop-color="#F5D5A4"/>
<stop offset="0.625" stop-color="#FAC95C"/>
<stop offset="0.75" stop-color="#FFC700"/>
</linearGradient>
<linearGradient id="paint2_linear_767_13061" x1="44.3783" y1="-5.83862e-07" x2="44.3783" y2="74.2088" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFE17A"/>
<stop offset="1" stop-color="#FE9902"/>
</linearGradient>
<linearGradient id="paint3_linear_767_13061" x1="44.3783" y1="4.51807" x2="44.3783" y2="69.5793" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFC700"/>
<stop offset="0.25" stop-color="#FAC95C"/>
<stop offset="0.335" stop-color="#F5D5A4"/>
<stop offset="0.625" stop-color="#FAC95C"/>
<stop offset="0.75" stop-color="#FFC700"/>
</linearGradient>
<linearGradient id="paint4_linear_767_13061" x1="44.5271" y1="9.03613" x2="44.5271" y2="65.3322" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF866B"/>
<stop offset="1" stop-color="#F66848"/>
</linearGradient>
<linearGradient id="paint5_linear_767_13061" x1="35.9407" y1="22.5908" x2="35.9407" y2="37.0489" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFEF64"/>
<stop offset="0.5" stop-color="#FFA215"/>
<stop offset="1" stop-color="#FFE456"/>
</linearGradient>
<linearGradient id="paint6_linear_767_13061" x1="45.8646" y1="22.5908" x2="45.8646" y2="37.0489" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFEF64"/>
<stop offset="0.5" stop-color="#FFA215"/>
<stop offset="1" stop-color="#FFE456"/>
</linearGradient>
<linearGradient id="paint7_linear_767_13061" x1="56.2844" y1="22.5908" x2="56.2844" y2="37.0489" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFEF64"/>
<stop offset="0.5" stop-color="#FFA215"/>
<stop offset="1" stop-color="#FFE456"/>
</linearGradient>
<linearGradient id="paint8_linear_767_13061" x1="44.3555" y1="41.5669" x2="44.3555" y2="55.1213" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFEF64"/>
<stop offset="0.5" stop-color="#FFA215"/>
<stop offset="1" stop-color="#FFE456"/>
</linearGradient>
</defs>
</svg>

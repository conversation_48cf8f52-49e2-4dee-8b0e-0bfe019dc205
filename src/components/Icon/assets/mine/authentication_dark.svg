<svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_211_823)">
<g filter="url(#filter0_d_211_823)">
<path d="M18.9855 4.55177C16.1201 -1.51726 13.2518 -1.51726 10.3865 4.55177C3.69867 3.99952 2.26453 6.48322 6.08696 12C2.26164 17.5168 3.69577 20.0005 10.3836 19.4482C13.2489 25.5173 16.1172 25.5173 18.9826 19.4482C25.6732 20.0005 27.1074 17.5168 23.285 12C27.1074 6.48322 25.6732 4.00241 18.9855 4.55177Z" fill="#4366DE"/>
</g>
<g filter="url(#filter1_d_211_823)">
<path d="M11.2808 11.4018L13.4018 13.5228L18.705 8.21953C18.8458 8.0789 19.0366 7.99993 19.2355 8C19.4345 8.00007 19.6253 8.07917 19.7659 8.2199C19.9065 8.36063 19.9855 8.55147 19.9854 8.75042C19.9854 8.94937 19.9063 9.14014 19.7655 9.28078L13.932 15.1143C13.7914 15.2549 13.6007 15.3339 13.4018 15.3339C13.2029 15.3339 13.0122 15.2549 12.8715 15.1143L10.2195 12.4623C10.0789 12.3215 9.99993 12.1307 10 11.9318C10.0001 11.7328 10.0792 11.542 10.2199 11.4014C10.3606 11.2608 10.5515 11.1818 10.7504 11.1819C10.9494 11.1819 11.1401 11.261 11.2808 11.4018Z" fill="#FF4953"/>
<path d="M11.2808 11.4018L13.4018 13.5228L18.705 8.21953C18.8458 8.0789 19.0366 7.99993 19.2355 8C19.4345 8.00007 19.6253 8.07917 19.7659 8.2199C19.9065 8.36063 19.9855 8.55147 19.9854 8.75042C19.9854 8.94937 19.9063 9.14014 19.7655 9.28078L13.932 15.1143C13.7914 15.2549 13.6007 15.3339 13.4018 15.3339C13.2029 15.3339 13.0122 15.2549 12.8715 15.1143L10.2195 12.4623C10.0789 12.3215 9.99993 12.1307 10 11.9318C10.0001 11.7328 10.0792 11.542 10.2199 11.4014C10.3606 11.2608 10.5515 11.1818 10.7504 11.1819C10.9494 11.1819 11.1401 11.261 11.2808 11.4018Z" fill="#F4F8FF"/>
</g>
</g>
<defs>
<filter id="filter0_d_211_823" x="-0.6" y="-1.6" width="30.5698" height="33.2" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3"/>
<feGaussianBlur stdDeviation="2.3"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0912902 0 0 0 0 0.0777329 0 0 0 0 0.272054 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_211_823"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_211_823" result="shape"/>
</filter>
<filter id="filter1_d_211_823" x="9" y="8" width="11.9855" height="9.33398" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.372827 0 0 0 0 0.480685 0 0 0 0 0.725439 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_211_823"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_211_823" result="shape"/>
</filter>
<clipPath id="clip0_211_823">
<rect width="30" height="30" fill="white"/>
</clipPath>
</defs>
</svg>

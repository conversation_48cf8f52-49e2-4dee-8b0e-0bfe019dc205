<template>
    <v-chart autoresize :option/>
</template>

<script setup>
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Candlestick<PERSON>hart } from 'echarts/charts'
import {
    <PERSON>rid<PERSON>omponent,
    <PERSON>lt<PERSON><PERSON>omponent,
    LegendComponent,
    MarkLineComponent,
    DataZoomComponent,
} from 'echarts/components'
import VChart from 'vue-echarts'

use([
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>hart,
    <PERSON><PERSON>hart,
    <PERSON><PERSON>hart,
    Candlestick<PERSON>hart,
    GridComponent,
    Tooltip<PERSON>omponent,
    LegendComponent,
    Mark<PERSON>ineComponent,
    DataZoomComponent,
])

defineProps({
    option: {
        type: Object,
        required: true,
    },
})

defineOptions({ name: 'C-Echarts' })
</script>

<style scoped>

</style>

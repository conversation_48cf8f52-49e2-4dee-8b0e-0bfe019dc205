<template>
    <div
        data-aos="fade-left"
        data-aos-anchor="#app"
    >
        <van-button
            block
            type="primary"
            native-type="submit"
            :size
            :disabled
            :loading
            @click="emits('click')"
        >
            <slot>
                {{ $t('form.submit') }}
            </slot>
        </van-button>
    </div>
</template>

<script setup>
defineProps({
    disabled: Boolean,
    loading: Boolean,
    size: String,
})

const emits = defineEmits([ 'click' ])

defineOptions({ name: 'Submit' })
</script>

<style scoped>

</style>

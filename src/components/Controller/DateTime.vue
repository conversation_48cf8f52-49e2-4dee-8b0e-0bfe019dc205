<template>
    <Controller
        v-bind="_.pick(attrs, [ 'class', 'required', 'disabled' ])"
    >
        <van-picker-group
            title=""
            :tabs="['选择日期', '选择时间']"
        >
            <van-date-picker
                v-model="currentDate"
                :min-date="minDate"
                :max-date="maxDate"
            />
            <van-time-picker v-model="currentTime"/>
        </van-picker-group>
    </Controller>
</template>

<script setup>
import _ from 'lodash'
import dayjs from 'dayjs'

import Controller from './Controller.vue'

const modelValue = ref({
    type: Array,
    required: true,
})

const attrs = useAttrs()

const minDate = dayjs().subtract(6, 'months'),
    maxDate = dayjs()

const currentDate = ref([]),
    currentTime = ref([])

defineOptions({ name: 'DateTime' })
</script>

<style scoped>

</style>

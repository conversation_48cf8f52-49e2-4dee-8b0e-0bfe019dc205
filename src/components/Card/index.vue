<template>
    <div
        data-aos="fade-left"
        data-aos-anchor="#app"
        class="c-card bg-bg rounded-lg custom-shadow overflow-hidden"
        :class="{ noPadding, 'pt-4': !title && !$slots.title && !noPadding }"
    >
        <div
            data-aos="fade-left"
            data-aos-delay="50"
            data-aos-anchor="#app"
            v-if="title || to || extra || arrow || $slots.extra || $slots.title"
            class="h-10  flex-between gap-2 border-border"
            :class="{ 'border-b': divider, 'px-4': noPadding }"
        >
            <slot name="title">
                <span v-if="title" class="flex-1 w-1 truncate text-title text-sm font-semibold">{{ title }}</span>
            </slot>

            <slot name="extra">
                <span v-if="extra" class="text-text text-xs ml-auto">{{ extra }}</span>
            </slot>

            <van-icon
                v-if="arrow"
                name="arrow"
                class="text-link ml-2"
                @click="to && $router.push(to)"
            />
        </div>

        <slot/>
    </div>
</template>

<script setup>
defineProps({
    title: String,
    extra: String,
    to: [ String, Object ],
    noPadding: Boolean,
    arrow: Boolean,
    // 分割线
    divider: Boolean,
})

// 卡片组件
defineOptions({ name: 'C-Card' })
</script>

<style scoped>
:where(.c-card:not(.noPadding)) {
    padding-right: 16px;
    padding-bottom: 16px;
    padding-left: 16px;
}
</style>

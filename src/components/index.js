import { allowMultipleToast, setToastDefaultOptions, Cell, CellGroup } from 'vant'

import Icon from './Icon/index.vue'
import ImageBox from './ImageBox/index.vue'
import Controller from './Controller/Controller.vue'
import Input from './Controller/Input.vue'
import Select from './Controller/Select.vue'
import Submit from './Controller/Submit.vue'
import Amount from './Amount/index.vue'
import Table from './Table/index.vue'
import Header from './Header/index.vue'
import Avatar from './Avatar/index.vue'
import Picker from './Picker/index.vue'
import DescriptionGroup from './Description/DescriptionGroup.vue'
import Description from './Description/index.vue'
import Card from './Card/index.vue'
import Record from './Record/index.vue'
import Collapse from './Collapse/index.vue'
import Echarts from './Echarts/index.vue'
import Service from './Service/index.vue'
import RateCurrency from './RateCurrency/index.vue'
import Keyboard from './Keyboard/index.vue'

allowMultipleToast(true)
setToastDefaultOptions('loading', { duration: 0 })

Cell.props.size = {
    default: 'large',
}
CellGroup.props.border = {
    default: false,
}

export default {
    install(Vue) {
        Vue.component('c-icon', Icon)
        Vue.component('c-image-box', ImageBox)
        Vue.component('c-controller', Controller)
        Vue.component('c-input', Input)
        Vue.component('c-select', Select)
        Vue.component('c-submit', Submit)
        Vue.component('c-amount', Amount)
        Vue.component('c-table', Table)
        Vue.component('c-header', Header)
        Vue.component('c-avatar', Avatar)
        Vue.component('c-picker', Picker)
        Vue.component('c-description-group', DescriptionGroup)
        Vue.component('c-description', Description)
        Vue.component('c-card', Card)
        Vue.component('c-record', Record)
        Vue.component('c-collapse', Collapse)
        Vue.component('c-echarts', Echarts)
        Vue.component('c-service', Service)
        Vue.component('c-rate-currency', RateCurrency)
        Vue.component('c-keyboard', Keyboard)
    },
}

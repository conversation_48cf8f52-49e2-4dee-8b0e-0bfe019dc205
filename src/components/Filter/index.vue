<template>
    <van-popup
        class="p-4"
        position="top"
        v-model:show="modelValue"
    >
        <slot/>

        <div class="flex-middle gap-4 mt-4">
            <van-button
                size="small"
                block
                @click="emits('reset')"
            >
                {{ $t('form.reset') }}
            </van-button>
            <van-button
                size="small"
                type="primary"
                block
                @click="modelValue = false; emits('confirm')"
            >
                {{ $t('form.query') }}
            </van-button>
        </div>
    </van-popup>
</template>

<script setup>
const modelValue = defineModel({
    type: Boolean,
    required: true,
})

const emits = defineEmits([ 'reset', 'confirm' ])

defineOptions({ name: 'C-Filter' })
</script>

<style scoped>

</style>

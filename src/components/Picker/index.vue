<template>
    <div class="c-picker">
        <div v-if="title" class="text-title mb-3 flex-between">
            <span>{{ title }}</span>

            <slot name="extra" />
        </div>

        <van-skeleton class="w-full" :loading>
            <template #template>
                <div class="w-full grid grid-cols-4 gap-4">
                    <van-skeleton-paragraph
                        v-for="n in 4"
                        :key="n"
                        class="rounded-md"
                        style="margin: 0; height: 32px;"
                    />
                </div>
            </template>

            <div class="flex flex-wrap gap-2">
                <render/>
            </div>
        </van-skeleton>
    </div>
</template>

<script setup>
import { Fragment } from 'vue'

const { options, fieldNames, clickable, disabled } = defineProps({
    title: String,
    loading: Boolean,
    options: {
        type: Array,
        required: true,
    },
    fieldNames: {
        type: Object,
        default: {
            label: 'label',
            value: 'value',
        },
    },
    // 纯点击模式
    clickable: Boolean,
    columns: {
        type: Number,
        default: 4,
    },
    disabled: Boolean,
})

const emits = defineEmits([ 'change' ])

const modelValue = defineModel('modelValue', {
    type: [ String, Number, Array ],
})
const indexValue = defineModel('index', {
    type: Number,
})

const onChange = ({ label, value, index }, option) => {
    if (disabled) return

    if (modelValue.value !== undefined) modelValue.value = value
    if (indexValue.value !== undefined) indexValue.value = index

    emits('change', value, { label, value, index, ...option })
}

const slots = useSlots()

const render = () => {
    const isSlot = 'default' in slots

    return options.map((option, index) => {
        const label = option[fieldNames.label],
            value = option[fieldNames.value]

        const active = (indexValue.value !== undefined && indexValue.value === index) ||
            (modelValue.value !== undefined && modelValue.value === value)

        return isSlot
            ? h(
                Fragment,
                slots.default({
                    option,
                    index,
                    active,
                }),
            )
            : h(
                'div',
                {
                    class: [
                        'c-picker__item h-8 flex-center text-sm rounded-md px-3 border',
                        !clickable && active ? 'bg-active' : 'border-text',
                        { 'active:bg-active': clickable && !disabled },
                    ],
                    onClick: () => {
                        onChange({ label, value, index }, option)
                    },
                },
                label,
            )
    })
}

defineOptions({ name: 'C-Picker' })
</script>

<style scoped>
:deep(.c-picker__item) {
    /* Gap 8px */
    min-width: calc((100% - (8px * 3)) / 4);
}
</style>

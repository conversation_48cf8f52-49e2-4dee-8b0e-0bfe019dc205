<template>
    <container>
        <c-amount
            v-bind="_.omit(attrs, 'class')"
            :amount="_amount ?? amount"
            :currency="currencyValue"
        />
    </container>
</template>

<script setup>
import { Fragment } from 'vue'
import { Popover, Icon } from 'vant'
import currencyJs from 'currency.js'
import _ from 'lodash'

const { amount, disabled } = defineProps({
    amount: {
        type: Number,
        required: true,
    },
    disabled: Boolean,
})

const emits = defineEmits([ 'change' ]),
    attrs = useAttrs()

const { $rateConfig } = storeToRefs(useRateStore())

const currencyValue = defineModel('currency', {
        type: String,
        default: CURRENCY.CNY,
    }),
    rateValue = defineModel('rate', {
        type: Number,
        default: 1,
    })

const _amount = computed(() => disabled ? amount : currencyJs(amount).multiply(rateValue.value))

const onSelect = (item) => {
    const { currencyTarget, rate } = item

    currencyValue.value = currencyTarget
    rateValue.value = rate

    emits('change', item)
}

const container = (props, { slots }) => {
    const slot = slots.default()

    return disabled
        ? h(
            Fragment,
            slot,
        )
        : h(
            Popover,
            {
                placement: 'bottom-end',
                actions: $rateConfig.value.map(e => ({
                    ...e,
                    color: e.currencyTarget === currencyValue.value ? 'var(--primary)' : '',
                })),
                onSelect,
            },
            {
                reference: () => h(
                    'div',
                    {
                        class: attrs.class,
                    },
                    [
                        slot,
                        h(
                            Icon,
                            {
                                name: 'play',
                                class: 'rotate-90',
                            },
                        ),
                    ],
                ),
            },
        )
}

defineOptions({ name: 'C-Rate-Currency', inheritAttrs: false })
</script>

<style scoped>

</style>

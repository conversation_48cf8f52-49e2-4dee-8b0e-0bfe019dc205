<template>
    <div class="c-collapse" @click="onToggle">
        <div
            class="overflow-hidden transition-all duration-300 ease-in-out"
            :style="{
                height: `${modelValue ? unfold : fold}px`
            }"
        >
            <slot/>
        </div>

        <div v-if="arrow" class="h-6 flex-center">
            <van-icon name="play" :class="[ modelValue ? '-rotate-90' : 'rotate-90' ]"/>

            <span v-if="tip" class="text-xs ml-1.5">{{ actionText }}</span>
        </div>
    </div>
</template>

<script setup>
import { computed } from 'vue'

const { disabled } = defineProps({
    fold: {
        type: Number,
        default: '0px',
    },
    unfold: {
        type: Number,
        default: 'max-content',
    },
    tip: {
        type: Boolean,
        default: true,
    },
    arrow: {
        type: Boolean,
        default: true,
    },
    disabled: Boolean,
})

const modelValue = defineModel({
    type: Boolean,
    default: false,
})

const actionText = computed(() => modelValue.value ? t('_unfold') : t('_fold'))

const onToggle = () => {
    if (!disabled) modelValue.value = !modelValue.value
}

const { t } = useI18n({
    useScope: 'local',
    messages: {
        [LANGUAGE.zhCN]: {
            _fold: '点击展开',
            _unfold: '点击收起',
        },
        [LANGUAGE.zhHK]: {
            _fold: '点击展开',
            _unfold: '点击收起',
        },
        [LANGUAGE.enUS]: {
            _fold: 'Click to expand',
            _unfold: 'Click to collapse',
        },
    },
})

// 对外暴露开关控制
defineExpose({ onToggle })

defineOptions({ name: 'C-Collapse' })
</script>

<style scoped>
</style>

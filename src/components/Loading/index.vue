<template>
    <template v-if="$slots.default">
        <div class="c-loading relative size-full">
            <slot/>

            <LoadingDom/>
        </div>
    </template>

    <LoadingDom v-else/>
</template>

<script setup>
const { loading } = defineProps({
    loading: <PERSON>olean,
})

const LoadingDom = () => {
    return loading
        ? h(
            'div',
            {
                class: 'absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2',
            },
            h(
                'img',
                {
                    src: '/loading.gif',
                    alt: 'loading',
                },
            ),
        )
        : null
}

defineOptions({ name: 'C-Loading' })
</script>

<style scoped>

</style>

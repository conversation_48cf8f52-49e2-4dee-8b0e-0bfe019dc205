<template>
    <div
        class="c-header h-(--header-height) px-4 flex-between overflow-hidden"
        :class="{ 'bg-bg text-title': !transparent, 'text-white': transparent }"
    >
        <div class="c-header__left" data-aos="fade-left">
            <slot name="left">
                <van-icon
                    v-if="!hideBack"
                    name="arrow-left"
                    @click="!disabled && $router.back()"
                />
            </slot>
        </div>

        <div
            class="c-header__center flex-1 text-center truncate"
            data-aos="fade-left"
            data-aos-delay="50"
        >
            <slot>
                <span>{{ title }}</span>
            </slot>
        </div>

        <div
            class="c-header__right"
            data-aos="fade-left"
            data-aos-delay="100"
        >
            <slot name="right"/>
        </div>
    </div>
</template>

<script setup>
defineProps({
    hideBack: <PERSON>olean,
    title: String,
    transparent: Boolean,
    disabled: Boolean,
})

defineOptions({ name: 'C-Header' })
</script>

<style scoped>
</style>

<template>
    <div v-bind="$attrs">
        <slot/>

        <van-icon
            v-if="tip"
            class="text-link align-super"
            :size="12"
            name="question-o"
            @click="onShowTip"
        />
    </div>

    <van-popup
        teleport="body"
        position="bottom"
        round
        class="p-4 text-title"
        v-model:show="show"
    >
        {{ tip }}
    </van-popup>
</template>

<script setup>
const { tip } = defineProps({
    tip: String,
})

const show = ref(false)

const onShowTip = () => {
    if (tip) show.value = true
}

defineOptions({ name: 'C-Tip', inheritAttrs: false })
</script>

<style scoped>

</style>

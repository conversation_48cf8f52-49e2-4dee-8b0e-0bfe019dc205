<template>
    <van-pull-refresh
        v-model="refreshLoading"
        :disabled="disabled || !onRefresh"
        @refresh="onRefresh"
        v-bind="refreshProps"
    >
        <div class="h-full">
            <van-list
                v-model:loading="loadLoading"
                :immediate-check="false"
                :finished
                :finished-text="t('common.finished')"
                :disabled="disabled || !onLoadMore"
                @load="onLoadMore"
                v-bind="listProps"
            >
                <slot>
                    <van-empty
                        :description="loadLoading ? t('common.loading') : t('common.empty')"
                        data-aos="zoom-in"
                        data-aos-anchor="#app"
                    />
                </slot>
            </van-list>
        </div>
    </van-pull-refresh>
</template>

<script setup>
defineProps({
    disabled: Boolean,
    finished: Boolean,
    onRefresh: Function,
    onLoadMore: Function,
    refreshProps: Object,
    listProps: Object,
})

const refreshLoading = defineModel('refreshLoading', Boolean),
    loadLoading = defineModel('loadLoading', Boolean)

const { t } = useI18n()

defineOptions({ name: 'C-Record' })
</script>

<style scoped>
</style>

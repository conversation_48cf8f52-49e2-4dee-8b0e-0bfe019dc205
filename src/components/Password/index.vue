<template>
    <van-popup
        teleport="body"
        round
        style="background-color: transparent;"
        v-model:show="visibleValue"
    >
        <div class="w-[90vw] h-max bg-white p-4">
            <div class="text-center text-title leading-10 font-semibold text-xl">
                {{ t('auth.password_financial') }}
            </div>

            <!-- 密码输入框 -->
            <van-password-input
                autofocus
                :gutter="10"
                :value="modelValue"
            />
        </div>

        <!-- 数字键盘 -->
        <c-keyboard
            v-model="modelValue"
            v-model:visible="visibleValue"
            @finish="emits('finish', $event)"
        />
    </van-popup>
</template>

<script setup>
const emits = defineEmits([ 'finish' ])

const modelValue = defineModel({
    type: String,
    required: true,
})

const visibleValue = defineModel('visible', {
    type: Boolean,
    required: true,
})

const { t } = useI18n()

defineOptions({ name: 'C-Password' })
</script>

<style scoped>
.van-password-input {
    --van-password-input-background: var(--controller_bg);
    --van-password-input-margin: 0;
}
</style>
